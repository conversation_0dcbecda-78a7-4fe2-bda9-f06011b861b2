#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Trace分析器包
提供trace文件解析和分析功能
"""

import logging


def setup_logging(level=logging.INFO, format_str=None):
    """
    设置日志格式
    
    Args:
        level: 日志级别
        format_str: 日志格式字符串
    """
    if format_str is None:
        format_str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    logging.basicConfig(
        level=level,
        format=format_str,
        datefmt='%Y-%m-%d %H:%M:%S'
    )


# 导入主要类
from .core.trace_parser import TraceParser
from .core.kernel_analyzer import KernelAnalyzer
from .core.models import TraceEvent, EventGroup, KernelProfile

__all__ = [
    'TraceParser',
    'KernelAnalyzer', 
    'TraceEvent',
    'EventGroup',
    'KernelProfile',
    'setup_logging'
]
