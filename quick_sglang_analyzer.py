#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SGLang Profiler 快速分析脚本
提供JSON文件的快速概览和关键指标分析
"""

import json
import argparse
import sys
from pathlib import Path
from collections import Counter, defaultdict
from typing import Dict, List, Any


def quick_analyze(json_file: str) -> Dict[str, Any]:
    """快速分析JSON文件并返回关键指标"""
    
    print(f"🔍 分析文件: {json_file}")
    
    # 加载数据
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    events = data.get('traceEvents', [])
    base_time = data.get('baseTimeNanoseconds', 0)
    
    print(f"📊 基本信息:")
    print(f"   - 总事件数: {len(events)}")
    print(f"   - 基准时间: {base_time}")
    
    # 分类统计
    complete_events = [e for e in events if e.get('ph') == 'X' and e.get('dur', 0) > 0]
    categories = Counter(e.get('cat', 'unknown') for e in complete_events)
    
    print(f"\\n📋 事件分类统计:")
    for cat, count in categories.most_common(10):
        print(f"   - {cat}: {count} 个事件")
    
    # 时间分析
    if complete_events:
        total_duration = sum(e.get('dur', 0) for e in complete_events)
        start_time = min(e.get('ts', 0) for e in complete_events)
        end_time = max(e.get('ts', 0) + e.get('dur', 0) for e in complete_events)
        span_duration = end_time - start_time
        
        print(f"\\n⏱️  时间分析:")
        print(f"   - 总累计时间: {total_duration/1000:.2f} ms")
        print(f"   - 实际执行跨度: {span_duration/1000:.2f} ms")
        print(f"   - 平均事件耗时: {total_duration/len(complete_events)/1000:.4f} ms")
    
    # Python函数分析
    python_events = [e for e in events if e.get('cat') == 'python_function']
    if python_events:
        python_functions = Counter(e.get('name', 'unknown') for e in python_events)
        python_total_time = sum(e.get('dur', 0) for e in python_events)
        
        print(f"\\n🐍 Python函数分析:")
        print(f"   - Python事件总数: {len(python_events)}")
        print(f"   - 唯一函数数: {len(python_functions)}")
        print(f"   - Python总耗时: {python_total_time/1000:.2f} ms")
        
        print(f"   - 最频繁调用的函数 (Top 5):")
        for func, count in python_functions.most_common(5):
            func_name = func[:60] + "..." if len(func) > 60 else func
            print(f"     * {func_name}: {count} 次")
        
        # 最耗时的Python函数
        python_time_stats = defaultdict(lambda: {'count': 0, 'total_time': 0})
        for event in python_events:
            name = event.get('name', 'unknown')
            duration = event.get('dur', 0)
            python_time_stats[name]['count'] += 1
            python_time_stats[name]['total_time'] += duration
        
        top_time_functions = sorted(
            python_time_stats.items(),
            key=lambda x: x[1]['total_time'],
            reverse=True
        )[:5]
        
        print(f"   - 最耗时的函数 (Top 5):")
        for func, stats in top_time_functions:
            func_name = func[:60] + "..." if len(func) > 60 else func
            print(f"     * {func_name}: {stats['total_time']/1000:.2f} ms ({stats['count']} 次)")
    
    # CUDA/GPU分析
    cuda_events = []
    kernel_events = []
    
    for event in events:
        name = event.get('name', '').lower()
        cat = event.get('cat', '').lower()
        
        if ('cuda' in name or 'cuda' in cat or 
            'kernel' in name or 'kernel' in cat or
            'gpu' in name or 'gpu' in cat):
            cuda_events.append(event)
            
            if 'kernel' in name or cat == 'kernel':
                kernel_events.append(event)
    
    if cuda_events:
        cuda_total_time = sum(e.get('dur', 0) for e in cuda_events if e.get('dur', 0) > 0)
        kernel_total_time = sum(e.get('dur', 0) for e in kernel_events if e.get('dur', 0) > 0)
        
        print(f"\\n🚀 GPU/CUDA分析:")
        print(f"   - CUDA事件总数: {len(cuda_events)}")
        print(f"   - 内核事件数: {len(kernel_events)}")
        print(f"   - CUDA总耗时: {cuda_total_time/1000:.2f} ms")
        print(f"   - 内核总耗时: {kernel_total_time/1000:.2f} ms")
        
        if kernel_events:
            kernel_names = Counter(e.get('name', 'unknown') for e in kernel_events)
            print(f"   - 最频繁的内核 (Top 5):")
            for kernel, count in kernel_names.most_common(5):
                kernel_name = kernel[:60] + "..." if len(kernel) > 60 else kernel
                print(f"     * {kernel_name}: {count} 次")
    
    # 检测可能的阶段
    if python_events:
        # 简单的阶段检测：基于函数名关键词
        forward_events = [e for e in python_events if 'forward' in e.get('name', '').lower()]
        attention_events = [e for e in python_events if 'attention' in e.get('name', '').lower()]
        moe_events = [e for e in python_events if 'moe' in e.get('name', '').lower()]
        embedding_events = [e for e in python_events if 'embedding' in e.get('name', '').lower()]
        linear_events = [e for e in python_events if 'linear' in e.get('name', '').lower()]
        
        print(f"\\n🔍 模型组件分析:")
        if forward_events:
            print(f"   - Forward Pass 事件: {len(forward_events)} 个")
        if attention_events:
            print(f"   - Attention 事件: {len(attention_events)} 个")
        if moe_events:
            print(f"   - MoE (专家混合) 事件: {len(moe_events)} 个")
        if embedding_events:
            print(f"   - Embedding 事件: {len(embedding_events)} 个")
        if linear_events:
            print(f"   - Linear 事件: {len(linear_events)} 个")
    
    # 性能建议
    print(f"\\n💡 性能洞察:")
    
    if complete_events:
        gpu_ratio = (cuda_total_time / total_duration * 100) if cuda_events and total_duration > 0 else 0
        print(f"   - GPU耗时占比: {gpu_ratio:.1f}%")
        
        if gpu_ratio < 30:
            print(f"   ⚠️  GPU利用率较低，可能存在CPU瓶颈")
        elif gpu_ratio > 80:
            print(f"   ✅ GPU利用率高，计算密集型负载")
        
        # 检查内存操作
        memory_events = [e for e in events if any(mem in e.get('name', '').lower() 
                        for mem in ['memcpy', 'memset', 'malloc', 'free'])]
        if memory_events:
            memory_time = sum(e.get('dur', 0) for e in memory_events)
            memory_ratio = (memory_time / total_duration * 100) if total_duration > 0 else 0
            print(f"   - 内存操作耗时占比: {memory_ratio:.1f}%")
            
            if memory_ratio > 10:
                print(f"   ⚠️  内存操作较多，考虑优化数据传输")
    
    # 推荐下一步分析
    print(f"\\n📈 建议进一步分析:")
    print(f"   1. 使用完整分析脚本获取详细报告")
    print(f"   2. 查看最耗时的函数调用栈")
    print(f"   3. 分析GPU内核的具体执行模式")
    if moe_events:
        print(f"   4. 特别关注MoE (专家混合模型) 的性能优化")
    
    return {
        'total_events': len(events),
        'complete_events': len(complete_events),
        'python_events': len(python_events),
        'cuda_events': len(cuda_events),
        'kernel_events': len(kernel_events),
        'total_duration_ms': total_duration/1000 if complete_events else 0,
        'span_duration_ms': span_duration/1000 if complete_events else 0
    }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="SGLang Profiler JSON文件快速分析")
    parser.add_argument("json_file", help="SGLang profiler JSON文件路径")
    
    args = parser.parse_args()
    
    # 检查文件
    if not Path(args.json_file).exists():
        print(f"❌ 错误: 文件不存在 - {args.json_file}")
        sys.exit(1)
    
    try:
        print("🎯 SGLang Profiler 快速分析工具")
        print("=" * 50)
        
        results = quick_analyze(args.json_file)
        
        print("\\n" + "=" * 50)
        print("✅ 快速分析完成！")
        
        # 提供进一步分析的命令建议
        print(f"\\n🚀 运行完整分析:")
        print(f"python /workspace/enhanced_sglang_analyzer.py '{args.json_file}' -o results -v")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
