#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SGLang Profiler JSON分析脚本
专门分析SGLang生成的profiler J<PERSON><PERSON>文件
"""

import json
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict, Counter
import csv
import sys

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SGLangProfilerAnalyzer:
    """SGLang Profiler分析器"""
    
    def __init__(self, json_file: str):
        """
        初始化分析器
        
        Args:
            json_file: JSON文件路径
        """
        self.json_file = Path(json_file)
        self.trace_data = None
        self.events = []
        self.base_time = 0
        
    def load_data(self) -> None:
        """加载JSON数据"""
        logger.info(f"正在加载文件: {self.json_file}")
        
        try:
            with open(self.json_file, 'r', encoding='utf-8') as f:
                self.trace_data = json.load(f)
            
            # 获取基准时间
            self.base_time = self.trace_data.get('baseTimeNanoseconds', 0)
            
            # 获取事件列表
            self.events = self.trace_data.get('traceEvents', [])
            
            logger.info(f"成功加载 {len(self.events)} 个事件")
            logger.info(f"基准时间: {self.base_time}")
            
        except Exception as e:
            logger.error(f"加载文件失败: {e}")
            raise
    
    def analyze_python_functions(self) -> Dict[str, Any]:
        """分析Python函数调用"""
        logger.info("分析Python函数调用...")
        
        python_events = []
        for event in self.events:
            if event.get('cat') == 'python_function' and event.get('ph') == 'X':
                python_events.append(event)
        
        logger.info(f"找到 {len(python_events)} 个Python函数事件")
        
        # 分析函数调用统计
        function_stats = defaultdict(lambda: {
            'count': 0,
            'total_duration': 0.0,
            'min_duration': float('inf'),
            'max_duration': 0.0,
            'avg_duration': 0.0
        })
        
        for event in python_events:
            name = event.get('name', 'unknown')
            duration = event.get('dur', 0)
            
            stats = function_stats[name]
            stats['count'] += 1
            stats['total_duration'] += duration
            stats['min_duration'] = min(stats['min_duration'], duration)
            stats['max_duration'] = max(stats['max_duration'], duration)
        
        # 计算平均值
        for stats in function_stats.values():
            if stats['count'] > 0:
                stats['avg_duration'] = stats['total_duration'] / stats['count']
        
        return {
            'total_events': len(python_events),
            'function_stats': dict(function_stats)
        }
    
    def analyze_cuda_events(self) -> Dict[str, Any]:
        """分析CUDA相关事件"""
        logger.info("分析CUDA事件...")
        
        cuda_events = []
        for event in self.events:
            name = event.get('name', '').lower()
            cat = event.get('cat', '').lower()
            
            if ('cuda' in name or 'cuda' in cat or 
                'kernel' in name or 'kernel' in cat or
                'gpu' in name or 'gpu' in cat):
                cuda_events.append(event)
        
        logger.info(f"找到 {len(cuda_events)} 个CUDA相关事件")
        
        # 分类CUDA事件
        kernel_events = []
        runtime_events = []
        memory_events = []
        
        for event in cuda_events:
            name = event.get('name', '').lower()
            if 'kernel' in name:
                kernel_events.append(event)
            elif any(kw in name for kw in ['memcpy', 'memset', 'malloc', 'free']):
                memory_events.append(event)
            else:
                runtime_events.append(event)
        
        return {
            'total_cuda_events': len(cuda_events),
            'kernel_events': len(kernel_events),
            'runtime_events': len(runtime_events),
            'memory_events': len(memory_events),
            'kernel_details': self._analyze_kernel_events(kernel_events),
            'runtime_details': self._analyze_runtime_events(runtime_events)
        }
    
    def _analyze_kernel_events(self, kernel_events: List[Dict]) -> Dict[str, Any]:
        """分析内核事件详情"""
        if not kernel_events:
            return {}
        
        kernel_stats = defaultdict(lambda: {
            'count': 0,
            'total_duration': 0.0,
            'avg_duration': 0.0
        })
        
        for event in kernel_events:
            name = event.get('name', 'unknown')
            duration = event.get('dur', 0)
            
            stats = kernel_stats[name]
            stats['count'] += 1
            stats['total_duration'] += duration
        
        # 计算平均值并排序
        for stats in kernel_stats.values():
            if stats['count'] > 0:
                stats['avg_duration'] = stats['total_duration'] / stats['count']
        
        # 按总耗时排序
        sorted_kernels = sorted(
            kernel_stats.items(),
            key=lambda x: x[1]['total_duration'],
            reverse=True
        )
        
        return {
            'unique_kernels': len(kernel_stats),
            'top_kernels': sorted_kernels[:10]
        }
    
    def _analyze_runtime_events(self, runtime_events: List[Dict]) -> Dict[str, Any]:
        """分析运行时事件详情"""
        if not runtime_events:
            return {}
        
        runtime_stats = Counter()
        total_duration = 0
        
        for event in runtime_events:
            name = event.get('name', 'unknown')
            duration = event.get('dur', 0)
            
            runtime_stats[name] += 1
            total_duration += duration
        
        return {
            'unique_operations': len(runtime_stats),
            'total_duration': total_duration,
            'top_operations': runtime_stats.most_common(10)
        }
    
    def analyze_torch_operations(self) -> Dict[str, Any]:
        """分析PyTorch操作"""
        logger.info("分析PyTorch操作...")
        
        torch_events = []
        for event in self.events:
            name = event.get('name', '').lower()
            if ('torch' in name or 
                any(op in name for op in ['linear', 'matmul', 'embedding', 'attention', 'softmax'])):
                torch_events.append(event)
        
        logger.info(f"找到 {len(torch_events)} 个PyTorch操作事件")
        
        # 统计操作类型
        operation_stats = defaultdict(lambda: {
            'count': 0,
            'total_duration': 0.0,
            'avg_duration': 0.0
        })
        
        for event in torch_events:
            name = event.get('name', 'unknown')
            duration = event.get('dur', 0)
            
            # 提取操作类型
            op_type = self._extract_operation_type(name)
            
            stats = operation_stats[op_type]
            stats['count'] += 1
            stats['total_duration'] += duration
        
        # 计算平均值
        for stats in operation_stats.values():
            if stats['count'] > 0:
                stats['avg_duration'] = stats['total_duration'] / stats['count']
        
        return {
            'total_torch_events': len(torch_events),
            'operation_stats': dict(operation_stats)
        }
    
    def _extract_operation_type(self, name: str) -> str:
        """从事件名称中提取操作类型"""
        name_lower = name.lower()
        
        # 常见的操作类型映射
        operation_map = {
            'linear': 'Linear',
            'matmul': 'MatMul',
            'embedding': 'Embedding',
            'attention': 'Attention',
            'softmax': 'Softmax',
            'layernorm': 'LayerNorm',
            'dropout': 'Dropout',
            'relu': 'ReLU',
            'gelu': 'GELU',
            'conv': 'Convolution',
            'pool': 'Pooling'
        }
        
        for key, op_type in operation_map.items():
            if key in name_lower:
                return op_type
        
        return name  # 如果没有匹配，返回原名称
    
    def analyze_timing_patterns(self) -> Dict[str, Any]:
        """分析时间模式和阶段"""
        logger.info("分析时间模式...")
        
        # 按时间排序事件
        sorted_events = sorted(
            [e for e in self.events if e.get('ph') == 'X' and e.get('dur', 0) > 0],
            key=lambda x: x.get('ts', 0)
        )
        
        if not sorted_events:
            return {}
        
        # 计算时间范围
        start_time = sorted_events[0]['ts']
        end_time = max(e['ts'] + e.get('dur', 0) for e in sorted_events)
        total_duration = end_time - start_time
        
        # 分析时间分布
        time_buckets = defaultdict(list)
        bucket_count = 10
        bucket_size = total_duration / bucket_count
        
        for event in sorted_events:
            bucket_idx = int((event['ts'] - start_time) / bucket_size)
            bucket_idx = min(bucket_idx, bucket_count - 1)
            time_buckets[bucket_idx].append(event)
        
        # 分析每个时间段的特征
        bucket_analysis = {}
        for bucket_idx, events in time_buckets.items():
            if events:
                bucket_start = start_time + bucket_idx * bucket_size
                bucket_end = bucket_start + bucket_size
                
                # 统计事件类型
                event_types = Counter(e.get('cat', 'unknown') for e in events)
                total_events = len(events)
                avg_duration = sum(e.get('dur', 0) for e in events) / total_events
                
                bucket_analysis[bucket_idx] = {
                    'time_range': (bucket_start, bucket_end),
                    'event_count': total_events,
                    'avg_duration': avg_duration,
                    'event_types': dict(event_types)
                }
        
        return {
            'total_duration': total_duration,
            'event_count': len(sorted_events),
            'time_bucket_analysis': bucket_analysis
        }
    
    def detect_phases(self) -> Dict[str, Any]:
        """检测执行阶段（如prefill/decode）"""
        logger.info("检测执行阶段...")
        
        # 根据事件模式检测阶段
        python_events = [e for e in self.events if e.get('cat') == 'python_function']
        
        if not python_events:
            return {'phases': []}
        
        # 按时间排序
        python_events.sort(key=lambda x: x.get('ts', 0))
        
        # 分析调用密度变化来检测阶段转换
        phases = []
        current_phase = {
            'start_time': python_events[0]['ts'],
            'events': [],
            'phase_type': 'unknown'
        }
        
        window_size = len(python_events) // 20  # 5%的窗口
        if window_size < 10:
            window_size = 10
        
        for i, event in enumerate(python_events):
            current_phase['events'].append(event)
            
            # 每个窗口检查一次是否需要切换阶段
            if i > 0 and i % window_size == 0:
                # 分析当前窗口的特征
                window_events = python_events[max(0, i-window_size):i]
                phase_type = self._classify_phase(window_events)
                
                if phase_type != current_phase['phase_type'] and current_phase['events']:
                    # 结束当前阶段
                    current_phase['end_time'] = python_events[i-1]['ts']
                    current_phase['duration'] = current_phase['end_time'] - current_phase['start_time']
                    phases.append(current_phase)
                    
                    # 开始新阶段
                    current_phase = {
                        'start_time': event['ts'],
                        'events': [event],
                        'phase_type': phase_type
                    }
                else:
                    current_phase['phase_type'] = phase_type
        
        # 添加最后一个阶段
        if current_phase['events']:
            current_phase['end_time'] = python_events[-1]['ts']
            current_phase['duration'] = current_phase['end_time'] - current_phase['start_time']
            phases.append(current_phase)
        
        return {
            'phases': phases,
            'phase_count': len(phases)
        }
    
    def _classify_phase(self, events: List[Dict]) -> str:
        """分类阶段类型"""
        # 统计事件名称中的关键词
        event_names = [e.get('name', '').lower() for e in events]
        all_names = ' '.join(event_names)
        
        # 简单的关键词匹配
        if 'forward' in all_names or 'attention' in all_names:
            return 'forward_pass'
        elif 'backward' in all_names or 'grad' in all_names:
            return 'backward_pass'
        elif 'sync' in all_names or 'cuda' in all_names:
            return 'synchronization'
        else:
            return 'computation'
    
    def generate_report(self, output_dir: str) -> None:
        """生成完整的分析报告"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"生成分析报告到: {output_path}")
        
        # 执行所有分析
        python_analysis = self.analyze_python_functions()
        cuda_analysis = self.analyze_cuda_events()
        torch_analysis = self.analyze_torch_operations()
        timing_analysis = self.analyze_timing_patterns()
        phase_analysis = self.detect_phases()
        
        # 生成汇总报告
        self._save_summary_report(output_path, {
            'python': python_analysis,
            'cuda': cuda_analysis,
            'torch': torch_analysis,
            'timing': timing_analysis,
            'phases': phase_analysis
        })
        
        # 生成详细的CSV报告
        self._save_detailed_csv(output_path, python_analysis, cuda_analysis, torch_analysis)
        
        logger.info("报告生成完成")
    
    def _save_summary_report(self, output_path: Path, analysis: Dict[str, Any]) -> None:
        """保存汇总报告"""
        report_file = output_path / 'sglang_profiler_summary.txt'
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("SGLang Profiler 分析报告\n")
            f.write("=" * 50 + "\n\n")
            
            # Python函数分析
            python_data = analysis['python']
            f.write(f"Python函数分析:\n")
            f.write(f"  总事件数: {python_data['total_events']}\n")
            f.write(f"  唯一函数数: {len(python_data['function_stats'])}\n")
            
            # 最耗时的Python函数
            top_functions = sorted(
                python_data['function_stats'].items(),
                key=lambda x: x[1]['total_duration'],
                reverse=True
            )[:10]
            
            f.write(f"\n  最耗时的Python函数 (Top 10):\n")
            for name, stats in top_functions:
                f.write(f"    {name}: {stats['total_duration']:.2f}μs "
                       f"({stats['count']}次调用, 平均{stats['avg_duration']:.2f}μs)\n")
            
            # CUDA分析
            f.write(f"\nCUDA事件分析:\n")
            cuda_data = analysis['cuda']
            f.write(f"  总CUDA事件数: {cuda_data['total_cuda_events']}\n")
            f.write(f"  内核事件数: {cuda_data['kernel_events']}\n")
            f.write(f"  运行时事件数: {cuda_data['runtime_events']}\n")
            f.write(f"  内存事件数: {cuda_data['memory_events']}\n")
            
            # 内核详情
            kernel_details = cuda_data.get('kernel_details', {})
            if kernel_details:
                f.write(f"  唯一内核数: {kernel_details.get('unique_kernels', 0)}\n")
                
                top_kernels = kernel_details.get('top_kernels', [])
                if top_kernels:
                    f.write(f"\n  最耗时的内核 (Top 5):\n")
                    for name, stats in top_kernels[:5]:
                        f.write(f"    {name}: {stats['total_duration']:.2f}μs "
                               f"({stats['count']}次调用)\n")
            
            # PyTorch操作分析
            f.write(f"\nPyTorch操作分析:\n")
            torch_data = analysis['torch']
            f.write(f"  总PyTorch事件数: {torch_data['total_torch_events']}\n")
            f.write(f"  唯一操作类型数: {len(torch_data['operation_stats'])}\n")
            
            # 最耗时的操作类型
            top_ops = sorted(
                torch_data['operation_stats'].items(),
                key=lambda x: x[1]['total_duration'],
                reverse=True
            )[:5]
            
            if top_ops:
                f.write(f"\n  最耗时的操作类型 (Top 5):\n")
                for op_type, stats in top_ops:
                    f.write(f"    {op_type}: {stats['total_duration']:.2f}μs "
                           f"({stats['count']}次调用)\n")
            
            # 时间模式分析
            f.write(f"\n时间模式分析:\n")
            timing_data = analysis['timing']
            if timing_data:
                f.write(f"  总执行时间: {timing_data['total_duration']:.2f}μs\n")
                f.write(f"  有效事件数: {timing_data['event_count']}\n")
            
            # 阶段分析
            f.write(f"\n阶段分析:\n")
            phase_data = analysis['phases']
            f.write(f"  检测到的阶段数: {phase_data['phase_count']}\n")
            
            phases = phase_data.get('phases', [])
            for i, phase in enumerate(phases):
                f.write(f"  阶段 {i+1}: {phase['phase_type']} "
                       f"(时长: {phase['duration']:.2f}μs, "
                       f"事件数: {len(phase['events'])})\n")
    
    def _save_detailed_csv(self, output_path: Path, python_analysis: Dict, 
                          cuda_analysis: Dict, torch_analysis: Dict) -> None:
        """保存详细的CSV报告"""
        
        # Python函数详情CSV
        python_csv = output_path / 'python_functions.csv'
        with open(python_csv, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['函数名', '调用次数', '总耗时(μs)', '平均耗时(μs)', 
                           '最小耗时(μs)', '最大耗时(μs)'])
            
            for name, stats in python_analysis['function_stats'].items():
                writer.writerow([
                    name,
                    stats['count'],
                    f"{stats['total_duration']:.2f}",
                    f"{stats['avg_duration']:.2f}",
                    f"{stats['min_duration']:.2f}" if stats['min_duration'] != float('inf') else "N/A",
                    f"{stats['max_duration']:.2f}"
                ])
        
        # PyTorch操作详情CSV
        torch_csv = output_path / 'torch_operations.csv'
        with open(torch_csv, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['操作类型', '调用次数', '总耗时(μs)', '平均耗时(μs)'])
            
            for op_type, stats in torch_analysis['operation_stats'].items():
                writer.writerow([
                    op_type,
                    stats['count'],
                    f"{stats['total_duration']:.2f}",
                    f"{stats['avg_duration']:.2f}"
                ])


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="分析SGLang Profiler JSON文件")
    parser.add_argument("json_file", help="SGLang profiler JSON文件路径")
    parser.add_argument("-o", "--output", default="sglang_analysis_output", 
                       help="输出目录 (默认: sglang_analysis_output)")
    parser.add_argument("-v", "--verbose", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 检查输入文件
    if not Path(args.json_file).exists():
        logger.error(f"文件不存在: {args.json_file}")
        sys.exit(1)
    
    try:
        # 创建分析器并运行分析
        analyzer = SGLangProfilerAnalyzer(args.json_file)
        analyzer.load_data()
        analyzer.generate_report(args.output)
        
        print(f"\n✅ 分析完成！结果已保存到: {args.output}")
        print(f"📊 主要报告文件:")
        print(f"  - sglang_profiler_summary.txt: 汇总报告")
        print(f"  - python_functions.csv: Python函数详情")
        print(f"  - torch_operations.csv: PyTorch操作详情")
        
    except Exception as e:
        logger.error(f"分析失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
