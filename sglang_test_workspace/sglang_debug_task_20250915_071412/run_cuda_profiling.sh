#!/bin/bash

# CUDA算子性能测试脚本
# 基于SGLang离线推理的性能分析

SCRIPT_DIR="/workspace/sglang_test_workspace/sglang_debug_task_20250915_071412"
PROFILE_DIR="/workspace/sglang_test_workspace/profile_logs"

# 创建输出目录
mkdir -p $PROFILE_DIR

echo "=== SGLang CUDA算子性能测试 ==="
echo "脚本目录: $SCRIPT_DIR"
echo "Profile输出目录: $PROFILE_DIR"
echo ""

# 检查NVTX是否可用
check_nvtx() {
    python3 -c "import nvtx; print('NVTX available')" 2>/dev/null && return 0 || return 1
}

# 1. 基础PyTorch Profiler测试
echo "1. 运行PyTorch Profiler性能测试..."
echo "----------------------------------------"
cd $SCRIPT_DIR
python3 offline_sglang_generate.py

echo ""
echo "2. 检查生成的trace文件..."
echo "----------------------------------------"
ls -la $PROFILE_DIR/*.json 2>/dev/null && echo "Chrome trace文件已生成，可以在 https://ui.perfetto.dev/ 或 chrome://tracing 中查看"

# 3. 如果NVTX可用，运行Nsight Systems兼容版本
echo ""
echo "3. 检查NVTX支持..."
echo "----------------------------------------"
if check_nvtx; then
    echo "NVTX可用，运行Nsight Systems兼容版本..."
    python3 offline_sglang_generate_nsight.py
else
    echo "NVTX不可用，安装命令: pip install nvtx"
    echo "可选：运行Nsight Systems命令:"
    echo "nsys profile --trace-fork-before-exec=true --cuda-graph-trace=node python3 offline_sglang_generate_nsight.py"
fi

# 4. 显示性能分析建议
echo ""
echo "=== 性能分析工具使用建议 ==="
echo "----------------------------------------"
echo "1. PyTorch Profiler (已运行):"
echo "   - 查看生成的JSON文件: $PROFILE_DIR/*.json"
echo "   - 在浏览器中打开: https://ui.perfetto.dev/"
echo ""
echo "2. Nsight Systems (高级分析):"
echo "   - 安装: apt install nsight-systems-cli"
echo "   - 运行: nsys profile --trace-fork-before-exec=true --cuda-graph-trace=node python3 offline_sglang_generate_nsight.py"
echo "   - 生成 .nsys-rep 文件，用Nsight Systems GUI查看"
echo ""
echo "3. 关键性能指标:"
echo "   - CUDA kernel执行时间"
echo "   - 内存使用情况"
echo "   - GPU利用率"
echo "   - 算子融合机会"

echo ""
echo "=== 测试完成 ==="
