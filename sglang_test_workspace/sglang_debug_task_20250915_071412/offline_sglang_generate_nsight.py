#!/usr/bin/env python3
import os
import sys
import traceback
import time

# 必须在导入 sglang 前设置分布式/通信相关环境
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

# NVTX for Nsight Systems profiling
try:
    import nvtx
    NVTX_AVAILABLE = True
    print("[offline] NVTX available for Nsight Systems profiling")
except ImportError:
    NVTX_AVAILABLE = False
    print("[offline] NVTX not available, install with: pip install nvtx")

import sglang as sgl

MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")


def main():
    # 直接离线引擎加载（不启动HTTP服务）
    llm = None
    try:
        if NVTX_AVAILABLE:
            nvtx.push_range("Engine Initialization", color="blue")
            
        print("[offline] init Engine ...")
        llm = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=1,
            quantization="w8a8_int8",
            trust_remote_code=True,
            dist_init_addr="127.0.0.1:8001",
            disable_cuda_graph=True,  # 禁用CUDA Graph以便看到详细的kernel调用
            log_level="info",
        )
        
        if NVTX_AVAILABLE:
            nvtx.pop_range()

        prompt = "用一句话介绍你自己。请详细描述你的功能和特点。"
        sampling_params = {"max_new_tokens": 64, "temperature": 0.7}
        
        if NVTX_AVAILABLE:
            nvtx.push_range("Text Generation", color="green")
            
        print("[offline] generate ...")
        start_time = time.time()
        out = llm.generate(prompt=prompt, sampling_params=sampling_params)
        end_time = time.time()
        
        if NVTX_AVAILABLE:
            nvtx.pop_range()
            
        print("Generate OK\n---\n", out.get("text", out))
        print(f"\n[offline] Generation time: {end_time - start_time:.3f} seconds")
        
    except Exception:
        print("[offline] ERROR:\n" + traceback.format_exc())
        sys.exit(1)
    finally:
        if NVTX_AVAILABLE:
            nvtx.push_range("Cleanup", color="red")
        if llm is not None:
            try:
                llm.shutdown()
            except Exception:
                pass
        if NVTX_AVAILABLE:
            nvtx.pop_range()


if __name__ == "__main__":
    main()
