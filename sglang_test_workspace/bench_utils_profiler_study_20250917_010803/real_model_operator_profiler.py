#!/usr/bin/env python3
"""
SGLang 真实模型算子性能分析工具

专门用于分析真实模型在实际推理过程中的算子性能，包括 shape 变化观察。
基于 vLLM kernels benchmark 设计，集成 SGLang 推理引擎。
"""

import argparse
import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

import numpy as np
import torch
import torch.nn.functional as F
from torch.profiler import profile, ProfilerActivity, tensorboard_trace_handler

# SGLang imports
import sglang as sgl
from sglang.srt.server_args import ServerArgs
from sglang.srt.utils import configure_logger, suppress_other_loggers


class RealModelOperatorProfiler:
    """真实模型算子性能分析器"""
    
    def __init__(
        self, 
        model_path: str,
        quantization: Optional[str] = None,
        trust_remote_code: bool = False,
        tp_size: int = 1,
        output_dir: Optional[str] = None
    ):
        self.model_path = model_path
        self.quantization = quantization
        self.trust_remote_code = trust_remote_code
        self.tp_size = tp_size
        
        # 创建输出目录
        if output_dir:
            self.output_dir = Path(output_dir)
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.output_dir = Path(f"./real_model_profiling_{timestamp}")
        
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.engine = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        print(f"=== SGLang 真实模型算子性能分析器 ===")
        print(f"模型路径: {model_path}")
        print(f"量化方式: {quantization}")
        print(f"Tensor 并行: {tp_size}")
        print(f"输出目录: {self.output_dir}")
    
    def initialize_engine(self):
        """初始化 SGLang 引擎"""
        print("正在初始化 SGLang 引擎...")
        
        # 配置日志
        suppress_other_loggers()
        
        try:
            self.engine = sgl.Engine(
                model_path=self.model_path,
                tp_size=self.tp_size,
                quantization=self.quantization,
                trust_remote_code=self.trust_remote_code,
                disable_cuda_graph=True,  # 禁用 CUDA Graph 以观察详细算子
                log_level="error",  # 减少日志干扰
                mem_fraction_static=0.7,  # 减少内存使用
            )
            print("✅ SGLang 引擎初始化成功")
            return True
        except Exception as e:
            print(f"❌ SGLang 引擎初始化失败: {e}")
            return False
    
    def create_test_prompts(self, lengths: List[int] = None) -> List[str]:
        """创建不同长度的测试 prompt"""
        if lengths is None:
            lengths = [128, 256, 512]
        
        prompts = []
        
        for length in lengths:
            # 创建指定长度的 prompt
            base_text = "请分析人工智能技术在未来社会发展中的作用和影响。"
            repeat_count = max(1, length // len(base_text))
            prompt = (base_text + " ") * repeat_count
            prompt = prompt[:length]  # 截断到指定长度
            prompts.append(prompt)
        
        return prompts
    
    def profile_inference_with_shapes(
        self, 
        prompts: List[str],
        max_new_tokens: int = 128,
        profile_name: str = "inference"
    ) -> Dict[str, Any]:
        """使用 profiler 分析推理过程，观察 shape 变化"""
        
        print(f"\n🔍 开始深度 Profiling: {profile_name}")
        print(f"测试 prompts 数量: {len(prompts)}")
        print(f"最大生成长度: {max_new_tokens}")
        
        # 创建 profiler 输出目录
        profile_dir = self.output_dir / f"{profile_name}_profile"
        profile_dir.mkdir(exist_ok=True)
        
        # L2 缓存刷新 (参考 SGLang bench_utils.py)
        def flush_l2_cache():
            cache_size = 1024 * 1024 * 64  # 64MB，更小的缓存大小
            try:
                cache_flush = torch.empty(cache_size, dtype=torch.int8, device=self.device)
                cache_flush.fill_(0)
                del cache_flush
                torch.cuda.empty_cache()
            except:
                pass  # 如果内存不足，跳过缓存刷新
        
        # 预热 - 多次调用以初始化所有算子
        print("  预热中...")
        flush_l2_cache()
        try:
            # 多次预热调用以确保所有算子被初始化
            for i in range(2):
                for prompt in prompts[:1]:  # 只用第一个 prompt 预热
                    _ = self.engine.generate(
                        prompt=prompt,
                        sampling_params={"max_new_tokens": min(8, max_new_tokens), "temperature": 0.0}
                    )
                print(f"    预热轮次 {i+1}/2 完成")
        except Exception as e:
            print(f"  预热过程中出现错误: {e}")
        
        # 开始 profiling
        results = {
            "config": {
                "model_path": self.model_path,
                "quantization": self.quantization,
                "tp_size": self.tp_size,
                "max_new_tokens": max_new_tokens,
                "num_prompts": len(prompts)
            },
            "prompts_info": [],
            "profiling_files": [],
            "timing_stats": {},
            "shape_analysis": {}
        }
        
        # 记录每个 prompt 的信息
        for i, prompt in enumerate(prompts):
            results["prompts_info"].append({
                "index": i,
                "length": len(prompt),
                "preview": prompt[:100] + "..." if len(prompt) > 100 else prompt
            })
        
        print("  正在进行详细 profiling (多轮次)...")
        
        # 配置 profiler
        activities = [ProfilerActivity.CPU, ProfilerActivity.CUDA]
        
        # 使用 profiler 进行推理 - 多轮次以捕获更多算子
        latencies = []
        with profile(
            activities=activities,
            record_shapes=True,  # 🔍 关键: 记录 tensor shapes
            profile_memory=True,
            with_stack=True,
            with_flops=True,
            with_modules=True,  # 🔍 添加模块级别的追踪
            on_trace_ready=tensorboard_trace_handler(
                str(profile_dir), 
                worker_name=f"{profile_name}_model_{Path(self.model_path).name}"
            )
        ) as profiler:
            
            # 多轮次推理以捕获完整的算子执行
            for round_idx in range(3):  # 3 轮推理
                print(f"    推理轮次 {round_idx + 1}/3")
                
                for step, prompt in enumerate(prompts):
                    flush_l2_cache()
                    torch.cuda.synchronize()
                    
                    start_time = time.perf_counter()
                    try:
                        # 逐步生成，以捕获 decode 阶段的算子
                        output = self.engine.generate(
                            prompt=prompt,
                            sampling_params={
                                "max_new_tokens": max_new_tokens,
                                "temperature": 0.0,
                                "top_p": 1.0,
                            }
                        )
                        torch.cuda.synchronize()
                        end_time = time.perf_counter()
                        
                        latency = (end_time - start_time) * 1000  # ms
                        latencies.append(latency)
                        
                        print(f"      Prompt {step+1}/{len(prompts)}: {latency:.2f} ms")
                        
                    except Exception as e:
                        print(f"      Prompt {step+1} 生成失败: {e}")
                        latencies.append(float('inf'))
                    
                    # 每个 prompt 后执行一次 profiler step
                    profiler.step()
        
        # 计算统计数据
        valid_latencies = [l for l in latencies if l != float('inf')]
        if valid_latencies:
            results["timing_stats"] = {
                "mean_ms": float(np.mean(valid_latencies)),
                "std_ms": float(np.std(valid_latencies)),
                "min_ms": float(np.min(valid_latencies)),
                "max_ms": float(np.max(valid_latencies)),
                "p50_ms": float(np.percentile(valid_latencies, 50)),
                "p95_ms": float(np.percentile(valid_latencies, 95)),
                "p99_ms": float(np.percentile(valid_latencies, 99)),
                "total_prompts": len(prompts) * 3,  # 3 轮
                "successful_prompts": len(valid_latencies)
            }
        
        # 导出 Chrome trace (用于详细的 shape 分析)
        try:
            chrome_trace_path = profile_dir / f"{profile_name}_chrome_trace.json"
            profiler.export_chrome_trace(str(chrome_trace_path))
            results["profiling_files"].append(str(chrome_trace_path))
            print(f"  ✅ Chrome trace 已保存: {chrome_trace_path}")
        except Exception as e:
            print(f"  ⚠️ Chrome trace 导出失败: {e}")
        
        # 生成算子统计表格
        try:
            print(f"\n📊 === {profile_name.upper()} 算子性能统计 ===")
            key_averages = profiler.key_averages()
            
            # 保存详细统计到文件
            stats_file = profile_dir / f"{profile_name}_operator_stats.txt"
            with open(stats_file, 'w') as f:
                f.write(f"=== {profile_name.upper()} 算子性能统计 ===\n\n")
                f.write("按 CUDA 时间排序 (前50个):\n")
                f.write(key_averages.table(sort_by="cuda_time_total", row_limit=50))
                f.write("\n\n按调用次数排序 (前30个):\n")
                f.write(key_averages.table(sort_by="count", row_limit=30))
                f.write("\n\n按 Self CUDA 时间排序 (前30个):\n")
                f.write(key_averages.table(sort_by="self_cuda_time_total", row_limit=30))
            
            # 打印 Top 15 算子 (按 CUDA 时间)
            print("按 CUDA 时间排序:")
            print(key_averages.table(sort_by="cuda_time_total", row_limit=15))
            
            # 打印 Top 10 算子 (按调用次数)
            print("\n按调用次数排序:")
            print(key_averages.table(sort_by="count", row_limit=10))
            
            # 提取关键算子信息 (包含 shape 信息)
            results["shape_analysis"] = self.analyze_operator_shapes(key_averages)
            
            print(f"\n🔍 算子分类统计:")
            for category, count in results["shape_analysis"]["operator_counts"].items():
                print(f"  {category}: {count}")
            
        except Exception as e:
            print(f"  ⚠️ 算子统计生成失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 保存 TensorBoard 路径
        results["profiling_files"].append(str(profile_dir))
        
        return results
    
    def analyze_operator_shapes(self, key_averages) -> Dict[str, Any]:
        """分析算子的 shape 信息"""
        shape_analysis = {
            "attention_ops": [],
            "matmul_ops": [],
            "moe_ops": [],
            "memory_ops": [],
            "quantization_ops": [],
            "layernorm_ops": [],
            "activation_ops": [],
            "all_ops": [],
            "shape_summary": {}
        }
        
        for event in key_averages:
            event_name = event.key
            # 修复属性访问问题
            input_shapes = getattr(event, 'input_shapes', []) or []
            cuda_time = getattr(event, 'cuda_time', 0)
            cpu_time = getattr(event, 'cpu_time', 0)
            self_cuda_time = getattr(event, 'self_cuda_time', 0)
            self_cpu_time = getattr(event, 'self_cpu_time', 0)
            count = getattr(event, 'count', 0)
            
            op_info = {
                "name": event_name,
                "input_shapes": input_shapes,
                "cuda_time": cuda_time,
                "cpu_time": cpu_time,
                "self_cuda_time": self_cuda_time,
                "self_cpu_time": self_cpu_time,
                "count": count
            }
            
            # 添加到全部算子
            shape_analysis["all_ops"].append(op_info)
            
            # 分类不同类型的算子 (更全面的匹配)
            event_lower = event_name.lower()
            
            if any(attn_kw in event_lower for attn_kw in ['attention', 'attn', 'scaled_dot_product', 'flash_attn']):
                shape_analysis["attention_ops"].append(op_info)
            elif any(mm_kw in event_lower for mm_kw in ['matmul', 'bmm', 'gemm', 'addmm', 'mm', 'linear']):
                shape_analysis["matmul_ops"].append(op_info)
            elif any(moe_kw in event_lower for moe_kw in ['moe', 'expert', 'gating', 'router']):
                shape_analysis["moe_ops"].append(op_info)
            elif any(quant_kw in event_lower for quant_kw in ['quant', 'dequant', 'int8', 'w8a8', 'scale']):
                shape_analysis["quantization_ops"].append(op_info)
            elif any(norm_kw in event_lower for norm_kw in ['layer_norm', 'layernorm', 'rms_norm', 'rmsnorm']):
                shape_analysis["layernorm_ops"].append(op_info)
            elif any(act_kw in event_lower for act_kw in ['relu', 'gelu', 'silu', 'swish', 'sigmoid', 'tanh']):
                shape_analysis["activation_ops"].append(op_info)
            elif any(mem_op in event_lower for mem_op in ['copy', 'fill', 'empty', 'malloc', 'free', 'clone']):
                shape_analysis["memory_ops"].append(op_info)
        
        # 生成 shape 摘要
        all_shapes = []
        for category in ["attention_ops", "matmul_ops", "moe_ops", "quantization_ops", "layernorm_ops"]:
            for op in shape_analysis[category]:
                if op["input_shapes"]:
                    all_shapes.extend(op["input_shapes"])
        
        if all_shapes:
            shape_analysis["shape_summary"] = {
                "total_unique_shapes": len(set(map(str, all_shapes))),
                "common_shapes": list(set(map(str, all_shapes)))[:10],  # 前 10 个不重复的 shape
                "max_tensor_elements": max(
                    [np.prod(shape) if isinstance(shape, (list, tuple)) and all(isinstance(x, int) for x in shape) else 0 
                     for shape in all_shapes], default=0
                )
            }
        else:
            shape_analysis["shape_summary"] = {
                "total_unique_shapes": 0,
                "common_shapes": [],
                "max_tensor_elements": 0
            }
        
        # 添加算子数量统计
        shape_analysis["operator_counts"] = {
            "total_ops": len(shape_analysis["all_ops"]),
            "attention_ops": len(shape_analysis["attention_ops"]),
            "matmul_ops": len(shape_analysis["matmul_ops"]),
            "moe_ops": len(shape_analysis["moe_ops"]),
            "quantization_ops": len(shape_analysis["quantization_ops"]),
            "layernorm_ops": len(shape_analysis["layernorm_ops"]),
            "activation_ops": len(shape_analysis["activation_ops"]),
            "memory_ops": len(shape_analysis["memory_ops"])
        }
        
        return shape_analysis
    
    def run_comprehensive_analysis(
        self, 
        test_lengths: List[int] = None,
        max_new_tokens: int = 64
    ) -> Dict[str, Any]:
        """运行全面的模型算子分析"""
        
        if test_lengths is None:
            test_lengths = [128, 256, 512]
        
        # 初始化引擎
        if not self.initialize_engine():
            return {"error": "引擎初始化失败"}
        
        print(f"\n🚀 开始全面的模型算子性能分析")
        print(f"测试序列长度: {test_lengths}")
        print(f"生成长度: {max_new_tokens}")
        
        comprehensive_results = {
            "model_info": {
                "model_path": self.model_path,
                "quantization": self.quantization,
                "tp_size": self.tp_size
            },
            "test_configs": {
                "input_lengths": test_lengths,
                "max_new_tokens": max_new_tokens
            },
            "length_analysis": {},
            "overall_summary": {}
        }
        
        # 针对每个长度进行测试
        for length in test_lengths:
            print(f"\n📏 测试输入长度: {length}")
            
            # 创建该长度的测试 prompt
            prompts = self.create_test_prompts([length])
            
            # 进行 profiling
            length_results = self.profile_inference_with_shapes(
                prompts=prompts,
                max_new_tokens=max_new_tokens,
                profile_name=f"length_{length}"
            )
            
            comprehensive_results["length_analysis"][length] = length_results
        
        # 生成总体摘要
        self.generate_comprehensive_summary(comprehensive_results)
        
        # 保存结果
        results_file = self.output_dir / "comprehensive_analysis_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(comprehensive_results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n✅ 全面分析完成！结果已保存到: {results_file}")
        
        return comprehensive_results
    
    def generate_comprehensive_summary(self, results: Dict[str, Any]):
        """生成全面的分析摘要报告"""
        
        report_path = self.output_dir / "analysis_summary_report.md"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# SGLang 真实模型算子性能分析报告\n\n")
            f.write(f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**模型路径**: {results['model_info']['model_path']}\n")
            f.write(f"**量化方式**: {results['model_info']['quantization']}\n")
            f.write(f"**Tensor 并行**: {results['model_info']['tp_size']}\n\n")
            
            # 性能摘要表格
            f.write("## 性能摘要\n\n")
            f.write("| 输入长度 | 平均延迟 (ms) | P95 延迟 (ms) | 成功率 | 主要算子类型 |\n")
            f.write("|----------|---------------|---------------|--------|-------------|\n")
            
            for length, analysis in results["length_analysis"].items():
                if "timing_stats" in analysis:
                    stats = analysis["timing_stats"]
                    success_rate = stats["successful_prompts"] / stats["total_prompts"] * 100
                    
                    # 统计主要算子类型
                    shape_analysis = analysis.get("shape_analysis", {})
                    main_ops = []
                    if shape_analysis.get("attention_ops"):
                        main_ops.append("Attention")
                    if shape_analysis.get("matmul_ops"):
                        main_ops.append("MatMul")
                    if shape_analysis.get("moe_ops"):
                        main_ops.append("MoE")
                    
                    f.write(f"| {length} | {stats['mean_ms']:.2f} | {stats['p95_ms']:.2f} | {success_rate:.1f}% | {', '.join(main_ops)} |\n")
            
            # Shape 分析
            f.write("\n## Shape 变化分析\n\n")
            for length, analysis in results["length_analysis"].items():
                shape_analysis = analysis.get("shape_analysis", {})
                if shape_analysis.get("shape_summary"):
                    summary = shape_analysis["shape_summary"]
                    f.write(f"### 输入长度 {length}\n")
                    f.write(f"- 独特 Shape 数量: {summary.get('total_unique_shapes', 0)}\n")
                    f.write(f"- 最大张量元素数: {summary.get('max_tensor_elements', 0):,}\n")
                    if summary.get('common_shapes'):
                        f.write(f"- 常见 Shapes: {', '.join(summary['common_shapes'][:5])}\n")
                    f.write("\n")
            
            # 算子类型分析
            f.write("## 算子类型分析\n\n")
            f.write("### 主要算子类别统计\n\n")
            
            all_categories = {"attention_ops": "Attention", "matmul_ops": "MatMul", "moe_ops": "MoE", "memory_ops": "Memory"}
            
            for cat_key, cat_name in all_categories.items():
                f.write(f"#### {cat_name} 算子\n")
                total_ops = 0
                total_cuda_time = 0
                
                for length, analysis in results["length_analysis"].items():
                    shape_analysis = analysis.get("shape_analysis", {})
                    ops = shape_analysis.get(cat_key, [])
                    total_ops += len(ops)
                    total_cuda_time += sum(op.get("cuda_time", 0) for op in ops)
                
                f.write(f"- 总数量: {total_ops}\n")
                f.write(f"- 总 CUDA 时间: {total_cuda_time:.2f} µs\n\n")
            
            # 优化建议
            f.write("## 优化建议\n\n")
            f.write("基于分析结果，以下是针对性的优化建议：\n\n")
            
            # 基于量化方式的建议
            if results['model_info']['quantization'] == 'w8a8_int8':
                f.write("### INT8 量化优化\n")
                f.write("- ✅ 已启用 W8A8 INT8 量化，有效减少内存使用\n")
                f.write("- 💡 建议监控量化误差对模型质量的影响\n")
                f.write("- 💡 考虑使用专门的 INT8 优化算子库\n\n")
            
            # 基于延迟分析的建议
            high_latency_lengths = []
            for length, analysis in results["length_analysis"].items():
                if "timing_stats" in analysis and analysis["timing_stats"]["mean_ms"] > 1000:
                    high_latency_lengths.append(length)
            
            if high_latency_lengths:
                f.write("### 高延迟场景优化\n")
                f.write(f"- ⚠️ 输入长度 {high_latency_lengths} 存在较高延迟\n")
                f.write("- 💡 建议使用批处理或异步处理\n")
                f.write("- 💡 考虑序列并行或专家并行策略\n\n")
            
            f.write("### 通用优化建议\n")
            f.write("- 🔧 使用 CUDA Graph 进行算子融合\n")
            f.write("- 🔧 优化内存分配和释放策略\n")
            f.write("- 🔧 考虑使用 FlashAttention 等优化算子\n")
            f.write("- 📊 定期进行性能基准测试\n")
        
        print(f"📄 详细分析报告已保存: {report_path}")
    
    def cleanup(self):
        """清理资源"""
        if self.engine:
            try:
                self.engine.shutdown()
                print("✅ SGLang 引擎已关闭")
            except Exception as e:
                print(f"⚠️ 引擎关闭时出现错误: {e}")


def main():
    parser = argparse.ArgumentParser(
        description='SGLang 真实模型算子性能分析工具',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument('--model-path', type=str, required=True, help='模型路径')
    parser.add_argument('--quantization', type=str, help='量化方式 (如 w8a8_int8)')
    parser.add_argument('--trust-remote-code', action='store_true', help='信任远程代码')
    parser.add_argument('--tp-size', type=int, default=1, help='Tensor 并行度')
    parser.add_argument('--test-lengths', type=int, nargs='+', default=[128, 256], 
                      help='测试的输入长度列表')
    parser.add_argument('--max-new-tokens', type=int, default=64, help='最大生成 token 数')
    parser.add_argument('--output-dir', type=str, help='输出目录')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    print(f"=== SGLang 真实模型算子性能分析 ===")
    print(f"参数: {args}")
    
    # 创建分析器
    profiler = RealModelOperatorProfiler(
        model_path=args.model_path,
        quantization=args.quantization,
        trust_remote_code=args.trust_remote_code,
        tp_size=args.tp_size,
        output_dir=args.output_dir
    )
    
    try:
        # 运行全面分析
        results = profiler.run_comprehensive_analysis(
            test_lengths=args.test_lengths,
            max_new_tokens=args.max_new_tokens
        )
        
        if "error" not in results:
            print(f"\n🎉 分析完成！")
            print(f"📂 结果目录: {profiler.output_dir}")
            print(f"📊 查看详细报告: {profiler.output_dir}/analysis_summary_report.md")
            print(f"📋 查看原始数据: {profiler.output_dir}/comprehensive_analysis_results.json")
            
            # 打印快速摘要
            if args.verbose:
                print(f"\n📈 快速性能摘要:")
                for length, analysis in results["length_analysis"].items():
                    if "timing_stats" in analysis:
                        stats = analysis["timing_stats"]
                        print(f"  长度 {length}: {stats['mean_ms']:.2f} ± {stats['std_ms']:.2f} ms")
        else:
            print(f"❌ 分析失败: {results['error']}")
    
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断分析")
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        profiler.cleanup()


if __name__ == "__main__":
    main()
