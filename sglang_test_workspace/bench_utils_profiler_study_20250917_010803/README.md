# 使用说明

## 快速开始

1. **激活环境**:
   ```bash
   source /workspace/sglang_test/bin/activate
   ```

2. **进入任务目录**:
   ```bash
   cd /workspace/sglang_test_workspace/bench_utils_profiler_study_20250917_010803
   ```

3. **运行测试**:
   ```bash
   # 运行所有测试
   ./run_tests.sh
   
   # 或者单独运行
   python fixed_offline_sglang_generate.py
   python improved_offline_sglang_generate.py
   ```

4. **查看结果**:
   - Profile traces: `/workspace/sglang_test_workspace/profile_logs/`
   - 分析文档: `bench_utils_analysis.md`
   - 任务总结: `task_summary.md`

## 文件说明

- `bench_utils_analysis.md` - bench_utils.py 源码学习分析
- `fixed_offline_sglang_generate.py` - 修复版原始代码  
- `improved_offline_sglang_generate.py` - 集成最佳实践的改进版
- `task_summary.md` - 完整任务总结报告
- `run_tests.sh` - 自动化测试脚本
- `README.md` - 本使用说明
