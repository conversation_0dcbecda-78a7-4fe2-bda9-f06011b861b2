#!/usr/bin/env python3
"""
SGLang Profiler 专门测试脚本

基于 bench_one_batch.py 的设计模式，专门用于深度性能分析和 profiling。
集成了 bench_utils.py 的最佳实践，提供详细的性能分析功能。

# 使用示例：
## 基础 profiling:
python sglang_profiler_bench.py --model-path /home/<USER>/deepseek-int8 --quantization w8a8_int8

## 多参数扫描 profiling:
python sglang_profiler_bench.py --model-path /home/<USER>/deepseek-int8 --batch-size 1 2 4 --input-len 512 1024 --output-len 32 64 --profile-all

## 深度内核分析:
python sglang_profiler_bench.py --model-path /home/<USER>/deepseek-int8 --profile-kernels attention matmul moe --profile-detail

## 内存分析:
python sglang_profiler_bench.py --model-path /home/<USER>/deepseek-int8 --profile-memory --profile-record-shapes

## L2 缓存刷新测试:
python sglang_profiler_bench.py --model-path /home/<USER>/deepseek-int8 --enable-l2-flush --l2-flush-size 1
"""

import argparse
import copy
import dataclasses
import itertools
import json
import logging
import multiprocessing
import os
import sys
import time
from contextlib import nullcontext
from typing import Tuple, List, Optional

import numpy as np
import torch
import torch.distributed as dist

from sglang.srt.configs.model_config import ModelConfig
from sglang.srt.distributed.parallel_state import destroy_distributed_environment
from sglang.srt.entrypoints.engine import _set_envs_and_config
from sglang.srt.hf_transformers_utils import get_tokenizer
from sglang.srt.managers.schedule_batch import Req, ScheduleBatch
from sglang.srt.managers.scheduler import Scheduler
from sglang.srt.model_executor.forward_batch_info import ForwardBatch
from sglang.srt.model_executor.model_runner import ModelRunner
from sglang.srt.sampling.sampling_params import SamplingParams
from sglang.srt.server_args import PortArgs, ServerArgs
from sglang.srt.speculative.spec_info import SpeculativeAlgorithm
from sglang.srt.utils import (
    configure_logger,
    get_bool_env_var,
    kill_process_tree,
    require_mlp_sync,
    require_mlp_tp_gather,
    set_gpu_proc_affinity,
    suppress_other_loggers,
)


class suppress_stdout_stderr:
    """从 bench_utils.py 学习的输出抑制类，用于清理 profiling 输出"""
    def __enter__(self):
        self.outnull_file = open(os.devnull, "w")
        self.errnull_file = open(os.devnull, "w")

        self.old_stdout_fileno_undup = sys.stdout.fileno()
        self.old_stderr_fileno_undup = sys.stderr.fileno()

        self.old_stdout_fileno = os.dup(sys.stdout.fileno())
        self.old_stderr_fileno = os.dup(sys.stderr.fileno())

        self.old_stdout = sys.stdout
        self.old_stderr = sys.stderr

        os.dup2(self.outnull_file.fileno(), self.old_stdout_fileno_undup)
        os.dup2(self.errnull_file.fileno(), self.old_stderr_fileno_undup)

        sys.stdout = self.outnull_file
        sys.stderr = self.errnull_file
        return self

    def __exit__(self, *_):
        sys.stdout = self.old_stdout
        sys.stderr = self.old_stderr

        os.dup2(self.old_stdout_fileno, self.old_stdout_fileno_undup)
        os.dup2(self.old_stderr_fileno, self.old_stderr_fileno_undup)

        os.close(self.old_stdout_fileno)
        os.close(self.old_stderr_fileno)

        self.outnull_file.close()
        self.errnull_file.close()


@dataclasses.dataclass
class ProfilerBenchArgs:
    """Profiler 专用的基准测试参数"""
    run_name: str = "profiler_bench"
    batch_size: Tuple[int] = (1,)
    input_len: Tuple[int] = (512,)
    output_len: Tuple[int] = (32,)
    prompt_filename: str = ""
    result_filename: str = "profiler_results.jsonl"
    
    # Profiler 专用参数
    profile_all: bool = False  # 是否 profile 所有阶段
    profile_prefill: bool = True  # 是否 profile prefill 阶段
    profile_decode: bool = True   # 是否 profile decode 阶段
    profile_memory: bool = False  # 是否进行内存分析
    profile_record_shapes: bool = False  # 是否记录 tensor shapes
    profile_with_stack: bool = True     # 是否包含调用栈信息
    profile_with_flops: bool = False    # 是否计算 FLOPS
    profile_with_modules: bool = False  # 是否包含模块信息
    profile_detail: bool = False        # 是否进行详细分析
    
    # 输出控制
    profile_output_dir: str = "profiler_traces"
    profile_filename_prefix: str = "sglang_profiler"
    suppress_profiler_output: bool = False
    
    # L2 缓存控制
    enable_l2_flush: bool = True
    l2_flush_size: float = 1.0  # GB
    
    # 内核分析
    profile_kernels: Tuple[str] = ()  # 要分析的特定内核
    kernel_warmup_runs: int = 2       # 内核预热次数
    kernel_test_runs: int = 5         # 内核测试次数
    
    # Nsight Systems 集成
    enable_nsys: bool = False
    nsys_output_prefix: str = "sglang_nsys"

    @staticmethod
    def add_cli_args(parser: argparse.ArgumentParser):
        parser.add_argument("--run-name", type=str, default=ProfilerBenchArgs.run_name)
        parser.add_argument("--batch-size", type=int, nargs="+", default=ProfilerBenchArgs.batch_size)
        parser.add_argument("--input-len", type=int, nargs="+", default=ProfilerBenchArgs.input_len)
        parser.add_argument("--output-len", type=int, nargs="+", default=ProfilerBenchArgs.output_len)
        parser.add_argument("--prompt-filename", type=str, default=ProfilerBenchArgs.prompt_filename)
        parser.add_argument("--result-filename", type=str, default=ProfilerBenchArgs.result_filename)
        
        # Profiler 参数
        parser.add_argument("--profile-all", action="store_true", help="Profile 所有阶段")
        parser.add_argument("--profile-prefill", action="store_true", default=True, help="Profile prefill 阶段")
        parser.add_argument("--profile-decode", action="store_true", default=True, help="Profile decode 阶段")
        parser.add_argument("--profile-memory", action="store_true", help="进行内存分析")
        parser.add_argument("--profile-record-shapes", action="store_true", help="记录 tensor shapes")
        parser.add_argument("--profile-with-stack", action="store_true", default=True, help="包含调用栈信息")
        parser.add_argument("--profile-with-flops", action="store_true", help="计算 FLOPS")
        parser.add_argument("--profile-with-modules", action="store_true", help="包含模块信息")
        parser.add_argument("--profile-detail", action="store_true", help="详细分析")
        
        # 输出控制
        parser.add_argument("--profile-output-dir", type=str, default=ProfilerBenchArgs.profile_output_dir)
        parser.add_argument("--profile-filename-prefix", type=str, default=ProfilerBenchArgs.profile_filename_prefix)
        parser.add_argument("--suppress-profiler-output", action="store_true", help="抑制 profiler 输出")
        
        # L2 缓存控制
        parser.add_argument("--enable-l2-flush", action="store_true", default=True, help="启用 L2 缓存刷新")
        parser.add_argument("--l2-flush-size", type=float, default=ProfilerBenchArgs.l2_flush_size, help="L2 缓存刷新大小(GB)")
        
        # 内核分析
        parser.add_argument("--profile-kernels", type=str, nargs="*", default=ProfilerBenchArgs.profile_kernels, 
                          help="要分析的特定内核 (如: attention matmul moe)")
        parser.add_argument("--kernel-warmup-runs", type=int, default=ProfilerBenchArgs.kernel_warmup_runs)
        parser.add_argument("--kernel-test-runs", type=int, default=ProfilerBenchArgs.kernel_test_runs)
        
        # Nsight Systems
        parser.add_argument("--enable-nsys", action="store_true", help="启用 Nsight Systems")
        parser.add_argument("--nsys-output-prefix", type=str, default=ProfilerBenchArgs.nsys_output_prefix)

    @classmethod
    def from_cli_args(cls, args: argparse.Namespace):
        attrs = [(attr.name, type(attr.default)) for attr in dataclasses.fields(cls)]
        return cls(**{attr: attr_type(getattr(args, attr)) for attr, attr_type in attrs})


def load_model_for_profiling(server_args, port_args, tp_rank):
    """加载模型用于 profiling 测试"""
    suppress_other_loggers()
    rank_print = print if tp_rank == 0 else lambda *args, **kwargs: None
    moe_ep_rank = tp_rank // (server_args.tp_size // server_args.ep_size)

    model_config = ModelConfig.from_server_args(server_args)
    model_runner = ModelRunner(
        model_config=model_config,
        mem_fraction_static=server_args.mem_fraction_static,
        gpu_id=tp_rank,
        tp_rank=tp_rank,
        tp_size=server_args.tp_size,
        moe_ep_rank=moe_ep_rank,
        moe_ep_size=server_args.ep_size,
        pp_rank=0,
        pp_size=1,
        nccl_port=port_args.nccl_port,
        server_args=server_args,
    )
    
    rank_print(f"[Profiler] max_total_num_tokens={model_runner.max_total_num_tokens}")
    rank_print(f"[Profiler] Model loaded with quantization: {server_args.quantization}")
    
    tokenizer = get_tokenizer(
        server_args.tokenizer_path,
        tokenizer_mode=server_args.tokenizer_mode,
        trust_remote_code=server_args.trust_remote_code,
    )
    
    if server_args.tp_size > 1:
        dist.barrier()
    
    return model_runner, tokenizer


def prepare_profiler_inputs(batch_size, input_len, custom_inputs=None):
    """准备 profiler 测试用的输入数据"""
    if custom_inputs:
        input_ids = custom_inputs
    else:
        # 使用固定的随机种子确保可重现性
        np.random.seed(42)
        input_ids = np.random.randint(0, 10000, (batch_size, input_len), dtype=np.int32)
    
    sampling_params = SamplingParams(
        temperature=0,
        max_new_tokens=32,  # 使用固定值
    )

    reqs = []
    for i in range(len(input_ids)):
        req = Req(
            rid=i,
            origin_input_text="",
            origin_input_ids=list(input_ids[i]),
            sampling_params=sampling_params,
        )
        req.prefix_indices = []
        req.fill_ids = req.origin_input_ids
        req.extend_input_len = len(req.fill_ids) - len(req.prefix_indices)
        req.logprob_start_len = len(req.origin_input_ids) - 1
        reqs.append(req)

    return reqs


def create_profiler_config(bench_args):
    """创建 profiler 配置"""
    activities = []
    if bench_args.profile_all or bench_args.profile_prefill or bench_args.profile_decode:
        activities.extend([
            torch.profiler.ProfilerActivity.CPU,
            torch.profiler.ProfilerActivity.CUDA,
        ])
    
    config = {
        "activities": activities,
        "record_shapes": bench_args.profile_record_shapes,
        "profile_memory": bench_args.profile_memory,
        "with_stack": bench_args.profile_with_stack,
        "with_flops": bench_args.profile_with_flops,
        "with_modules": bench_args.profile_with_modules,
    }
    
    return config


def flush_l2_cache(bench_args, device, rank_print):
    """刷新 L2 缓存以确保测试一致性"""
    if not bench_args.enable_l2_flush:
        return
    
    flush_size = int(bench_args.l2_flush_size * 1e9 // 4)  # 转换为 int32 元素数量
    rank_print(f"[Profiler] Flushing L2 cache with {bench_args.l2_flush_size}GB data...")
    
    try:
        torch.empty(flush_size, dtype=torch.int, device=device).zero_()
    except torch.OutOfMemoryError as e:
        rank_print(f"[Profiler] L2 flush failed due to OOM, reducing size...")
        # 减少到一半大小重试
        flush_size = flush_size // 2
        torch.empty(flush_size, dtype=torch.int, device=device).zero_()


@torch.no_grad
def extend_with_profiling(reqs, model_runner):
    """带 profiling 的 extend 操作"""
    batch = ScheduleBatch.init_new(
        reqs=reqs,
        req_to_token_pool=model_runner.req_to_token_pool,
        token_to_kv_pool_allocator=model_runner.token_to_kv_pool_allocator,
        tree_cache=None,
        model_config=model_runner.model_config,
        enable_overlap=False,
        spec_algorithm=SpeculativeAlgorithm.NONE,
    )
    batch.prepare_for_extend()
    _maybe_prepare_mlp_sync_batch(batch, model_runner)
    model_worker_batch = batch.get_model_worker_batch()
    forward_batch = ForwardBatch.init_new(model_worker_batch, model_runner)
    logits_output, _ = model_runner.forward(forward_batch)
    next_token_ids = model_runner.sample(logits_output, forward_batch)
    return next_token_ids, logits_output.next_token_logits, batch


@torch.no_grad
def decode_with_profiling(input_token_ids, batch, model_runner):
    """带 profiling 的 decode 操作"""
    batch.output_ids = input_token_ids
    batch.prepare_for_decode()
    _maybe_prepare_mlp_sync_batch(batch, model_runner)
    model_worker_batch = batch.get_model_worker_batch()
    forward_batch = ForwardBatch.init_new(model_worker_batch, model_runner)
    logits_output, _ = model_runner.forward(forward_batch)
    next_token_ids = model_runner.sample(logits_output, forward_batch)
    return next_token_ids, logits_output.next_token_logits


def _maybe_prepare_mlp_sync_batch(batch: ScheduleBatch, model_runner):
    """准备 MLP 同步批次"""
    if require_mlp_sync(model_runner.server_args):
        Scheduler.prepare_mlp_sync_batch_raw(
            batch,
            dp_size=model_runner.server_args.dp_size,
            attn_tp_size=1,
            tp_group=model_runner.tp_group,
            get_idle_batch=None,
            disable_cuda_graph=model_runner.server_args.disable_cuda_graph,
            spec_algorithm=SpeculativeAlgorithm.NONE,
            speculative_num_draft_tokens=None,
            require_mlp_tp_gather=require_mlp_tp_gather(model_runner.server_args),
            disable_overlap_schedule=model_runner.server_args.disable_overlap_schedule,
        )


def save_profiler_results(profiler, filename, rank_print, detailed_analysis=False):
    """保存 profiler 结果"""
    parent_dir = os.path.dirname(os.path.abspath(filename))
    os.makedirs(parent_dir, exist_ok=True)
    
    # 导出 Chrome trace
    profiler.export_chrome_trace(filename)
    rank_print(f"[Profiler] Chrome trace saved to: {filename}")
    
    # 打印统计信息
    if detailed_analysis:
        rank_print("\n[Profiler] === Detailed Performance Analysis ===")
        rank_print("\nTop CUDA kernels by time:")
        rank_print(profiler.key_averages().table(sort_by="cuda_time_total", row_limit=15))
        
        rank_print("\nTop CPU operations by time:")
        rank_print(profiler.key_averages().table(sort_by="cpu_time_total", row_limit=15))
        
        rank_print("\nOperations grouped by input shape:")
        rank_print(profiler.key_averages(group_by_input_shape=True).table(
            sort_by="self_cpu_time_total", row_limit=10
        ))
    else:
        rank_print("\nTop 5 CUDA kernels:")
        rank_print(profiler.key_averages().table(sort_by="cuda_time_total", row_limit=5))


def synchronize_device(device):
    """设备同步"""
    torch.get_device_module(device).synchronize()


def profiler_benchmark_single_run(
    server_args,
    bench_args,
    model_runner,
    rank_print,
    batch_size,
    input_len,
    output_len,
):
    """单次 profiler 基准测试运行"""
    
    # 检查批次大小限制
    max_batch_size = model_runner.max_total_num_tokens // (input_len + output_len)
    if batch_size > max_batch_size:
        rank_print(f"[Profiler] Skipping ({batch_size}, {input_len}, {output_len}) due to max batch size limit")
        return None

    # 清理池
    model_runner.req_to_token_pool.clear()
    model_runner.token_to_kv_pool_allocator.clear()

    # 准备输入
    reqs = prepare_profiler_inputs(batch_size, input_len)
    
    # 结果记录
    results = {
        "run_name": bench_args.run_name,
        "batch_size": batch_size,
        "input_len": input_len,
        "output_len": output_len,
        "profiler_config": {
            "l2_flush_enabled": bench_args.enable_l2_flush,
            "l2_flush_size_gb": bench_args.l2_flush_size,
            "record_shapes": bench_args.profile_record_shapes,
            "profile_memory": bench_args.profile_memory,
        }
    }

    # 创建输出目录
    os.makedirs(bench_args.profile_output_dir, exist_ok=True)
    
    device = server_args.device
    
    # === Prefill Profiling ===
    if bench_args.profile_all or bench_args.profile_prefill:
        rank_print(f"\n[Profiler] === Prefill Profiling (batch={batch_size}, input_len={input_len}) ===")
        
        # L2 缓存刷新
        flush_l2_cache(bench_args, device, rank_print)
        
        # 预热
        rank_print("[Profiler] Warmup prefill...")
        for _ in range(bench_args.kernel_warmup_runs):
            flush_l2_cache(bench_args, device, rank_print)
            extend_with_profiling(reqs, model_runner)
        
        # Profiling
        profiler_config = create_profiler_config(bench_args)
        profiler = torch.profiler.profile(**profiler_config)
        
        profiler.start()
        
        prefill_times = []
        for i in range(bench_args.kernel_test_runs):
            flush_l2_cache(bench_args, device, rank_print)
            
            synchronize_device(device)
            start_time = time.perf_counter()
            next_token_ids, _, batch = extend_with_profiling(reqs, model_runner)
            synchronize_device(device)
            prefill_time = time.perf_counter() - start_time
            
            prefill_times.append(prefill_time)
            rank_print(f"[Profiler] Prefill run {i+1}: {prefill_time:.4f}s")
        
        profiler.stop()
        
        # 保存 prefill 结果
        prefill_filename = f"{bench_args.profile_output_dir}/{bench_args.profile_filename_prefix}_batch{batch_size}_input{input_len}_output{output_len}_prefill.json"
        save_profiler_results(profiler, prefill_filename, rank_print, bench_args.profile_detail)
        
        # 记录 prefill 统计
        results["prefill"] = {
            "times": prefill_times,
            "avg_time": np.mean(prefill_times),
            "min_time": np.min(prefill_times),
            "max_time": np.max(prefill_times),
            "std_time": np.std(prefill_times),
            "throughput": input_len * batch_size / np.mean(prefill_times),
            "trace_file": prefill_filename,
        }
    
    # === Decode Profiling ===
    if bench_args.profile_all or bench_args.profile_decode:
        rank_print(f"\n[Profiler] === Decode Profiling (batch={batch_size}) ===")
        
        # 重新准备 prefill（为 decode 准备状态）
        model_runner.req_to_token_pool.clear()
        model_runner.token_to_kv_pool_allocator.clear()
        reqs = prepare_profiler_inputs(batch_size, input_len)
        next_token_ids, _, batch = extend_with_profiling(reqs, model_runner)
        
        # L2 缓存刷新
        flush_l2_cache(bench_args, device, rank_print)
        
        # 预热 decode
        rank_print("[Profiler] Warmup decode...")
        for _ in range(bench_args.kernel_warmup_runs):
            flush_l2_cache(bench_args, device, rank_print)
            decode_with_profiling(next_token_ids, batch, model_runner)
        
        # Profiling decode
        profiler_config = create_profiler_config(bench_args)
        profiler = torch.profiler.profile(**profiler_config)
        
        profiler.start()
        
        decode_times = []
        for i in range(bench_args.kernel_test_runs):
            flush_l2_cache(bench_args, device, rank_print)
            
            synchronize_device(device)
            start_time = time.perf_counter()
            next_token_ids, _ = decode_with_profiling(next_token_ids, batch, model_runner)
            synchronize_device(device)
            decode_time = time.perf_counter() - start_time
            
            decode_times.append(decode_time)
            rank_print(f"[Profiler] Decode run {i+1}: {decode_time:.4f}s")
        
        profiler.stop()
        
        # 保存 decode 结果
        decode_filename = f"{bench_args.profile_output_dir}/{bench_args.profile_filename_prefix}_batch{batch_size}_input{input_len}_output{output_len}_decode.json"
        save_profiler_results(profiler, decode_filename, rank_print, bench_args.profile_detail)
        
        # 记录 decode 统计
        results["decode"] = {
            "times": decode_times,
            "avg_time": np.mean(decode_times),
            "min_time": np.min(decode_times),
            "max_time": np.max(decode_times),
            "std_time": np.std(decode_times),
            "throughput": batch_size / np.mean(decode_times),
            "trace_file": decode_filename,
        }

    return results


def profiler_benchmark_main(
    server_args,
    port_args,
    bench_args,
    tp_rank,
):
    """主要的 profiler 基准测试函数"""
    
    # 设置 CPU 亲和性
    if get_bool_env_var("SGLANG_SET_CPU_AFFINITY"):
        set_gpu_proc_affinity(server_args.tp_size, server_args.nnodes, tp_rank)

    # 配置日志
    configure_logger(server_args, prefix=f" TP{tp_rank}")
    rank_print = print if tp_rank == 0 else lambda *args, **kwargs: None

    # 设置 Nsight Systems 环境变量
    if bench_args.enable_nsys:
        os.environ["SGLANG_NSYS_PROFILING"] = "1"
        rank_print("[Profiler] Nsight Systems profiling enabled")

    # 加载模型
    rank_print("[Profiler] Loading model for profiling...")
    model_runner, tokenizer = load_model_for_profiling(server_args, port_args, tp_rank)

    # 全局预热
    rank_print("[Profiler] Global warmup...")
    warmup_reqs = prepare_profiler_inputs(1, 128)
    extend_with_profiling(warmup_reqs, model_runner)

    rank_print("[Profiler] Starting benchmark sweep...")

    # 运行参数扫描
    all_results = []
    for bs, il, ol in itertools.product(
        bench_args.batch_size, bench_args.input_len, bench_args.output_len
    ):
        rank_print(f"\n[Profiler] === Running benchmark: batch={bs}, input_len={il}, output_len={ol} ===")
        
        result = profiler_benchmark_single_run(
            server_args=server_args,
            bench_args=bench_args,
            model_runner=model_runner,
            rank_print=rank_print,
            batch_size=bs,
            input_len=il,
            output_len=ol,
        )
        
        if result is not None:
            all_results.append(result)

    # 保存结果
    if tp_rank == 0 and bench_args.result_filename:
        with open(bench_args.result_filename, "w") as f:
            for result in all_results:
                f.write(json.dumps(result, indent=2) + "\n")
        rank_print(f"[Profiler] Results saved to: {bench_args.result_filename}")

    # 打印总结
    rank_print(f"\n[Profiler] === Benchmark Summary ===")
    rank_print(f"Total runs completed: {len(all_results)}")
    rank_print(f"Profile traces saved in: {bench_args.profile_output_dir}")
    
    if server_args.tp_size > 1:
        destroy_distributed_environment()


def main(server_args, bench_args):
    """主函数"""
    server_args.cuda_graph_max_bs = max(bench_args.batch_size)
    
    # 强制禁用 CUDA Graph 以便观察详细的 kernel 调用
    server_args.disable_cuda_graph = True
    
    _set_envs_and_config(server_args)

    if not server_args.model_path:
        raise ValueError("必须提供 --model-path 参数")

    port_args = PortArgs.init_new(server_args)

    if server_args.tp_size == 1:
        profiler_benchmark_main(server_args, port_args, bench_args, 0)
    else:
        workers = []
        for tp_rank in range(server_args.tp_size):
            proc = multiprocessing.Process(
                target=profiler_benchmark_main,
                args=(server_args, port_args, bench_args, tp_rank),
            )
            proc.start()
            workers.append(proc)

        for proc in workers:
            proc.join()


if __name__ == "__main__":
    import sys
    
    parser = argparse.ArgumentParser(description="SGLang Profiler 专门测试脚本")
    ServerArgs.add_cli_args(parser)
    ProfilerBenchArgs.add_cli_args(parser)
    args = parser.parse_args()
    
    server_args = ServerArgs.from_cli_args(args)
    bench_args = ProfilerBenchArgs.from_cli_args(args)

    logging.basicConfig(
        level=getattr(logging, server_args.log_level.upper()),
        format="%(message)s",
    )

    try:
        main(server_args, bench_args)
    finally:
        if server_args.tp_size != 1:
            kill_process_tree(os.getpid(), include_parent=False)
