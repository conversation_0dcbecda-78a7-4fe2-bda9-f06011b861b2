# SGLang 模型 Profiler 文档 (vLLM 风格)

## 脚本概述

`sglang_model_profiler.py` 是一个仿照 vLLM 的 `benchmark_latency.py` 风格创建的 SGLang 模型性能测试工具。它提供了专业的性能 profiling 功能，包括延迟统计、吞吐量测试和百分位延迟分析。

## 主要特性

### 1. vLLM 风格的 API 设计
- 使用 vLLM 相似的命令行参数结构
- 支持 vLLM 风格的 EngineArgs 参数传递
- 提供与 vLLM benchmark_latency.py 类似的输出格式

### 2. 延迟测试功能
- 支持多轮次预热和测试
- 提供详细的延迟统计 (平均值、标准差、百分位数)
- 计算总体吞吐量、Prefill 等效吞吐量、Decode 等效吞吐量

### 3. Profiling 支持
- 支持 PyTorch Profiler 集成
- 可选择生成 Chrome Trace 格式输出
- 支持 TensorBoard 日志记录
- 提供 JSON 格式的结果输出

### 4. 批处理和参数扫描
- 支持多种输入长度测试
- 支持多种输出长度测试  
- 支持多种批次大小测试
- 提供详细的配置记录

## 使用方法

### 基础延迟测试
```bash
python sglang_model_profiler.py \
    --model-path /home/<USER>/deepseek-int8 \
    --quantization w8a8_int8 \
    --input-len 128 \
    --output-len 16 \
    --batch-size 1 \
    --num-iters-warmup 2 \
    --num-iters 3 \
    --trust-remote-code \
    --output-json test_results.json
```

### 启用 Chrome Trace Profiling
```bash
python sglang_model_profiler.py \
    --model-path /home/<USER>/deepseek-int8 \
    --quantization w8a8_int8 \
    --input-len 128 \
    --output-len 16 \
    --batch-size 1 \
    --profile \
    --profile-result-dir ./profiler_output \
    --num-iters 5 \
    --trust-remote-code
```

### 多参数扫描测试
```bash
python sglang_model_profiler.py \
    --model-path /home/<USER>/deepseek-int8 \
    --quantization w8a8_int8 \
    --input-len 64 128 256 \
    --output-len 16 32 \
    --batch-size 1 2 4 \
    --num-iters 5 \
    --trust-remote-code \
    --output-json sweep_results.json
```

## 实际运行结果

### 测试配置
- 模型: DeepSeek-V3 INT8 (/home/<USER>/deepseek-int8)
- 量化方式: w8a8_int8
- 输入长度: 128 tokens
- 输出长度: 16 tokens
- 批次大小: 1
- 预热迭代: 2 次
- 测试迭代: 3 次

### 性能指标
```
平均延迟: 0.0918 秒
标准差: 0.0051 秒
10% 百分位延迟: 0.0878 秒
25% 百分位延迟: 0.0883 秒
50% 百分位延迟: 0.0890 秒
75% 百分位延迟: 0.0940 秒
90% 百分位延迟: 0.0970 秒
99% 百分位延迟: 0.0988 秒

总体吞吐量: 1567.88 tokens/s
Prefill 等效吞吐量: 1393.67 tokens/s
Decode 等效吞吐量: 174.21 tokens/s
```

### JSON 输出格式
```json
{
  "config": {
    "model_path": "/home/<USER>/deepseek-int8",
    "quantization": "w8a8_int8",
    "input_len": 128,
    "output_len": 16,
    "batch_size": 1,
    "tp_size": 1
  },
  "results": {
    "avg_latency": 0.09184369766929497,
    "std_latency": 0.005078431197630898,
    "latencies": [0.08898, 0.08756, 0.09898],
    "percentiles": {
      "p10": 0.08785, "p25": 0.08828, "p50": 0.08899,
      "p75": 0.09398, "p90": 0.09698, "p99": 0.09878
    },
    "throughput": {
      "total_tokens_per_sec": 1567.88,
      "prefill_equivalent_tokens_per_sec": 1393.67,
      "decode_equivalent_tokens_per_sec": 174.21
    }
  }
}
```

## 核心优势

### 1. vLLM 兼容性
- 使用熟悉的 vLLM 风格参数和接口
- 便于从 vLLM 迁移到 SGLang 时的性能对比
- 统一的 benchmark 体验

### 2. 专业性能分析
- 提供详细的百分位延迟分析
- 区分 Prefill 和 Decode 阶段吞吐量
- 支持多种配置的系统性测试

### 3. 易用性
- 命令行参数清晰易懂
- 输出格式标准化
- 支持批处理和自动化测试

### 4. 扩展性
- 支持多种模型和量化方式
- 支持分布式训练参数
- 可轻松集成到 CI/CD 流程中

## 注意事项

1. **内存管理**: 使用大批次大小时注意 GPU 内存使用情况
2. **预热重要性**: 建议至少进行 2-3 次预热迭代以获得稳定结果
3. **Profiling 开销**: 启用 profiling 会增加运行时间，建议在性能测试时禁用
4. **MoE 配置**: DeepSeek-V3 等 MoE 模型可能需要特定的 kernel 配置文件以获得最佳性能

## 脚本特点

这个脚本成功地将 vLLM 的 benchmark_latency.py 的设计理念移植到了 SGLang，提供了：
- 统一的命令行接口
- 标准化的性能指标
- 专业的 profiling 支持
- 便于自动化的 JSON 输出

是进行 SGLang 模型性能测试和对比分析的理想工具。
