#!/bin/bash
# SGLang 算子 Profiler 使用示例

echo "=== SGLang 算子 Profiler 使用示例 ==="

# 基础配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROFILER_SCRIPT="$SCRIPT_DIR/sglang_operator_profiler.py"

# 确保在正确的虚拟环境中
source /workspace/sglang_test/bin/activate

echo "正在运行算子性能监控示例..."

# 示例 1: 测试 Attention 算子
echo -e "\n📍 示例 1: Attention 算子性能测试"
python "$PROFILER_SCRIPT" \
    --operator attention \
    --batch-size 4 \
    --seq-len 1024 \
    --num-heads 32 \
    --head-size 128 \
    --num-iters 50 \
    --dtype bfloat16 \
    --verbose

# 示例 2: 测试 MoE 算子
echo -e "\n📍 示例 2: MoE 算子性能测试"
python "$PROFILER_SCRIPT" \
    --operator moe \
    --batch-size 2 \
    --seq-len 512 \
    --num-experts 8 \
    --topk 2 \
    --hidden-size 4096 \
    --intermediate-size 11008 \
    --num-iters 30 \
    --dtype bfloat16 \
    --verbose

# 示例 3: 测试 LayerNorm 算子
echo -e "\n📍 示例 3: LayerNorm 算子性能测试"
python "$PROFILER_SCRIPT" \
    --operator layernorm \
    --batch-size 8 \
    --seq-len 2048 \
    --hidden-size 4096 \
    --num-iters 100 \
    --dtype bfloat16 \
    --verbose

# 示例 4: 启用 Profiling 的详细测试
echo -e "\n📍 示例 4: 启用 Profiling 的详细测试 (Attention)"
python "$PROFILER_SCRIPT" \
    --operator attention \
    --batch-size 2 \
    --seq-len 512 \
    --num-heads 16 \
    --head-size 64 \
    --num-iters 20 \
    --profile \
    --profile-dir "./attention_profiling_$(date +%Y%m%d_%H%M%S)" \
    --export-chrome-trace \
    --export-tensorboard \
    --verbose

# 示例 5: 自动检测所有算子
echo -e "\n📍 示例 5: 自动检测和测试所有算子"
python "$PROFILER_SCRIPT" \
    --operator auto \
    --batch-size 1 \
    --seq-len 256 \
    --num-iters 20 \
    --verbose

# 示例 6: 与真实模型集成的自动测试
echo -e "\n📍 示例 6: 与 DeepSeek INT8 模型集成的自动测试"
python "$PROFILER_SCRIPT" \
    --operator auto \
    --model-path /home/<USER>/deepseek-int8 \
    --quantization w8a8_int8 \
    --trust-remote-code \
    --batch-size 1 \
    --seq-len 128 \
    --num-iters 10 \
    --profile \
    --verbose

# 示例 7: 性能对比测试 (不同数据类型)
echo -e "\n📍 示例 7: 性能对比测试 - float16"
python "$PROFILER_SCRIPT" \
    --operator attention \
    --batch-size 4 \
    --seq-len 1024 \
    --dtype float16 \
    --num-iters 50 \
    --output-json "./attention_fp16_results.json" \
    --verbose

echo -e "\n📍 示例 7: 性能对比测试 - bfloat16"
python "$PROFILER_SCRIPT" \
    --operator attention \
    --batch-size 4 \
    --seq-len 1024 \
    --dtype bfloat16 \
    --num-iters 50 \
    --output-json "./attention_bf16_results.json" \
    --verbose

# 示例 8: 大规模测试
echo -e "\n📍 示例 8: 大规模算子性能测试"
python "$PROFILER_SCRIPT" \
    --operator moe \
    --batch-size 16 \
    --seq-len 4096 \
    --num-experts 64 \
    --topk 8 \
    --hidden-size 8192 \
    --intermediate-size 22016 \
    --num-iters 20 \
    --profile \
    --verbose

echo -e "\n✅ 所有示例运行完成！"
echo "查看生成的结果文件："
echo "  - JSON 结果: ./attention_*_results.json"
echo "  - Profiling 目录: ./attention_profiling_* 和 ./sglang_operator_profiling_*"
echo "  - 性能报告: */performance_summary.md"

echo -e "\n📊 分析结果的建议："
echo "1. 使用 Chrome 打开 *_chrome.json 查看详细的算子调用"
echo "2. 使用 'tensorboard --logdir <profiling_dir>' 查看 TensorBoard 日志"
echo "3. 比较不同配置的 JSON 结果文件"
echo "4. 阅读 performance_summary.md 获取快速概览"
