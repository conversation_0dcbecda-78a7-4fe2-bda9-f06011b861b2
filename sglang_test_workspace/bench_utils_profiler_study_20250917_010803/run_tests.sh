#!/bin/bash
# SGLang Bench Utils Profiler 测试脚本

set -e

echo "=== SGLang Bench Utils Profiler 测试脚本 ==="
echo "开始时间: $(date)"
echo ""

# 激活环境
source /workspace/sglang_test/bin/activate

# 确保在正确的目录
cd /workspace/sglang_test_workspace/bench_utils_profiler_study_20250917_010803

echo "1. 测试修复后的原始离线推理代码..."
echo "-------------------------------------------"
python fixed_offline_sglang_generate.py
echo ""

echo "2. 测试改进版的基准测试代码..."
echo "-------------------------------------------"
python improved_offline_sglang_generate.py
echo ""

echo "=== 测试完成 ==="
echo "结束时间: $(date)"
echo ""
echo "生成的文件:"
echo "- 分析文档: bench_utils_analysis.md"
echo "- 修复版代码: fixed_offline_sglang_generate.py"
echo "- 改进版代码: improved_offline_sglang_generate.py"
echo ""
echo "Profile traces 保存在: /workspace/sglang_test_workspace/profile_logs/"
ls -la /workspace/sglang_test_workspace/profile_logs/
