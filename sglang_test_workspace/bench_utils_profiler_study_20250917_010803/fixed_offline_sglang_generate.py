#!/usr/bin/env python3
import os
import sys
import traceback
import time
import torch

# 必须在导入 sglang 前设置分布式/通信相关环境
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

# 设置profiling环境变量
os.environ.setdefault("SGLANG_TORCH_PROFILER_DIR", "/workspace/sglang_test_workspace/profile_logs")
os.environ.setdefault("SGLANG_PROFILE_WITH_STACK", "True")

import sglang as sgl

MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")


def main():
    # 创建profiler输出目录
    profile_dir = "/workspace/sglang_test_workspace/profile_logs"
    os.makedirs(profile_dir, exist_ok=True)
    
    # 直接离线引擎加载（不启动HTTP服务）
    llm = None
    profiler = None
    
    try:
        print("[offline] init Engine ...")  # 修复：移除了多余的 "sss"
        llm = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=1,
            quantization="w8a8_int8",
            trust_remote_code=True,
            dist_init_addr="127.0.0.1:8001",
            disable_cuda_graph=True,  # 禁用CUDA Graph以便看到详细的kernel调用
            log_level="info",
        )

        prompt = "用一句话介绍你自己。"
        sampling_params = {"max_new_tokens": 32, "temperature": 0.7}
        
        # 启动PyTorch Profiler
        print("[offline] Starting PyTorch Profiler...")
        profiler = torch.profiler.profile(
            activities=[
                torch.profiler.ProfilerActivity.CPU,
                torch.profiler.ProfilerActivity.CUDA,
            ],
            record_shapes=True,
            profile_memory=True,
            with_stack=True,
            with_flops=True,
            with_modules=True,
        )
        
        profiler.start()
        
        print("[offline] generate ...")
        out = llm.generate(prompt=prompt, sampling_params=sampling_params)
        print("Generate OK\n---\n", out.get("text", out))
        
        # 停止profiler并导出结果
        profiler.stop()
        
        # 导出Chrome trace格式
        timestamp = int(time.time())
        trace_file = f"{profile_dir}/offline_sglang_trace_{timestamp}.json"
        profiler.export_chrome_trace(trace_file)
        print(f"[offline] Profile trace saved to: {trace_file}")
        
        # 打印性能统计信息
        print("\n[offline] Top 10 CUDA kernels by time:")
        print(profiler.key_averages().table(sort_by="cuda_time_total", row_limit=10))
        
        print("\n[offline] Top 10 CPU operations by time:")
        print(profiler.key_averages().table(sort_by="cpu_time_total", row_limit=10))
        
    except Exception:
        print("[offline] ERROR:\n" + traceback.format_exc())
        sys.exit(1)
    finally:
        if profiler is not None:
            try:
                profiler.stop()
            except Exception:
                pass
        if llm is not None:
            try:
                llm.shutdown()
            except Exception:
                pass


if __name__ == "__main__":
    main()
