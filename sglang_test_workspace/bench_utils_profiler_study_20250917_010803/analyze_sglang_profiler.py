#!/usr/bin/env python3
"""
SGLang 算子 Profiler 结果分析脚本

读取和分析 sglang_operator_profiler.py 生成的结果，提供详细的性能分析和可视化。
"""

import argparse
import json
import os
import sys
from pathlib import Path
from typing import Dict, Any, List

import numpy as np


def load_results(json_path: str) -> Dict[str, Any]:
    """加载 JSON 结果文件"""
    with open(json_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def analyze_single_operator(name: str, data: Dict[str, Any]) -> str:
    """分析单个算子的性能数据"""
    stats = data['latency_stats']
    config = data['config']
    throughput = data['throughput']
    
    analysis = f"""
## {name.upper()} 算子性能分析

### 配置信息
- 批次大小: {config['batch_size']}
- 序列长度: {config['seq_len']}
- 隐藏层大小: {config['hidden_size']}
- 数据类型: {config['dtype']}

### 延迟统计 (毫秒)
- 平均延迟: {stats['mean_ms']:.3f} ms
- 标准差: {stats['std_ms']:.3f} ms
- 最小延迟: {stats['min_ms']:.3f} ms
- 最大延迟: {stats['max_ms']:.3f} ms
- P50 (中位数): {stats['p50_ms']:.3f} ms
- P95: {stats['p95_ms']:.3f} ms
- P99: {stats['p99_ms']:.3f} ms

### 吞吐量
- 每秒操作数: {throughput['ops_per_sec']:.2f} ops/s

### 性能稳定性
- 变异系数 (CV): {(stats['std_ms'] / stats['mean_ms'] * 100):.2f}%
- 延迟范围: {(stats['max_ms'] - stats['min_ms']):.3f} ms

"""
    return analysis


def compare_operators(results: Dict[str, Any]) -> str:
    """比较不同算子的性能"""
    if len(results) < 2:
        return "## 算子性能对比\n\n需要至少两个算子进行对比分析。\n"
    
    # 提取关键指标
    operators = []
    for name, data in results.items():
        if 'latency_stats' in data:  # 确保是有效的算子数据
            stats = data['latency_stats']
            throughput = data['throughput']
            operators.append({
                'name': name,
                'mean_latency': stats['mean_ms'],
                'std_latency': stats['std_ms'],
                'p95_latency': stats['p95_ms'],
                'throughput': throughput['ops_per_sec'],
                'cv': stats['std_ms'] / stats['mean_ms'] * 100
            })
    
    if not operators:
        return "## 算子性能对比\n\n没有找到有效的算子性能数据。\n"
    
    # 按平均延迟排序
    operators.sort(key=lambda x: x['mean_latency'])
    
    comparison = """
## 算子性能对比

### 延迟排行榜 (低延迟 = 更好)
| 排名 | 算子 | 平均延迟 (ms) | 标准差 (ms) | P95 延迟 (ms) | 变异系数 (%) |
|------|------|---------------|-------------|---------------|--------------|
"""
    
    for i, op in enumerate(operators, 1):
        comparison += f"| {i} | {op['name'].upper()} | {op['mean_latency']:.3f} | {op['std_latency']:.3f} | {op['p95_latency']:.3f} | {op['cv']:.2f}% |\n"
    
    # 按吞吐量排序
    operators.sort(key=lambda x: x['throughput'], reverse=True)
    
    comparison += """

### 吞吐量排行榜 (高吞吐量 = 更好)
| 排名 | 算子 | 吞吐量 (ops/s) | 相对性能 |
|------|------|----------------|----------|
"""
    
    max_throughput = operators[0]['throughput']
    for i, op in enumerate(operators, 1):
        relative_perf = op['throughput'] / max_throughput * 100
        comparison += f"| {i} | {op['name'].upper()} | {op['throughput']:.2f} | {relative_perf:.1f}% |\n"
    
    # 性能稳定性分析
    comparison += """

### 性能稳定性分析
"""
    
    stable_ops = [op for op in operators if op['cv'] < 5.0]
    unstable_ops = [op for op in operators if op['cv'] >= 10.0]
    
    if stable_ops:
        comparison += f"\n**稳定算子** (变异系数 < 5%):\n"
        for op in stable_ops:
            comparison += f"- {op['name'].upper()}: {op['cv']:.2f}%\n"
    
    if unstable_ops:
        comparison += f"\n**不稳定算子** (变异系数 >= 10%):\n"
        for op in unstable_ops:
            comparison += f"- {op['name'].upper()}: {op['cv']:.2f}%\n"
    
    return comparison


def generate_optimization_suggestions(results: Dict[str, Any]) -> str:
    """生成优化建议"""
    suggestions = """
## 性能优化建议

"""
    
    for name, data in results.items():
        if 'latency_stats' not in data:
            continue
            
        stats = data['latency_stats']
        config = data['config']
        cv = stats['std_ms'] / stats['mean_ms'] * 100
        
        suggestions += f"### {name.upper()} 算子\n"
        
        # 基于算子类型的建议
        if name == 'attention':
            if stats['mean_ms'] > 5.0:
                suggestions += "- **建议**: 考虑使用 FlashAttention 或其他优化的 attention 实现\n"
            if config['seq_len'] > 1024:
                suggestions += "- **建议**: 对于长序列，考虑使用分块 attention\n"
                
        elif name == 'moe':
            if stats['mean_ms'] > 100.0:
                suggestions += "- **建议**: MoE 算子延迟较高，考虑以下优化:\n"
                suggestions += "  - 使用更高效的专家路由算法\n"
                suggestions += "  - 优化专家权重的内存布局\n"
                suggestions += "  - 考虑专家并行化\n"
                
        elif name == 'layernorm':
            if stats['mean_ms'] > 2.0:
                suggestions += "- **建议**: LayerNorm 可以通过 kernel fusion 优化\n"
                
        elif name == 'rope':
            if cv > 10.0:
                suggestions += "- **建议**: RoPE 性能不稳定，检查内存访问模式\n"
                
        elif name == 'quantization':
            if stats['mean_ms'] > 3.0:
                suggestions += "- **建议**: 量化算子可能需要优化，考虑使用专门的量化库\n"
        
        # 通用建议
        if cv > 15.0:
            suggestions += f"- **警告**: 性能变异系数较高 ({cv:.1f}%)，需要进一步调查\n"
        
        if config['batch_size'] == 1:
            suggestions += "- **提示**: 批次大小为1，考虑增加批次大小以提高吞吐量\n"
        
        suggestions += "\n"
    
    return suggestions


def generate_detailed_report(results: Dict[str, Any], output_path: str):
    """生成详细的分析报告"""
    report = f"""# SGLang 算子性能分析报告

**生成时间**: {Path(output_path).parent.name}
**分析的算子数量**: {len([k for k, v in results.items() if 'latency_stats' in v])}

---

"""
    
    # 执行摘要
    valid_results = {k: v for k, v in results.items() if 'latency_stats' in v}
    if valid_results:
        fastest_op = min(valid_results.items(), key=lambda x: x[1]['latency_stats']['mean_ms'])
        slowest_op = max(valid_results.items(), key=lambda x: x[1]['latency_stats']['mean_ms'])
        
        report += f"""## 执行摘要

- **最快算子**: {fastest_op[0].upper()} ({fastest_op[1]['latency_stats']['mean_ms']:.3f} ms)
- **最慢算子**: {slowest_op[0].upper()} ({slowest_op[1]['latency_stats']['mean_ms']:.3f} ms)
- **性能差异**: {slowest_op[1]['latency_stats']['mean_ms'] / fastest_op[1]['latency_stats']['mean_ms']:.1f}x

---

"""
    
    # 详细分析
    for name, data in results.items():
        if 'latency_stats' in data:
            report += analyze_single_operator(name, data)
            report += "---\n\n"
    
    # 对比分析
    report += compare_operators(valid_results)
    report += "\n---\n\n"
    
    # 优化建议
    report += generate_optimization_suggestions(valid_results)
    
    # 保存报告
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(report)


def main():
    parser = argparse.ArgumentParser(description='分析 SGLang 算子 profiler 结果')
    parser.add_argument('json_file', help='JSON 结果文件路径')
    parser.add_argument('--output', '-o', help='输出分析报告路径')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.json_file):
        print(f"错误: 文件 {args.json_file} 不存在")
        sys.exit(1)
    
    # 加载结果
    try:
        results = load_results(args.json_file)
    except Exception as e:
        print(f"错误: 无法加载 JSON 文件: {e}")
        sys.exit(1)
    
    # 确定输出路径
    if args.output:
        output_path = args.output
    else:
        json_path = Path(args.json_file)
        output_path = json_path.parent / f"analysis_report_{json_path.stem}.md"
    
    # 生成报告
    try:
        generate_detailed_report(results, output_path)
        print(f"✅ 分析报告已生成: {output_path}")
        
        if args.verbose:
            print(f"\n📊 快速预览:")
            valid_results = {k: v for k, v in results.items() if 'latency_stats' in v}
            for name, data in valid_results.items():
                stats = data['latency_stats']
                print(f"  {name.upper()}: {stats['mean_ms']:.3f} ± {stats['std_ms']:.3f} ms")
        
    except Exception as e:
        print(f"错误: 生成报告时出现问题: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
