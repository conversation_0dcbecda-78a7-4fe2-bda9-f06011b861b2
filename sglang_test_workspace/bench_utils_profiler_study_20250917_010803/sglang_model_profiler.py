#!/usr/bin/env python3
"""
SGLang Model Profiler - 参考 vLLM benchmark_latency.py 的 profiler 使用方式

基于 vLLM benchmark_latency.py 的设计模式，适配 SGLang 框架进行模型性能分析。
支持 TensorBoard 和 Chrome trace 两种 profiler 输出格式。

# 使用示例：
## 基础延迟测试:
python sglang_model_profiler.py --model-path /home/<USER>/deepseek-int8 --quantization w8a8_int8 --input-len 256 --output-len 32 --batch-size 1

## 启用 profiling (TensorBoard 格式):
python sglang_model_profiler.py --model-path /home/<USER>/deepseek-int8 --quantization w8a8_int8 --input-len 256 --output-len 32 --batch-size 1 --profile

## 启用 profiling 并指定输出目录:
python sglang_model_profiler.py --model-path /home/<USER>/deepseek-int8 --quantization w8a8_int8 --input-len 256 --output-len 32 --batch-size 1 --profile --profile-result-dir ./profiler_results

## JSON 结果输出:
python sglang_model_profiler.py --model-path /home/<USER>/deepseek-int8 --quantization w8a8_int8 --input-len 256 --output-len 32 --batch-size 1 --output-json results.json
"""

import argparse
import dataclasses
import json
import os
import sys
import time
from pathlib import Path
from typing import List, Optional

import numpy as np
import torch
from tqdm import tqdm

import sglang as sgl
from sglang.srt.server_args import ServerArgs
from sglang.srt.utils import configure_logger, suppress_other_loggers


@dataclasses.dataclass 
class ModelProfilerArgs:
    """模型 Profiler 参数配置"""
    # 基础参数
    input_len: int = 256
    output_len: int = 32  
    batch_size: int = 1
    n: int = 1  # 每个 prompt 生成的序列数量
    
    # 测试参数
    num_iters_warmup: int = 5
    num_iters: int = 10
    
    # Profiling 参数
    profile: bool = False
    profile_result_dir: Optional[str] = None
    profile_chrome_trace: bool = True  # 是否同时生成 Chrome trace
    
    # 输出参数
    output_json: Optional[str] = None
    verbose: bool = True

    @staticmethod
    def add_cli_args(parser: argparse.ArgumentParser):
        parser.add_argument('--input-len', type=int, default=ModelProfilerArgs.input_len,
                          help='输入序列长度')
        parser.add_argument('--output-len', type=int, default=ModelProfilerArgs.output_len,
                          help='输出序列长度')
        parser.add_argument('--batch-size', type=int, default=ModelProfilerArgs.batch_size,
                          help='批次大小')
        parser.add_argument('--n', type=int, default=ModelProfilerArgs.n,
                          help='每个 prompt 生成的序列数量')
        
        parser.add_argument('--num-iters-warmup', type=int, default=ModelProfilerArgs.num_iters_warmup,
                          help='预热迭代次数')
        parser.add_argument('--num-iters', type=int, default=ModelProfilerArgs.num_iters,
                          help='测试迭代次数')
        
        parser.add_argument('--profile', action='store_true',
                          help='启用 profiling（生成 TensorBoard 格式）')
        parser.add_argument('--profile-result-dir', type=str, default=ModelProfilerArgs.profile_result_dir,
                          help='profiler 结果保存目录')
        parser.add_argument('--profile-chrome-trace', action='store_true', default=True,
                          help='同时生成 Chrome trace 格式')
        
        parser.add_argument('--output-json', type=str, default=ModelProfilerArgs.output_json,
                          help='JSON 结果输出路径')
        parser.add_argument('--verbose', action='store_true', default=True,
                          help='详细输出')

    @classmethod
    def from_cli_args(cls, args: argparse.Namespace):
        return cls(
            input_len=args.input_len,
            output_len=args.output_len,
            batch_size=args.batch_size,
            n=args.n,
            num_iters_warmup=args.num_iters_warmup,
            num_iters=args.num_iters,
            profile=args.profile,
            profile_result_dir=args.profile_result_dir,
            profile_chrome_trace=args.profile_chrome_trace,
            output_json=args.output_json,
            verbose=args.verbose,
        )


class SGLangModelProfiler:
    """SGLang 模型 Profiler 类"""
    
    def __init__(self, server_args: ServerArgs, profiler_args: ModelProfilerArgs):
        self.server_args = server_args
        self.profiler_args = profiler_args
        self.engine = None
        
        # 配置日志
        if profiler_args.verbose:
            configure_logger(server_args)
        else:
            suppress_other_loggers()
    
    def initialize_engine(self):
        """初始化 SGLang 引擎"""
        print("正在初始化 SGLang 引擎...")
        
        # 设置环境变量以禁用某些功能来获得更清晰的 profiling 结果
        os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
        os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")
        
        self.engine = sgl.Engine(
            model_path=self.server_args.model_path,
            tp_size=self.server_args.tp_size,
            quantization=self.server_args.quantization,
            trust_remote_code=self.server_args.trust_remote_code,
            disable_cuda_graph=True,  # 禁用 CUDA Graph 以便观察详细的 kernel 调用
            log_level="info" if self.profiler_args.verbose else "error",
        )
        
        print(f"引擎初始化完成")
        print(f"模型路径: {self.server_args.model_path}")
        print(f"量化方式: {self.server_args.quantization}")
        print(f"Tensor 并行度: {self.server_args.tp_size}")
    
    def prepare_dummy_inputs(self) -> List[str]:
        """准备虚拟输入数据，类似 vLLM 的做法"""
        # 使用固定随机种子确保可重现性
        np.random.seed(42)
        
        # 生成随机 token IDs（类似 vLLM benchmark）
        dummy_token_ids = np.random.randint(
            1000, 10000,  # 避免特殊 token
            size=(self.profiler_args.batch_size, self.profiler_args.input_len)
        )
        
        # 将 token IDs 转换为简单文本（用于 SGLang）
        dummy_prompts = []
        for i in range(self.profiler_args.batch_size):
            # 创建一个简单的重复模式作为输入
            prompt = "测试文本 " * (self.profiler_args.input_len // 4 + 1)
            prompt = prompt[:self.profiler_args.input_len]  # 截断到指定长度
            dummy_prompts.append(prompt)
        
        return dummy_prompts
    
    def create_sampling_params(self) -> dict:
        """创建采样参数"""
        return {
            "max_new_tokens": self.profiler_args.output_len,
            "temperature": 1.0,
            "top_p": 1.0,
            "stop": None,  # 不使用 stop token 以确保生成完整长度
        }
    
    def run_generation(self, prompts: List[str]) -> List[str]:
        """运行生成任务"""
        sampling_params = self.create_sampling_params()
        
        results = []
        for prompt in prompts:
            output = self.engine.generate(
                prompt=prompt,
                sampling_params=sampling_params
            )
            results.append(output.get("text", ""))
        
        return results
    
    def run_to_completion(self, prompts: List[str], profile_dir: Optional[str] = None) -> Optional[float]:
        """运行到完成，可选地进行 profiling"""
        
        if profile_dir:
            # 生成有意义的 trace 文件名（参考 vLLM 的命名方式）
            model_name = Path(self.server_args.model_path).name
            input_len = self.profiler_args.input_len
            output_len = self.profiler_args.output_len
            batch_size = self.profiler_args.batch_size
            quantization = self.server_args.quantization or "none"
            
            trace_filename_prefix = f"{model_name}_IL{input_len}_OL{output_len}_BS{batch_size}_quant{quantization}"
            
            # 创建 profiler（参考 vLLM 的配置）
            with torch.profiler.profile(
                activities=[
                    torch.profiler.ProfilerActivity.CPU,
                    torch.profiler.ProfilerActivity.CUDA,
                ],
                with_stack=True,
                record_shapes=True,
                on_trace_ready=torch.profiler.tensorboard_trace_handler(
                    str(profile_dir), 
                    worker_name=trace_filename_prefix
                )
            ) as profiler:
                self.run_generation(prompts)
            
            # 打印性能统计表格（参考 vLLM 的输出）
            if self.profiler_args.verbose:
                print("\n=== PyTorch Profiler 统计结果 ===")
                print(profiler.key_averages().table(sort_by="self_cuda_time_total", row_limit=10))
            
            # 同时生成 Chrome trace 格式（如果启用）
            if self.profiler_args.profile_chrome_trace:
                chrome_trace_path = Path(profile_dir) / f"{trace_filename_prefix}_chrome.json"
                profiler.export_chrome_trace(str(chrome_trace_path))
                print(f"Chrome trace 已保存到: {chrome_trace_path}")
            
            return None
        else:
            # 普通延迟测试
            start_time = time.perf_counter()
            self.run_generation(prompts)
            end_time = time.perf_counter()
            return end_time - start_time
    
    def run_benchmark(self):
        """运行完整的基准测试"""
        print(f"\n=== SGLang 模型性能测试 ===")
        print(f"输入长度: {self.profiler_args.input_len}")
        print(f"输出长度: {self.profiler_args.output_len}")
        print(f"批次大小: {self.profiler_args.batch_size}")
        print(f"生成序列数: {self.profiler_args.n}")
        
        # 准备输入数据
        dummy_prompts = self.prepare_dummy_inputs()
        
        # 预热阶段
        print(f"\n正在预热 ({self.profiler_args.num_iters_warmup} 次迭代)...")
        for i in tqdm(range(self.profiler_args.num_iters_warmup), desc="预热迭代"):
            self.run_to_completion(dummy_prompts, profile_dir=None)
        
        # Profiling 模式
        if self.profiler_args.profile:
            profile_dir = self.profiler_args.profile_result_dir
            if not profile_dir:
                profile_dir = Path(".") / "sglang_profiler_results" / f"profile_{int(time.time())}"
            
            print(f"\n正在进行 Profiling (结果将保存到 '{profile_dir}')...")
            os.makedirs(profile_dir, exist_ok=True)
            self.run_to_completion(dummy_prompts, profile_dir=profile_dir)
            
            print(f"Profiling 完成！结果已保存到: {profile_dir}")
            print(f"可以使用以下命令查看 TensorBoard:")
            print(f"  tensorboard --logdir {profile_dir}")
            return
        
        # 延迟测试模式
        print(f"\n正在进行基准测试 ({self.profiler_args.num_iters} 次迭代)...")
        latencies = []
        
        for i in tqdm(range(self.profiler_args.num_iters), desc="测试迭代"):
            latency = self.run_to_completion(dummy_prompts, profile_dir=None)
            latencies.append(latency)
        
        # 计算统计结果（参考 vLLM 的统计方式）
        latencies = np.array(latencies)
        percentages = [10, 25, 50, 75, 90, 99]
        percentiles = np.percentile(latencies, percentages)
        
        # 打印结果
        print(f"\n=== 延迟测试结果 ===")
        print(f"平均延迟: {np.mean(latencies):.4f} 秒")
        print(f"标准差: {np.std(latencies):.4f} 秒")
        
        for percentage, percentile in zip(percentages, percentiles):
            print(f'{percentage}% 百分位延迟: {percentile:.4f} 秒')
        
        # 计算吞吐量
        avg_latency = np.mean(latencies)
        total_tokens = (self.profiler_args.input_len + self.profiler_args.output_len) * self.profiler_args.batch_size
        throughput = total_tokens / avg_latency
        print(f"\n总体吞吐量: {throughput:.2f} tokens/s")
        print(f"Prefill 等效吞吐量: {self.profiler_args.input_len * self.profiler_args.batch_size / avg_latency:.2f} tokens/s")
        print(f"Decode 等效吞吐量: {self.profiler_args.output_len * self.profiler_args.batch_size / avg_latency:.2f} tokens/s")
        
        # 输出 JSON 结果（如果指定）
        if self.profiler_args.output_json:
            results = {
                "config": {
                    "model_path": self.server_args.model_path,
                    "quantization": self.server_args.quantization,
                    "input_len": self.profiler_args.input_len,
                    "output_len": self.profiler_args.output_len,
                    "batch_size": self.profiler_args.batch_size,
                    "tp_size": self.server_args.tp_size,
                },
                "results": {
                    "avg_latency": float(np.mean(latencies)),
                    "std_latency": float(np.std(latencies)),
                    "latencies": latencies.tolist(),
                    "percentiles": dict(zip([f"p{p}" for p in percentages], percentiles.tolist())),
                    "throughput": {
                        "total_tokens_per_sec": float(throughput),
                        "prefill_equivalent_tokens_per_sec": float(self.profiler_args.input_len * self.profiler_args.batch_size / avg_latency),
                        "decode_equivalent_tokens_per_sec": float(self.profiler_args.output_len * self.profiler_args.batch_size / avg_latency),
                    }
                }
            }
            
            with open(self.profiler_args.output_json, "w", encoding="utf-8") as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            print(f"\n结果已保存到 JSON 文件: {self.profiler_args.output_json}")
    
    def cleanup(self):
        """清理资源"""
        if self.engine:
            try:
                self.engine.shutdown()
            except Exception as e:
                print(f"引擎关闭时出现错误: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='SGLang 模型性能分析工具 - 参考 vLLM benchmark_latency.py 设计'
    )
    
    # 添加 SGLang 服务器参数
    ServerArgs.add_cli_args(parser)
    
    # 添加 Profiler 参数
    ModelProfilerArgs.add_cli_args(parser)
    
    args = parser.parse_args()
    
    # 解析参数
    server_args = ServerArgs.from_cli_args(args)
    profiler_args = ModelProfilerArgs.from_cli_args(args)
    
    # 参数验证
    if not server_args.model_path:
        print("错误: 必须指定 --model-path 参数")
        sys.exit(1)
    
    print(f"=== SGLang 模型 Profiler ===")
    print(f"服务器参数: {server_args}")
    print(f"Profiler 参数: {profiler_args}")
    
    # 创建并运行 profiler
    profiler = SGLangModelProfiler(server_args, profiler_args)
    
    try:
        profiler.initialize_engine()
        profiler.run_benchmark()
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        profiler.cleanup()


if __name__ == "__main__":
    main()
