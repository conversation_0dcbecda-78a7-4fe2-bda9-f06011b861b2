# SGLang Profiler 专门测试脚本文档

## 概述

`sglang_profiler_bench.py` 是基于 `bench_one_batch.py` 设计模式开发的专门用于 SGLang 性能分析的脚本。它集成了 `bench_utils.py` 的最佳实践，提供深度的性能分析和 profiling 功能。

## 核心特性

### 1. 深度 Profiling 分析
- **分阶段分析**: 支持分别对 prefill 和 decode 阶段进行独立 profiling
- **内核级别分析**: 详细分析 CUDA 内核和 CPU 操作的性能
- **内存分析**: 可选的内存使用情况分析
- **调用栈信息**: 包含详细的函数调用栈信息

### 2. 高级性能测试
- **L2 缓存管理**: 集成了从 bench_utils.py 学习的 L2 缓存刷新机制
- **多轮测试**: 支持预热和多轮性能测试以获得稳定结果
- **参数扫描**: 支持多个批次大小、输入长度、输出长度的组合测试

### 3. 灵活的配置选项
- **Profiler 配置**: 可配置记录 shapes、内存、FLOPS 等详细信息
- **输出控制**: 支持输出抑制和详细分析模式
- **Nsight Systems 集成**: 支持 Nsight Systems profiling

## 主要参数说明

### 基础参数
- `--model-path`: 模型路径（必需）
- `--quantization`: 量化方式（如 w8a8_int8）
- `--batch-size`: 批次大小列表
- `--input-len`: 输入长度列表
- `--output-len`: 输出长度列表

### Profiling 参数
- `--profile-all`: 分析所有阶段
- `--profile-prefill`: 分析 prefill 阶段（默认开启）
- `--profile-decode`: 分析 decode 阶段（默认开启）
- `--profile-memory`: 进行内存分析
- `--profile-record-shapes`: 记录 tensor shapes
- `--profile-detail`: 详细分析模式

### L2 缓存参数
- `--enable-l2-flush`: 启用 L2 缓存刷新（默认开启）
- `--l2-flush-size`: L2 缓存刷新大小（GB，默认1.0）

### 内核测试参数
- `--kernel-warmup-runs`: 内核预热次数（默认2）
- `--kernel-test-runs`: 内核测试次数（默认5）

## 使用示例

### 1. 基础 profiling
```bash
python sglang_profiler_bench.py \
    --model-path /home/<USER>/deepseek-int8 \
    --quantization w8a8_int8 \
    --batch-size 1 \
    --input-len 256 \
    --output-len 16
```

### 2. 多参数扫描
```bash
python sglang_profiler_bench.py \
    --model-path /home/<USER>/deepseek-int8 \
    --quantization w8a8_int8 \
    --batch-size 1 2 4 \
    --input-len 256 512 1024 \
    --output-len 16 32 64 \
    --profile-all
```

### 3. 详细内存分析
```bash
python sglang_profiler_bench.py \
    --model-path /home/<USER>/deepseek-int8 \
    --quantization w8a8_int8 \
    --batch-size 1 \
    --input-len 512 \
    --output-len 32 \
    --profile-memory \
    --profile-record-shapes \
    --profile-detail
```

### 4. L2 缓存优化测试
```bash
python sglang_profiler_bench.py \
    --model-path /home/<USER>/deepseek-int8 \
    --quantization w8a8_int8 \
    --batch-size 1 \
    --input-len 256 \
    --output-len 16 \
    --enable-l2-flush \
    --l2-flush-size 0.5 \
    --kernel-test-runs 10
```

## 输出文件说明

### 1. Chrome Trace 文件
- **位置**: `profiler_traces/` 目录
- **格式**: JSON 格式，可在 Chrome 浏览器中打开 `chrome://tracing/` 查看
- **命名**: `{prefix}_batch{bs}_input{il}_output{ol}_{stage}.json`

### 2. 结果 JSON 文件
- **位置**: 工作目录
- **内容**: 包含详细的性能统计信息
- **字段说明**:
  - `times`: 每轮测试的时间列表
  - `avg_time`: 平均时间
  - `throughput`: 吞吐量
  - `trace_file`: 对应的 trace 文件路径

## 性能分析流程

### 1. 模型加载
- 加载指定的量化模型
- 初始化 tokenizer 和 model runner
- 显示内存使用情况

### 2. 全局预热
- 运行简单的推理以初始化内核
- 准备模型进入稳定状态

### 3. Prefill 阶段分析
- L2 缓存刷新
- 内核预热（多轮运行）
- 启动 profiler 进行精确测量
- 记录时间和导出 trace

### 4. Decode 阶段分析
- 重新准备状态（通过 prefill）
- L2 缓存刷新
- 内核预热
- 启动 profiler 进行 decode 测量
- 记录时间和导出 trace

### 5. 结果汇总
- 计算统计信息（平均值、标准差、吞吐量）
- 保存 JSON 格式结果
- 打印性能总结

## 与 bench_one_batch.py 的区别

### 相同点
- 使用相同的底层 SGLang 接口
- 支持多进程和分布式测试
- 参数解析和配置管理

### 改进点
1. **专门的 Profiling 功能**
   - 更详细的 profiler 配置选项
   - 分阶段的性能分析
   - L2 缓存管理集成

2. **更好的结果记录**
   - 详细的统计信息
   - 结构化的 JSON 输出
   - 多种性能指标

3. **增强的稳定性**
   - 预热机制
   - 多轮测试求平均
   - 错误处理和内存管理

## 最佳实践建议

### 1. 测试环境准备
- 确保 GPU 内存充足
- 关闭其他 GPU 进程
- 使用一致的环境变量设置

### 2. 参数选择
- 从小的批次大小开始测试
- 根据 GPU 内存调整 L2 缓存刷新大小
- 多轮测试获得稳定结果

### 3. 结果分析
- 使用 Chrome trace 查看详细的内核执行情况
- 关注 MoE 内核的性能
- 比较不同参数配置的性能差异

## 故障排除

### 1. 内存不足
- 减少 `--l2-flush-size` 值
- 减少批次大小
- 减少输入/输出长度

### 2. 性能不稳定
- 增加 `--kernel-warmup-runs` 和 `--kernel-test-runs`
- 启用 L2 缓存刷新
- 检查系统负载

### 3. 文件权限问题
- 确保有 `profiler_traces/` 目录的写权限
- 检查输出文件路径的权限

## 实际测试结果示例

基于实际运行结果（batch=1, input_len=128, output_len=8）:

```json
{
  "prefill": {
    "avg_time": 0.007444s,
    "throughput": 17193 tokens/s,
    "std_time": 0.000757s
  },
  "decode": {
    "avg_time": 0.005204s,
    "throughput": 192 tokens/s,
    "std_time": 0.000426s
  }
}
```

这表明：
- Prefill 阶段吞吐量高，但单次延迟较高
- Decode 阶段延迟较低，适合实时推理
- 测试结果具有良好的稳定性（低标准差）
