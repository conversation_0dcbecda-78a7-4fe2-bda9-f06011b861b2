#!/bin/bash
# SGLang Profiler 测试脚本使用示例

set -e

echo "=== SGLang Profiler 测试脚本使用示例 ==="
echo "开始时间: $(date)"
echo ""

# 激活环境
source /workspace/sglang_test/bin/activate

# 确保在正确的目录
cd /workspace/sglang_test_workspace/bench_utils_profiler_study_20250917_010803

# 设置模型路径
MODEL_PATH="${MODEL_PATH:-/home/<USER>/deepseek-int8}"

echo "使用模型路径: $MODEL_PATH"
echo ""

echo "1. 基础 Profiler 测试..."
echo "-------------------------------------------"
python sglang_profiler_bench.py \
    --model-path "$MODEL_PATH" \
    --quantization w8a8_int8 \
    --batch-size 1 \
    --input-len 256 \
    --output-len 16 \
    --profile-prefill \
    --profile-decode \
    --enable-l2-flush \
    --l2-flush-size 0.5 \
    --run-name "basic_profiler_test" \
    --result-filename "basic_profiler_results.jsonl" \
    --trust-remote-code
echo ""

echo "2. 多参数扫描 Profiler 测试..."
echo "-------------------------------------------"
python sglang_profiler_bench.py \
    --model-path "$MODEL_PATH" \
    --quantization w8a8_int8 \
    --batch-size 1 2 \
    --input-len 256 512 \
    --output-len 16 32 \
    --profile-all \
    --enable-l2-flush \
    --l2-flush-size 0.5 \
    --kernel-test-runs 3 \
    --run-name "sweep_profiler_test" \
    --result-filename "sweep_profiler_results.jsonl" \
    --trust-remote-code
echo ""

echo "3. 详细内存分析测试..."
echo "-------------------------------------------"
python sglang_profiler_bench.py \
    --model-path "$MODEL_PATH" \
    --quantization w8a8_int8 \
    --batch-size 1 \
    --input-len 512 \
    --output-len 32 \
    --profile-memory \
    --profile-record-shapes \
    --profile-with-stack \
    --profile-detail \
    --run-name "memory_analysis_test" \
    --result-filename "memory_analysis_results.jsonl" \
    --trust-remote-code
echo ""

echo "=== 测试完成 ==="
echo "结束时间: $(date)"
echo ""
echo "生成的文件:"
echo "- Profiler 脚本: sglang_profiler_bench.py"
echo "- 使用示例: run_profiler_examples.sh"
echo ""
echo "Profile traces 保存在: profiler_traces/"
echo "Results 文件: *_profiler_results.jsonl"
ls -la *.jsonl 2>/dev/null || echo "暂无结果文件"
echo ""
echo "Trace 文件:"
ls -la profiler_traces/ 2>/dev/null || echo "暂无 trace 文件"
