#!/bin/bash
# SGLang Model Profiler 使用示例脚本

set -e

echo "=== SGLang Model Profiler 使用示例 ==="
echo "基于 vLLM benchmark_latency.py 的设计模式"
echo "开始时间: $(date)"
echo ""

# 激活环境
source /workspace/sglang_test/bin/activate

# 确保在正确的目录
cd /workspace/sglang_test_workspace/bench_utils_profiler_study_20250917_010803

# 设置模型路径
MODEL_PATH="${MODEL_PATH:-/home/<USER>/deepseek-int8}"

echo "使用模型路径: $MODEL_PATH"
echo ""

echo "1. 基础延迟测试..."
echo "-------------------------------------------"
python sglang_model_profiler.py \
    --model-path "$MODEL_PATH" \
    --quantization w8a8_int8 \
    --input-len 256 \
    --output-len 32 \
    --batch-size 1 \
    --num-iters-warmup 3 \
    --num-iters 5 \
    --trust-remote-code \
    --output-json basic_latency_results.json
echo ""

echo "2. TensorBoard Profiling 测试..."
echo "-------------------------------------------"
python sglang_model_profiler.py \
    --model-path "$MODEL_PATH" \
    --quantization w8a8_int8 \
    --input-len 128 \
    --output-len 16 \
    --batch-size 1 \
    --trust-remote-code \
    --profile \
    --profile-result-dir ./tensorboard_profiler_results \
    --profile-chrome-trace
echo ""

echo "3. 批量大小对比测试..."
echo "-------------------------------------------"
for batch_size in 1 2 4; do
    echo "测试批量大小: $batch_size"
    python sglang_model_profiler.py \
        --model-path "$MODEL_PATH" \
        --quantization w8a8_int8 \
        --input-len 256 \
        --output-len 32 \
        --batch-size $batch_size \
        --num-iters-warmup 2 \
        --num-iters 3 \
        --trust-remote-code \
        --output-json "batch${batch_size}_results.json"
    echo ""
done

echo "4. 不同输入长度对比测试..."
echo "-------------------------------------------"
for input_len in 128 256 512; do
    echo "测试输入长度: $input_len"
    python sglang_model_profiler.py \
        --model-path "$MODEL_PATH" \
        --quantization w8a8_int8 \
        --input-len $input_len \
        --output-len 32 \
        --batch-size 1 \
        --num-iters-warmup 2 \
        --num-iters 3 \
        --trust-remote-code \
        --output-json "input${input_len}_results.json"
    echo ""
done

echo "=== 测试完成 ==="
echo "结束时间: $(date)"
echo ""
echo "生成的文件:"
echo "- Profiler 脚本: sglang_model_profiler.py"
echo "- 使用示例: run_model_profiler_examples.sh"
echo ""
echo "结果文件:"
ls -la *.json 2>/dev/null || echo "暂无 JSON 结果文件"
echo ""
echo "TensorBoard 结果:"
ls -la tensorboard_profiler_results/ 2>/dev/null || echo "暂无 TensorBoard 结果"
echo ""
echo "查看 TensorBoard 结果的命令:"
echo "tensorboard --logdir ./tensorboard_profiler_results"
echo ""
echo "Chrome trace 文件可以在浏览器中打开 chrome://tracing/ 查看"
