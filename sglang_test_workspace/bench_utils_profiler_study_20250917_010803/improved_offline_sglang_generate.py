#!/usr/bin/env python3
"""
基于 bench_utils.py 学习的改进版离线 SGLang 推理测试
集成了 bench_kineto 的核心功能来提供更准确的性能分析
"""
import os
import sys
import traceback
import time
import torch
from contextlib import nullcontext

# 必须在导入 sglang 前设置分布式/通信相关环境
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

# 设置profiling环境变量
os.environ.setdefault("SGLANG_TORCH_PROFILER_DIR", "/workspace/sglang_test_workspace/profile_logs")
os.environ.setdefault("SGLANG_PROFILE_WITH_STACK", "True")

import sglang as sgl

MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")


class suppress_stdout_stderr:
    """从 bench_utils.py 学习的输出抑制类"""
    def __enter__(self):
        self.outnull_file = open(os.devnull, "w")
        self.errnull_file = open(os.devnull, "w")

        self.old_stdout_fileno_undup = sys.stdout.fileno()
        self.old_stderr_fileno_undup = sys.stderr.fileno()

        self.old_stdout_fileno = os.dup(sys.stdout.fileno())
        self.old_stderr_fileno = os.dup(sys.stderr.fileno())

        self.old_stdout = sys.stdout
        self.old_stderr = sys.stderr

        os.dup2(self.outnull_file.fileno(), self.old_stdout_fileno_undup)
        os.dup2(self.errnull_file.fileno(), self.old_stderr_fileno_undup)

        sys.stdout = self.outnull_file
        sys.stderr = self.errnull_file
        return self

    def __exit__(self, *_):
        sys.stdout = self.old_stdout
        sys.stderr = self.old_stderr

        os.dup2(self.old_stdout_fileno, self.old_stdout_fileno_undup)
        os.dup2(self.old_stderr_fileno, self.old_stderr_fileno_undup)

        os.close(self.old_stdout_fileno)
        os.close(self.old_stderr_fileno)

        self.outnull_file.close()
        self.errnull_file.close()


def bench_sglang_inference(
    llm,
    prompt,
    sampling_params,
    num_tests: int = 5,
    suppress_kineto_output: bool = False,
    trace_path: str = None,
    flush_l2: bool = True,
):
    """
    基于 bench_utils.bench_kineto 的 SGLang 推理性能测试函数
    """
    # 检查是否使用 Nsight Systems
    using_nsys = int(os.environ.get("SGLANG_NSYS_PROFILING", 0))
    
    # L2 缓存刷新大小：调整为更小的值以适应可用内存
    flush_l2_size = int(1e9 // 4)  # 1GB instead of 8GB

    # 定义推理函数
    def inference_fn():
        return llm.generate(prompt=prompt, sampling_params=sampling_params)

    # 首次运行以初始化自动调优内核
    print("[bench] Warming up...")
    inference_fn()

    # 输出抑制配置
    suppress = (
        suppress_stdout_stderr
        if suppress_kineto_output and not using_nsys
        else nullcontext
    )
    
    # 开始性能测试
    with suppress():
        schedule = (
            torch.profiler.schedule(wait=0, warmup=1, active=1, repeat=1)
            if not using_nsys
            else None
        )
        profiler = (
            torch.profiler.profile(
                activities=[torch.profiler.ProfilerActivity.CUDA, torch.profiler.ProfilerActivity.CPU],
                schedule=schedule,
                record_shapes=True,
                profile_memory=True,
                with_stack=True,
                with_flops=True,
                with_modules=True,
            )
            if not using_nsys
            else nullcontext()
        )
        
        inference_times = []
        results = []
        
        with profiler:
            for i in range(2):  # 两轮测试
                for test_idx in range(num_tests):
                    if flush_l2:
                        # 刷新 L2 缓存以获得一致的性能测试结果
                        torch.empty(flush_l2_size, dtype=torch.int, device="cuda").zero_()
                    
                    # 测量推理时间
                    start_time = time.time()
                    result = inference_fn()
                    end_time = time.time()
                    
                    inference_time = end_time - start_time
                    inference_times.append(inference_time)
                    
                    if i == 1:  # 只保存第二轮的结果
                        results.append(result)
                    
                    print(f"[bench] Test {i+1}-{test_idx+1}: {inference_time:.4f}s")

                if not using_nsys:
                    profiler.step()

    # 如果使用 Nsight Systems，直接返回
    if using_nsys:
        return results, inference_times

    # 保存 Chrome trace
    if trace_path is not None:
        profiler.export_chrome_trace(trace_path)
        print(f"[bench] Profile trace saved to: {trace_path}")

    # 分析和打印性能统计
    print("\n[bench] === Performance Analysis ===")
    print(f"Average inference time: {sum(inference_times)/len(inference_times):.4f}s")
    print(f"Min inference time: {min(inference_times):.4f}s")
    print(f"Max inference time: {max(inference_times):.4f}s")
    
    print("\n[bench] Top 10 CUDA kernels by time:")
    print(profiler.key_averages().table(sort_by="cuda_time_total", row_limit=10))
    
    print("\n[bench] Top 10 CPU operations by time:")
    print(profiler.key_averages().table(sort_by="cpu_time_total", row_limit=10))
    
    return results, inference_times, profiler


def main():
    # 创建profiler输出目录
    profile_dir = "/workspace/sglang_test_workspace/profile_logs"
    os.makedirs(profile_dir, exist_ok=True)
    
    # 直接离线引擎加载（不启动HTTP服务）
    llm = None
    
    try:
        print("[main] Initializing SGLang Engine...")
        llm = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=1,
            quantization="w8a8_int8",
            trust_remote_code=True,
            dist_init_addr="127.0.0.1:8001",
            disable_cuda_graph=True,  # 禁用CUDA Graph以便看到详细的kernel调用
            log_level="info",
        )

        prompt = "用一句话介绍你自己。"
        sampling_params = {"max_new_tokens": 32, "temperature": 0.7}
        
        # 使用改进的 bench 函数进行测试
        timestamp = int(time.time())
        trace_file = f"{profile_dir}/bench_sglang_trace_{timestamp}.json"
        
        print("[main] Starting benchmark testing...")
        results, inference_times, profiler = bench_sglang_inference(
            llm=llm,
            prompt=prompt,
            sampling_params=sampling_params,
            num_tests=3,  # 减少测试次数以加快速度
            suppress_kineto_output=False,
            trace_path=trace_file,
            flush_l2=True,
        )
        
        # 显示推理结果
        print(f"\n[main] === Inference Results ===")
        for i, result in enumerate(results):
            text = result.get("text", str(result))
            print(f"Result {i+1}: {text}")
        
        print(f"\n[main] Benchmark completed. Total tests: {len(inference_times)}")
        
    except Exception:
        print("[main] ERROR:\n" + traceback.format_exc())
        sys.exit(1)
    finally:
        if llm is not None:
            try:
                llm.shutdown()
            except Exception:
                pass


if __name__ == "__main__":
    main()
