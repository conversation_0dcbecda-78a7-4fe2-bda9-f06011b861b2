#!/usr/bin/env python3
"""
SGLang 深度算子分析器

通过 hook PyTorch 操作和 CUDA kernels 来深入分析 SGLang 推理过程中的真实算子性能。
特别关注 shape 变化和量化算子的性能表现。
"""

import argparse
import json
import os
import sys
import time
import traceback
from collections import defaultdict
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

import numpy as np
import torch
import torch.nn.functional as F
from torch.profiler import profile, ProfilerActivity

# SGLang imports
import sglang as sgl


class DeepOperatorHook:
    """深度算子 Hook 类，用于捕获所有 PyTorch 操作"""
    
    def __init__(self):
        self.operations = []
        self.call_stack = []
        self.operation_counts = defaultdict(int)
        self.shape_info = defaultdict(list)
        self.timing_info = defaultdict(list)
        self.active = False
    
    def start_recording(self):
        """开始记录算子调用"""
        self.active = True
        self.operations.clear()
        self.call_stack.clear()
        self.operation_counts.clear()
        self.shape_info.clear()
        self.timing_info.clear()
        
        # Hook 主要的 PyTorch 操作
        self._hook_torch_functions()
    
    def stop_recording(self):
        """停止记录算子调用"""
        self.active = False
        self._unhook_torch_functions()
    
    def _hook_torch_functions(self):
        """Hook PyTorch 核心函数"""
        # 保存原始函数
        self.original_functions = {}
        
        # 要 hook 的函数列表
        functions_to_hook = [
            # 线性代数
            (torch, 'matmul'),
            (torch, 'bmm'),
            (torch, 'addmm'),
            (torch.nn.functional, 'linear'),
            
            # 注意力机制
            (torch.nn.functional, 'scaled_dot_product_attention'),
            
            # 激活函数
            (torch.nn.functional, 'relu'),
            (torch.nn.functional, 'gelu'),
            (torch.nn.functional, 'silu'),
            
            # 归一化
            (torch.nn.functional, 'layer_norm'),
            (torch.nn.functional, 'rms_norm'),
            
            # 形状操作
            (torch, 'reshape'),
            (torch, 'view'),
            (torch, 'transpose'),
            (torch, 'permute'),
            
            # 量化相关
            (torch, 'quantize_per_tensor'),
            (torch, 'dequantize'),
        ]
        
        for module, func_name in functions_to_hook:
            if hasattr(module, func_name):
                original_func = getattr(module, func_name)
                self.original_functions[(module, func_name)] = original_func
                
                # 创建 wrapper 函数
                wrapped_func = self._create_wrapper(func_name, original_func)
                setattr(module, func_name, wrapped_func)
    
    def _unhook_torch_functions(self):
        """恢复原始函数"""
        for (module, func_name), original_func in self.original_functions.items():
            setattr(module, func_name, original_func)
        self.original_functions.clear()
    
    def _create_wrapper(self, func_name: str, original_func):
        """创建函数包装器"""
        def wrapper(*args, **kwargs):
            if not self.active:
                return original_func(*args, **kwargs)
            
            # 记录输入 shapes
            input_shapes = []
            for arg in args:
                if isinstance(arg, torch.Tensor):
                    input_shapes.append(list(arg.shape))
            
            # 记录调用开始时间
            start_time = time.perf_counter()
            
            # 调用原始函数
            try:
                result = original_func(*args, **kwargs)
                success = True
                error_msg = None
            except Exception as e:
                result = None
                success = False
                error_msg = str(e)
            
            # 记录调用结束时间
            end_time = time.perf_counter()
            execution_time = (end_time - start_time) * 1000  # ms
            
            # 记录输出 shapes
            output_shapes = []
            if success and result is not None:
                if isinstance(result, torch.Tensor):
                    output_shapes.append(list(result.shape))
                elif isinstance(result, (list, tuple)):
                    for item in result:
                        if isinstance(item, torch.Tensor):
                            output_shapes.append(list(item.shape))
            
            # 记录操作信息
            operation_info = {
                'function_name': func_name,
                'input_shapes': input_shapes,
                'output_shapes': output_shapes,
                'execution_time_ms': execution_time,
                'success': success,
                'error_msg': error_msg,
                'timestamp': time.time()
            }
            
            self.operations.append(operation_info)
            self.operation_counts[func_name] += 1
            self.shape_info[func_name].append({
                'input_shapes': input_shapes,
                'output_shapes': output_shapes
            })
            self.timing_info[func_name].append(execution_time)
            
            return result
        
        return wrapper
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {
            'total_operations': len(self.operations),
            'operation_counts': dict(self.operation_counts),
            'timing_stats': {},
            'shape_analysis': {},
            'operations_detail': self.operations
        }
        
        # 计算每个操作的时间统计
        for func_name, times in self.timing_info.items():
            if times:
                stats['timing_stats'][func_name] = {
                    'count': len(times),
                    'total_time_ms': sum(times),
                    'mean_time_ms': np.mean(times),
                    'std_time_ms': np.std(times),
                    'min_time_ms': np.min(times),
                    'max_time_ms': np.max(times)
                }
        
        # 分析 shape 变化
        for func_name, shapes in self.shape_info.items():
            if shapes:
                input_shapes = [s['input_shapes'] for s in shapes if s['input_shapes']]
                output_shapes = [s['output_shapes'] for s in shapes if s['output_shapes']]
                
                stats['shape_analysis'][func_name] = {
                    'input_shape_variations': len(set(str(s) for s in input_shapes)),
                    'output_shape_variations': len(set(str(s) for s in output_shapes)),
                    'common_input_shapes': list(set(str(s) for s in input_shapes))[:5],
                    'common_output_shapes': list(set(str(s) for s in output_shapes))[:5]
                }
        
        return stats


class DeepSGLangAnalyzer:
    """深度 SGLang 算子分析器"""
    
    def __init__(
        self,
        model_path: str,
        quantization: Optional[str] = None,
        trust_remote_code: bool = False,
        tp_size: int = 1,
        output_dir: Optional[str] = None
    ):
        self.model_path = model_path
        self.quantization = quantization
        self.trust_remote_code = trust_remote_code
        self.tp_size = tp_size
        
        # 创建输出目录
        if output_dir:
            self.output_dir = Path(output_dir)
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.output_dir = Path(f"./deep_sglang_analysis_{timestamp}")
        
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.engine = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.hook = DeepOperatorHook()
        
        print(f"=== SGLang 深度算子分析器 ===")
        print(f"模型路径: {model_path}")
        print(f"量化方式: {quantization}")
        print(f"输出目录: {self.output_dir}")
    
    def initialize_engine(self):
        """初始化 SGLang 引擎"""
        print("正在初始化 SGLang 引擎...")
        
        try:
            self.engine = sgl.Engine(
                model_path=self.model_path,
                tp_size=self.tp_size,
                quantization=self.quantization,
                trust_remote_code=self.trust_remote_code,
                disable_cuda_graph=True,  # 禁用 CUDA Graph 以观察详细算子
                log_level="error",
                mem_fraction_static=0.6,  # 减少内存使用
            )
            print("✅ SGLang 引擎初始化成功")
            return True
        except Exception as e:
            print(f"❌ SGLang 引擎初始化失败: {e}")
            return False
    
    def analyze_inference_with_hooks(
        self,
        prompts: List[str],
        max_new_tokens: int = 32,
        analysis_name: str = "deep_analysis"
    ) -> Dict[str, Any]:
        """使用 Hook 方法深度分析推理过程"""
        
        print(f"\n🔍 开始深度算子分析: {analysis_name}")
        print(f"测试 prompts: {len(prompts)}")
        print(f"最大生成长度: {max_new_tokens}")
        
        results = {
            "config": {
                "model_path": self.model_path,
                "quantization": self.quantization,
                "max_new_tokens": max_new_tokens,
                "num_prompts": len(prompts)
            },
            "hook_analysis": {},
            "profiler_analysis": {},
            "timing_stats": {},
            "shape_summary": {}
        }
        
        # 预热（不记录）
        print("  预热推理...")
        try:
            for prompt in prompts[:1]:
                _ = self.engine.generate(
                    prompt=prompt,
                    sampling_params={"max_new_tokens": min(8, max_new_tokens), "temperature": 0.0}
                )
            print("  预热完成")
        except Exception as e:
            print(f"  预热失败: {e}")
        
        # 1. Hook 分析
        print("  开始 Hook 算子分析...")
        self.hook.start_recording()
        
        hook_latencies = []
        try:
            for i, prompt in enumerate(prompts):
                torch.cuda.synchronize()
                start_time = time.perf_counter()
                
                output = self.engine.generate(
                    prompt=prompt,
                    sampling_params={
                        "max_new_tokens": max_new_tokens,
                        "temperature": 0.0
                    }
                )
                
                torch.cuda.synchronize()
                end_time = time.perf_counter()
                
                latency = (end_time - start_time) * 1000
                hook_latencies.append(latency)
                
                print(f"    Prompt {i+1}: {latency:.2f} ms, 输出长度: {len(output) if output else 0}")
        
        except Exception as e:
            print(f"  Hook 分析中出现错误: {e}")
        
        finally:
            self.hook.stop_recording()
        
        # 获取 Hook 统计信息
        hook_stats = self.hook.get_statistics()
        results["hook_analysis"] = hook_stats
        
        print(f"  Hook 分析完成，捕获 {hook_stats['total_operations']} 个算子调用")
        
        # 2. PyTorch Profiler 分析
        print("  开始 PyTorch Profiler 分析...")
        
        profiler_latencies = []
        with profile(
            activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA],
            record_shapes=True,
            profile_memory=True,
            with_stack=True
        ) as profiler:
            
            for i, prompt in enumerate(prompts):
                torch.cuda.synchronize()
                start_time = time.perf_counter()
                
                try:
                    output = self.engine.generate(
                        prompt=prompt,
                        sampling_params={
                            "max_new_tokens": max_new_tokens,
                            "temperature": 0.0
                        }
                    )
                    
                    torch.cuda.synchronize()
                    end_time = time.perf_counter()
                    
                    latency = (end_time - start_time) * 1000
                    profiler_latencies.append(latency)
                    
                except Exception as e:
                    print(f"    Profiler 分析 Prompt {i+1} 失败: {e}")
                    profiler_latencies.append(float('inf'))
                
                profiler.step()
        
        # 分析 profiler 结果
        try:
            key_averages = profiler.key_averages()
            profiler_stats = self._analyze_profiler_results(key_averages)
            results["profiler_analysis"] = profiler_stats
            
            # 保存 profiler 详细结果
            profiler_file = self.output_dir / f"{analysis_name}_profiler_details.txt"
            with open(profiler_file, 'w') as f:
                f.write("=== PyTorch Profiler 详细结果 ===\n\n")
                f.write("按 CUDA 时间排序:\n")
                f.write(key_averages.table(sort_by="cuda_time_total", row_limit=50))
                f.write("\n\n按调用次数排序:\n")
                f.write(key_averages.table(sort_by="count", row_limit=30))
            
            print(f"  PyTorch Profiler 分析完成，详细结果保存到: {profiler_file}")
            
        except Exception as e:
            print(f"  Profiler 结果分析失败: {e}")
            traceback.print_exc()
        
        # 3. 综合统计
        all_latencies = [l for l in hook_latencies + profiler_latencies if l != float('inf')]
        if all_latencies:
            results["timing_stats"] = {
                "hook_latencies": hook_latencies,
                "profiler_latencies": profiler_latencies,
                "mean_latency_ms": float(np.mean(all_latencies)),
                "std_latency_ms": float(np.std(all_latencies)),
                "min_latency_ms": float(np.min(all_latencies)),
                "max_latency_ms": float(np.max(all_latencies))
            }
        
        # 4. Shape 变化摘要
        results["shape_summary"] = self._generate_shape_summary(hook_stats)
        
        # 保存完整结果
        results_file = self.output_dir / f"{analysis_name}_complete_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        return results
    
    def _analyze_profiler_results(self, key_averages) -> Dict[str, Any]:
        """分析 PyTorch Profiler 结果"""
        analysis = {
            "top_cuda_ops": [],
            "top_cpu_ops": [],
            "memory_ops": [],
            "compute_ops": [],
            "shape_info": {}
        }
        
        for event in key_averages:
            event_info = {
                "name": event.key,
                "cuda_time": getattr(event, 'device_time', getattr(event, 'cuda_time', 0)),
                "cpu_time": getattr(event, 'cpu_time', 0),
                "count": getattr(event, 'count', 0),
                "input_shapes": getattr(event, 'input_shapes', [])
            }
            
            # 分类算子
            name_lower = event.key.lower()
            if any(compute_kw in name_lower for compute_kw in ['matmul', 'bmm', 'addmm', 'gemm', 'conv']):
                analysis["compute_ops"].append(event_info)
            elif any(mem_kw in name_lower for mem_kw in ['copy', 'fill', 'malloc', 'free']):
                analysis["memory_ops"].append(event_info)
        
        # 按时间排序
        analysis["compute_ops"].sort(key=lambda x: x["cuda_time"], reverse=True)
        analysis["memory_ops"].sort(key=lambda x: x["cuda_time"], reverse=True)
        
        return analysis
    
    def _generate_shape_summary(self, hook_stats: Dict[str, Any]) -> Dict[str, Any]:
        """生成 shape 变化摘要"""
        summary = {
            "total_operations": hook_stats.get("total_operations", 0),
            "operations_with_shapes": 0,
            "unique_input_shapes": set(),
            "unique_output_shapes": set(),
            "shape_transformations": [],
            "large_tensors": []
        }
        
        for op in hook_stats.get("operations_detail", []):
            if op["input_shapes"] or op["output_shapes"]:
                summary["operations_with_shapes"] += 1
            
            # 记录 shape 变化
            for shape in op["input_shapes"]:
                summary["unique_input_shapes"].add(str(shape))
            
            for shape in op["output_shapes"]:
                summary["unique_output_shapes"].add(str(shape))
            
            # 记录 shape 变换
            if op["input_shapes"] and op["output_shapes"]:
                transform = {
                    "operation": op["function_name"],
                    "input_shapes": op["input_shapes"],
                    "output_shapes": op["output_shapes"],
                    "execution_time_ms": op["execution_time_ms"]
                }
                summary["shape_transformations"].append(transform)
            
            # 识别大张量
            for shape in op["input_shapes"] + op["output_shapes"]:
                if len(shape) > 0:
                    elements = np.prod(shape)
                    if elements > 1000000:  # 超过 1M 元素
                        summary["large_tensors"].append({
                            "operation": op["function_name"],
                            "shape": shape,
                            "elements": int(elements)
                        })
        
        # 转换 set 为 list 以便 JSON 序列化
        summary["unique_input_shapes"] = list(summary["unique_input_shapes"])[:20]
        summary["unique_output_shapes"] = list(summary["unique_output_shapes"])[:20]
        summary["large_tensors"] = sorted(summary["large_tensors"], 
                                        key=lambda x: x["elements"], reverse=True)[:10]
        
        return summary
    
    def run_comprehensive_analysis(
        self,
        test_prompts: List[str] = None,
        max_new_tokens: int = 16
    ) -> Dict[str, Any]:
        """运行全面的深度分析"""
        
        if not self.initialize_engine():
            return {"error": "引擎初始化失败"}
        
        if test_prompts is None:
            test_prompts = [
                "请分析人工智能技术在未来社会发展中的作用。",
                "解释量子计算的基本原理和应用前景。"
            ]
        
        print(f"\n🚀 开始 SGLang 深度算子分析")
        print(f"测试 prompts: {len(test_prompts)}")
        print(f"生成长度: {max_new_tokens}")
        
        try:
            results = self.analyze_inference_with_hooks(
                prompts=test_prompts,
                max_new_tokens=max_new_tokens,
                analysis_name="comprehensive"
            )
            
            # 生成分析报告
            self._generate_analysis_report(results)
            
            return results
            
        except Exception as e:
            print(f"❌ 深度分析失败: {e}")
            traceback.print_exc()
            return {"error": str(e)}
        
        finally:
            if self.engine:
                try:
                    self.engine.shutdown()
                    print("✅ SGLang 引擎已关闭")
                except:
                    pass
    
    def _generate_analysis_report(self, results: Dict[str, Any]):
        """生成分析报告"""
        report_file = self.output_dir / "deep_analysis_report.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# SGLang 深度算子分析报告\n\n")
            f.write(f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**模型路径**: {results['config']['model_path']}\n")
            f.write(f"**量化方式**: {results['config']['quantization']}\n\n")
            
            # Hook 分析结果
            hook_analysis = results.get("hook_analysis", {})
            f.write("## Hook 算子分析\n\n")
            f.write(f"- 总算子调用次数: {hook_analysis.get('total_operations', 0)}\n")
            
            op_counts = hook_analysis.get('operation_counts', {})
            if op_counts:
                f.write("\n### 算子调用统计\n\n")
                f.write("| 算子名称 | 调用次数 |\n")
                f.write("|----------|----------|\n")
                for op_name, count in sorted(op_counts.items(), key=lambda x: x[1], reverse=True):
                    f.write(f"| {op_name} | {count} |\n")
            
            # 时间分析
            timing_stats = hook_analysis.get('timing_stats', {})
            if timing_stats:
                f.write("\n### 算子执行时间分析\n\n")
                f.write("| 算子名称 | 平均时间(ms) | 总时间(ms) | 调用次数 |\n")
                f.write("|----------|-------------|------------|----------|\n")
                for op_name, stats in timing_stats.items():
                    f.write(f"| {op_name} | {stats['mean_time_ms']:.3f} | {stats['total_time_ms']:.3f} | {stats['count']} |\n")
            
            # Shape 分析
            shape_summary = results.get("shape_summary", {})
            f.write("\n## Shape 变化分析\n\n")
            f.write(f"- 有 shape 信息的操作: {shape_summary.get('operations_with_shapes', 0)}\n")
            f.write(f"- 独特输入 shapes: {len(shape_summary.get('unique_input_shapes', []))}\n")
            f.write(f"- 独特输出 shapes: {len(shape_summary.get('unique_output_shapes', []))}\n")
            
            # 大张量分析
            large_tensors = shape_summary.get("large_tensors", [])
            if large_tensors:
                f.write("\n### 大张量操作 (>1M 元素)\n\n")
                f.write("| 算子 | Shape | 元素数量 |\n")
                f.write("|------|-------|----------|\n")
                for tensor_info in large_tensors[:10]:
                    f.write(f"| {tensor_info['operation']} | {tensor_info['shape']} | {tensor_info['elements']:,} |\n")
            
            # 性能摘要
            timing_summary = results.get("timing_stats", {})
            if timing_summary:
                f.write("\n## 性能摘要\n\n")
                f.write(f"- 平均延迟: {timing_summary.get('mean_latency_ms', 0):.2f} ms\n")
                f.write(f"- 标准差: {timing_summary.get('std_latency_ms', 0):.2f} ms\n")
                f.write(f"- 最小延迟: {timing_summary.get('min_latency_ms', 0):.2f} ms\n")
                f.write(f"- 最大延迟: {timing_summary.get('max_latency_ms', 0):.2f} ms\n")
        
        print(f"📄 深度分析报告已保存: {report_file}")


def main():
    parser = argparse.ArgumentParser(
        description='SGLang 深度算子分析工具',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument('--model-path', type=str, required=True, help='模型路径')
    parser.add_argument('--quantization', type=str, help='量化方式')
    parser.add_argument('--trust-remote-code', action='store_true', help='信任远程代码')
    parser.add_argument('--tp-size', type=int, default=1, help='Tensor 并行度')
    parser.add_argument('--max-new-tokens', type=int, default=16, help='最大生成 token 数')
    parser.add_argument('--output-dir', type=str, help='输出目录')
    parser.add_argument('--prompts', type=str, nargs='+', 
                       help='测试 prompts（如果不提供则使用默认）')
    
    args = parser.parse_args()
    
    analyzer = DeepSGLangAnalyzer(
        model_path=args.model_path,
        quantization=args.quantization,
        trust_remote_code=args.trust_remote_code,
        tp_size=args.tp_size,
        output_dir=args.output_dir
    )
    
    try:
        results = analyzer.run_comprehensive_analysis(
            test_prompts=args.prompts,
            max_new_tokens=args.max_new_tokens
        )
        
        if "error" not in results:
            print(f"\n🎉 深度分析完成！")
            print(f"📂 结果目录: {analyzer.output_dir}")
            print(f"📊 查看详细报告: {analyzer.output_dir}/deep_analysis_report.md")
            
            # 快速摘要
            hook_analysis = results.get("hook_analysis", {})
            print(f"\n📈 快速摘要:")
            print(f"  捕获算子调用: {hook_analysis.get('total_operations', 0)} 次")
            print(f"  算子类型: {len(hook_analysis.get('operation_counts', {}))}")
            
            timing_stats = results.get("timing_stats", {})
            if timing_stats:
                print(f"  平均延迟: {timing_stats.get('mean_latency_ms', 0):.2f} ms")
        else:
            print(f"❌ 分析失败: {results['error']}")
    
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断分析")
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        traceback.print_exc()


if __name__ == "__main__":
    main()
