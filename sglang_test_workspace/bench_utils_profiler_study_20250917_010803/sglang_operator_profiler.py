#!/usr/bin/env python3
"""
SGLang 算子性能监控脚本 - 参考 vLLM kernels benchmark 设计

基于 vLLM benchmarks/kernels 和 benchmarks/fused_kernels 的设计模式，
为 SGLang 创建专门的算子级 profiling 和性能监控工具。

使用示例:
# 监控 attention 算子
python sglang_operator_profiler.py --operator attention --seq-len 1024 --num-heads 32

# 监控 MoE 算子
python sglang_operator_profiler.py --operator moe --num-experts 8 --topk 2

# 启用详细 profiling
python sglang_operator_profiler.py --operator layernorm --profile --profile-dir ./profiling_results

# 自动检测和 profile 所有算子
python sglang_operator_profiler.py --operator auto --model-path /home/<USER>/deepseek-int8
"""

import argparse
import dataclasses
import json
import os
import pickle
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Callable, Union

import numpy as np
import torch
import torch.nn.functional as F
import torch.utils.benchmark as TBenchmark
from torch.utils.benchmark import Measurement as TMeasurement
from tqdm import tqdm

# SGLang imports
import sglang as sgl
from sglang.srt.server_args import ServerArgs
from sglang.srt.utils import configure_logger, suppress_other_loggers

# vLLM-style benchmark utilities (adapted)
@dataclasses.dataclass
class OperatorBenchConfig:
    """算子 Benchmark 配置"""
    # 基础配置
    operator: str = "attention"  # attention, moe, layernorm, rope, quant, auto
    batch_size: int = 1
    seq_len: int = 1024
    hidden_size: int = 4096
    num_heads: int = 32
    head_size: int = 128
    
    # MoE 专用
    num_experts: int = 8
    topk: int = 2
    intermediate_size: int = 11008
    
    # 测试配置
    num_warmup_iters: int = 5
    num_iters: int = 100
    dtype: str = "bfloat16"
    
    # Profiling 配置
    profile: bool = False
    profile_dir: Optional[str] = None
    save_results: bool = True
    export_chrome_trace: bool = True
    export_tensorboard: bool = True
    
    # 输出配置
    output_json: Optional[str] = None
    verbose: bool = True

    @staticmethod
    def add_cli_args(parser: argparse.ArgumentParser):
        parser.add_argument('--operator', type=str, 
                          choices=['attention', 'moe', 'layernorm', 'rope', 'quant', 'auto'],
                          default='attention', help='要监控的算子类型')
        parser.add_argument('--batch-size', type=int, default=1, help='批次大小')
        parser.add_argument('--seq-len', type=int, default=1024, help='序列长度')
        parser.add_argument('--hidden-size', type=int, default=4096, help='隐藏层大小')
        parser.add_argument('--num-heads', type=int, default=32, help='注意力头数')
        parser.add_argument('--head-size', type=int, default=128, help='注意力头大小')
        parser.add_argument('--num-experts', type=int, default=8, help='MoE 专家数量')
        parser.add_argument('--topk', type=int, default=2, help='MoE TopK')
        parser.add_argument('--intermediate-size', type=int, default=11008, help='MoE 中间层大小')
        parser.add_argument('--num-warmup-iters', type=int, default=5, help='预热迭代次数')
        parser.add_argument('--num-iters', type=int, default=100, help='测试迭代次数')
        parser.add_argument('--dtype', type=str, choices=['float16', 'bfloat16', 'float32'],
                          default='bfloat16', help='数据类型')
        parser.add_argument('--profile', action='store_true', help='启用详细 profiling')
        parser.add_argument('--profile-dir', type=str, help='Profiling 结果保存目录')
        parser.add_argument('--save-results', action='store_true', default=True, 
                          help='保存测试结果')
        parser.add_argument('--export-chrome-trace', action='store_true', default=True,
                          help='导出 Chrome trace')
        parser.add_argument('--export-tensorboard', action='store_true', default=True,
                          help='导出 TensorBoard 日志')
        parser.add_argument('--output-json', type=str, help='JSON 结果输出路径')
        parser.add_argument('--verbose', action='store_true', default=True, help='详细输出')

    @classmethod
    def from_cli_args(cls, args: argparse.Namespace):
        return cls(
            operator=args.operator,
            batch_size=args.batch_size,
            seq_len=args.seq_len,
            hidden_size=args.hidden_size,
            num_heads=args.num_heads,
            head_size=args.head_size,
            num_experts=args.num_experts,
            topk=args.topk,
            intermediate_size=args.intermediate_size,
            num_warmup_iters=args.num_warmup_iters,
            num_iters=args.num_iters,
            dtype=args.dtype,
            profile=args.profile,
            profile_dir=args.profile_dir,
            save_results=args.save_results,
            export_chrome_trace=args.export_chrome_trace,
            export_tensorboard=args.export_tensorboard,
            output_json=args.output_json,
            verbose=args.verbose,
        )


class SGLangOperatorProfiler:
    """SGLang 算子 Profiler 类"""
    
    def __init__(self, config: OperatorBenchConfig, server_args: Optional[ServerArgs] = None):
        self.config = config
        self.server_args = server_args
        self.engine = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.dtype = getattr(torch, config.dtype)
        
        # 设置结果保存目录
        if config.profile_dir:
            self.profile_dir = Path(config.profile_dir)
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.profile_dir = Path(f"./sglang_operator_profiling_{timestamp}")
        
        self.profile_dir.mkdir(parents=True, exist_ok=True)
        
        if config.verbose:
            print(f"=== SGLang 算子性能监控器 ===")
            print(f"算子类型: {config.operator}")
            print(f"设备: {self.device}")
            print(f"数据类型: {self.dtype}")
            print(f"结果保存目录: {self.profile_dir}")

    def initialize_engine_if_needed(self):
        """如果需要，初始化 SGLang 引擎"""
        if self.config.operator == "auto" and self.server_args:
            print("正在初始化 SGLang 引擎用于自动算子检测...")
            self.engine = sgl.Engine(
                model_path=self.server_args.model_path,
                tp_size=self.server_args.tp_size,
                quantization=self.server_args.quantization,
                trust_remote_code=self.server_args.trust_remote_code,
                disable_cuda_graph=True,  # 便于观察详细的 kernel 调用
                log_level="error",  # 减少日志干扰
            )

    def benchmark_attention_operator(self) -> Dict[str, Any]:
        """Benchmark attention 算子"""
        print(f"正在测试 Attention 算子...")
        
        # 准备输入数据 (参考 vLLM benchmark_paged_attention.py)
        batch_size = self.config.batch_size
        seq_len = self.config.seq_len
        num_heads = self.config.num_heads
        head_size = self.config.head_size
        
        scale = float(1.0 / (head_size ** 0.5))
        
        # 输入张量
        query = torch.randn(batch_size, seq_len, num_heads, head_size, 
                          dtype=self.dtype, device=self.device) * scale
        key = torch.randn(batch_size, seq_len, num_heads, head_size,
                        dtype=self.dtype, device=self.device) * scale  
        value = torch.randn(batch_size, seq_len, num_heads, head_size,
                          dtype=self.dtype, device=self.device) * scale
        
        def attention_forward():
            # 使用 PyTorch 的标准 attention 实现
            q = query.transpose(1, 2)  # (batch, num_heads, seq_len, head_size)
            k = key.transpose(1, 2)
            v = value.transpose(1, 2)
            
            # Scaled dot-product attention
            scores = torch.matmul(q, k.transpose(-2, -1)) * scale
            attn_weights = F.softmax(scores, dim=-1)
            output = torch.matmul(attn_weights, v)
            return output
        
        return self._benchmark_kernel_function(
            attention_forward, 
            "attention",
            f"batch{batch_size}_seq{seq_len}_heads{num_heads}_headsize{head_size}"
        )

    def benchmark_moe_operator(self) -> Dict[str, Any]:
        """Benchmark MoE 算子"""
        print(f"正在测试 MoE 算子...")
        
        # 准备输入数据 (参考 vLLM benchmark_moe.py)
        batch_size = self.config.batch_size
        seq_len = self.config.seq_len
        hidden_size = self.config.hidden_size
        num_experts = self.config.num_experts
        topk = self.config.topk
        intermediate_size = self.config.intermediate_size
        
        # 输入张量
        x = torch.randn(batch_size * seq_len, hidden_size, 
                       dtype=self.dtype, device=self.device)
        
        # MoE 权重
        gate_weight = torch.randn(hidden_size, num_experts, 
                                dtype=self.dtype, device=self.device)
        expert_weights = torch.randn(num_experts, intermediate_size, hidden_size,
                                   dtype=self.dtype, device=self.device)
        
        def moe_forward():
            # 简化的 MoE 实现
            # Gate network
            gate_logits = F.linear(x, gate_weight.T)
            gate_probs = F.softmax(gate_logits, dim=-1)
            
            # Top-k selection
            topk_probs, topk_indices = torch.topk(gate_probs, topk, dim=-1)
            
            # Expert computation (简化版)
            outputs = []
            for i in range(topk):
                expert_idx = topk_indices[:, i]
                expert_weight = expert_weights[expert_idx]
                expert_out = torch.bmm(x.unsqueeze(1), expert_weight.transpose(-2, -1))
                outputs.append(expert_out.squeeze(1) * topk_probs[:, i:i+1])
            
            return sum(outputs)
        
        return self._benchmark_kernel_function(
            moe_forward,
            "moe", 
            f"batch{batch_size}_seq{seq_len}_experts{num_experts}_topk{topk}"
        )

    def benchmark_layernorm_operator(self) -> Dict[str, Any]:
        """Benchmark LayerNorm 算子"""
        print(f"正在测试 LayerNorm 算子...")
        
        # 准备输入数据 (参考 vLLM benchmark_layernorm.py)
        batch_size = self.config.batch_size
        seq_len = self.config.seq_len
        hidden_size = self.config.hidden_size
        
        # 输入张量
        x = torch.randn(batch_size * seq_len, hidden_size,
                       dtype=self.dtype, device=self.device)
        weight = torch.ones(hidden_size, dtype=self.dtype, device=self.device)
        bias = torch.zeros(hidden_size, dtype=self.dtype, device=self.device)
        
        def layernorm_forward():
            return F.layer_norm(x, (hidden_size,), weight, bias)
        
        return self._benchmark_kernel_function(
            layernorm_forward,
            "layernorm",
            f"tokens{batch_size * seq_len}_hidden{hidden_size}"
        )

    def benchmark_rope_operator(self) -> Dict[str, Any]:
        """Benchmark RoPE 算子"""
        print(f"正在测试 RoPE 算子...")
        
        # 准备输入数据 (参考 vLLM benchmark_rope.py)
        batch_size = self.config.batch_size
        seq_len = self.config.seq_len
        num_heads = self.config.num_heads
        head_size = self.config.head_size
        
        # 输入张量
        x = torch.randn(batch_size, seq_len, num_heads * head_size,
                       dtype=self.dtype, device=self.device)
        
        # RoPE 参数
        base = 10000
        max_position = 8192
        
        def create_rope_embeddings():
            inv_freq = 1.0 / (base ** (torch.arange(0, head_size, 2).float() / head_size))
            position_ids = torch.arange(seq_len).float()
            freqs = torch.outer(position_ids, inv_freq)
            cos = torch.cos(freqs).to(self.device, dtype=self.dtype)
            sin = torch.sin(freqs).to(self.device, dtype=self.dtype)
            return cos, sin
        
        cos, sin = create_rope_embeddings()
        
        def rope_forward():
            # 简化的 RoPE 实现
            x_reshaped = x.view(batch_size, seq_len, num_heads, head_size)
            # 这里只是示例，实际 RoPE 实现更复杂
            cos_expanded = cos.unsqueeze(0).unsqueeze(2)  # (1, seq_len, 1, head_size//2)
            sin_expanded = sin.unsqueeze(0).unsqueeze(2)
            
            # 简化的旋转操作
            return x_reshaped * cos_expanded[:, :, :, :head_size//2].repeat(1, 1, 1, 2)
        
        return self._benchmark_kernel_function(
            rope_forward,
            "rope",
            f"batch{batch_size}_seq{seq_len}_heads{num_heads}"
        )

    def benchmark_quantization_operator(self) -> Dict[str, Any]:
        """Benchmark quantization 算子"""
        print(f"正在测试 Quantization 算子...")
        
        # 准备输入数据
        batch_size = self.config.batch_size
        seq_len = self.config.seq_len
        hidden_size = self.config.hidden_size
        
        # 输入张量 (FP16/BF16)
        x = torch.randn(batch_size * seq_len, hidden_size,
                       dtype=self.dtype, device=self.device)
        
        def quantize_int8():
            # 简化的 INT8 量化
            scale = x.abs().max() / 127.0
            quantized = torch.round(x / scale).clamp(-128, 127).to(torch.int8)
            return quantized, scale
        
        def dequantize_int8():
            quantized, scale = quantize_int8()
            return quantized.to(self.dtype) * scale
        
        return self._benchmark_kernel_function(
            dequantize_int8,
            "quantization",
            f"tokens{batch_size * seq_len}_hidden{hidden_size}_int8"
        )

    def _benchmark_kernel_function(
        self, 
        kernel_fn: Callable, 
        kernel_name: str, 
        description: str
    ) -> Dict[str, Any]:
        """通用的 kernel 性能测试函数 (参考 vLLM utils.py)"""
        
        # L2 cache flush (参考 SGLang bench_utils.py)
        def flush_l2_cache():
            cache_size = 1024 * 1024 * 512  # 512MB
            cache_flush = torch.empty(cache_size, dtype=torch.int8, device=self.device)
            cache_flush.fill_(0)
            del cache_flush
            torch.cuda.empty_cache()
        
        # 预热
        if self.config.verbose:
            print(f"  预热中... ({self.config.num_warmup_iters} 次)")
        
        for _ in range(self.config.num_warmup_iters):
            flush_l2_cache()
            torch.cuda.synchronize()
            kernel_fn()
            torch.cuda.synchronize()
        
        # 性能测试
        latencies = []
        
        if self.config.profile:
            # Profiling 模式
            profile_path = self.profile_dir / f"{kernel_name}_profile"
            profile_path.mkdir(exist_ok=True)
            
            if self.config.verbose:
                print(f"  Profiling 中... (保存到 {profile_path})")
            
            # 创建 profiler
            activities = [torch.profiler.ProfilerActivity.CPU, torch.profiler.ProfilerActivity.CUDA]
            
            # 选择输出方式：优先 TensorBoard，如果同时启用则分别处理
            on_trace_ready_handler = None
            if self.config.export_tensorboard:
                on_trace_ready_handler = torch.profiler.tensorboard_trace_handler(
                    str(profile_path), worker_name=f"{kernel_name}_{description}"
                )
            
            with torch.profiler.profile(
                activities=activities,
                record_shapes=True,
                profile_memory=True,
                with_stack=True,
                with_flops=True,
                on_trace_ready=on_trace_ready_handler
            ) as profiler:
                for step in range(self.config.num_iters):
                    flush_l2_cache()
                    torch.cuda.synchronize()
                    
                    start_time = time.perf_counter()
                    kernel_fn()
                    torch.cuda.synchronize()
                    end_time = time.perf_counter()
                    
                    latencies.append((end_time - start_time) * 1000)  # ms
                    profiler.step()
            
            # 打印 profiler 统计
            if self.config.verbose:
                print(f"\n=== {kernel_name.upper()} Profiler 统计 ===")
                print(profiler.key_averages().table(sort_by="cuda_time_total", row_limit=10))
            
            # 导出 Chrome trace (在 profiler 上下文之外)
            if self.config.export_chrome_trace:
                chrome_trace_path = profile_path / f"{kernel_name}_{description}_chrome.json"
                try:
                    profiler.export_chrome_trace(str(chrome_trace_path))
                    if self.config.verbose:
                        print(f"  Chrome trace 已保存: {chrome_trace_path}")
                except RuntimeError as e:
                    if self.config.verbose:
                        print(f"  Chrome trace 导出失败: {e}")
                        print(f"  (TensorBoard trace 仍然可用)")
        
        else:
            # 普通性能测试模式
            if self.config.verbose:
                print(f"  性能测试中... ({self.config.num_iters} 次)")
            
            for _ in tqdm(range(self.config.num_iters), disable=not self.config.verbose):
                flush_l2_cache()
                torch.cuda.synchronize()
                
                start_time = time.perf_counter()
                kernel_fn()
                torch.cuda.synchronize()
                end_time = time.perf_counter()
                
                latencies.append((end_time - start_time) * 1000)  # ms
        
        # 计算统计结果
        latencies = np.array(latencies)
        results = {
            "kernel": kernel_name,
            "description": description,
            "config": {
                "batch_size": self.config.batch_size,
                "seq_len": self.config.seq_len,
                "hidden_size": self.config.hidden_size,
                "dtype": self.config.dtype,
            },
            "latency_stats": {
                "mean_ms": float(np.mean(latencies)),
                "std_ms": float(np.std(latencies)),
                "min_ms": float(np.min(latencies)),
                "max_ms": float(np.max(latencies)),
                "p50_ms": float(np.percentile(latencies, 50)),
                "p95_ms": float(np.percentile(latencies, 95)),
                "p99_ms": float(np.percentile(latencies, 99)),
            },
            "throughput": {
                "ops_per_sec": 1000.0 / np.mean(latencies),  # operations per second
            },
            "raw_latencies_ms": latencies.tolist()
        }
        
        return results

    def auto_detect_and_profile(self) -> Dict[str, Any]:
        """自动检测并 profile 模型中的关键算子"""
        print("=== 自动算子检测和 Profiling ===")
        
        results = {}
        
        # 检测可用的算子类型
        operator_configs = [
            ("attention", {}),
            ("moe", {}),
            ("layernorm", {}),
            ("rope", {}),
            ("quantization", {}),
        ]
        
        for op_name, op_kwargs in operator_configs:
            try:
                if self.config.verbose:
                    print(f"\n检测 {op_name} 算子...")
                
                # 临时切换算子类型
                original_operator = self.config.operator
                self.config.operator = op_name
                
                # 运行对应的 benchmark
                if op_name == "attention":
                    result = self.benchmark_attention_operator()
                elif op_name == "moe":
                    result = self.benchmark_moe_operator()
                elif op_name == "layernorm":
                    result = self.benchmark_layernorm_operator()
                elif op_name == "rope":
                    result = self.benchmark_rope_operator()
                elif op_name == "quantization":
                    result = self.benchmark_quantization_operator()
                
                results[op_name] = result
                
                # 恢复原始配置
                self.config.operator = original_operator
                
            except Exception as e:
                if self.config.verbose:
                    print(f"  {op_name} 算子测试失败: {e}")
                results[op_name] = {"error": str(e)}
        
        return results

    def run_benchmark(self) -> Dict[str, Any]:
        """运行指定的 benchmark"""
        
        # 初始化引擎（如果需要）
        self.initialize_engine_if_needed()
        
        if self.config.operator == "attention":
            return self.benchmark_attention_operator()
        elif self.config.operator == "moe":
            return self.benchmark_moe_operator()
        elif self.config.operator == "layernorm":
            return self.benchmark_layernorm_operator()
        elif self.config.operator == "rope":
            return self.benchmark_rope_operator()
        elif self.config.operator == "quantization":
            return self.benchmark_quantization_operator()
        elif self.config.operator == "auto":
            return self.auto_detect_and_profile()
        else:
            raise ValueError(f"不支持的算子类型: {self.config.operator}")

    def save_results(self, results: Dict[str, Any]):
        """保存测试结果"""
        if not self.config.save_results:
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存 JSON 格式
        if self.config.output_json:
            json_path = Path(self.config.output_json)
        else:
            json_path = self.profile_dir / f"operator_benchmark_results_{timestamp}.json"
        
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        if self.config.verbose:
            print(f"\n结果已保存到: {json_path}")
        
        # 保存 pickle 格式 (便于后续分析)
        pickle_path = self.profile_dir / f"operator_benchmark_results_{timestamp}.pkl"
        with open(pickle_path, 'wb') as f:
            pickle.dump(results, f)
        
        # 生成简要报告
        self.generate_summary_report(results)

    def generate_summary_report(self, results: Dict[str, Any]):
        """生成简要性能报告"""
        report_path = self.profile_dir / "performance_summary.md"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# SGLang 算子性能测试报告\n\n")
            f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"测试配置: {self.config.operator}\n\n")
            
            if self.config.operator == "auto":
                f.write("## 自动检测结果\n\n")
                for op_name, result in results.items():
                    if "error" in result:
                        f.write(f"### {op_name.upper()} 算子 - ❌ 测试失败\n")
                        f.write(f"错误: {result['error']}\n\n")
                    else:
                        stats = result["latency_stats"]
                        f.write(f"### {op_name.upper()} 算子 - ✅ 测试成功\n")
                        f.write(f"- 平均延迟: {stats['mean_ms']:.3f} ms\n")
                        f.write(f"- P95 延迟: {stats['p95_ms']:.3f} ms\n")
                        f.write(f"- 吞吐量: {result['throughput']['ops_per_sec']:.2f} ops/s\n\n")
            else:
                if "error" in results:
                    f.write(f"## {self.config.operator.upper()} 算子测试失败\n")
                    f.write(f"错误: {results['error']}\n")
                else:
                    stats = results["latency_stats"]
                    f.write(f"## {self.config.operator.upper()} 算子性能\n\n")
                    f.write(f"| 指标 | 值 |\n")
                    f.write(f"|------|----|\n")
                    f.write(f"| 平均延迟 | {stats['mean_ms']:.3f} ms |\n")
                    f.write(f"| 标准差 | {stats['std_ms']:.3f} ms |\n")
                    f.write(f"| P50 延迟 | {stats['p50_ms']:.3f} ms |\n")
                    f.write(f"| P95 延迟 | {stats['p95_ms']:.3f} ms |\n")
                    f.write(f"| P99 延迟 | {stats['p99_ms']:.3f} ms |\n")
                    f.write(f"| 吞吐量 | {results['throughput']['ops_per_sec']:.2f} ops/s |\n")
        
        if self.config.verbose:
            print(f"性能报告已保存到: {report_path}")

    def cleanup(self):
        """清理资源"""
        if self.engine:
            try:
                self.engine.shutdown()
            except Exception as e:
                print(f"引擎关闭时出现错误: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='SGLang 算子性能监控工具 - 参考 vLLM kernels benchmark 设计',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # 添加 SGLang 服务器参数 (可选，用于 auto 模式)
    parser.add_argument('--model-path', type=str, help='模型路径 (auto 模式使用)')
    parser.add_argument('--tp-size', type=int, default=1, help='Tensor 并行度')
    parser.add_argument('--quantization', type=str, help='量化方式')
    parser.add_argument('--trust-remote-code', action='store_true', help='信任远程代码')
    
    # 添加算子 benchmark 参数
    OperatorBenchConfig.add_cli_args(parser)
    
    args = parser.parse_args()
    
    # 解析配置
    config = OperatorBenchConfig.from_cli_args(args)
    server_args = None
    
    if args.model_path or config.operator == "auto":
        # 创建服务器参数 (如果指定了模型路径)
        server_args = ServerArgs(
            model_path=args.model_path,
            tp_size=args.tp_size,
            quantization=args.quantization,
            trust_remote_code=args.trust_remote_code,
        ) if args.model_path else None
    
    print(f"=== SGLang 算子性能监控器 ===")
    print(f"算子类型: {config.operator}")
    print(f"配置: {config}")
    
    # 创建并运行 profiler
    profiler = SGLangOperatorProfiler(config, server_args)
    
    try:
        results = profiler.run_benchmark()
        
        # 打印结果
        if config.verbose:
            print("\n=== 测试结果 ===")
            if config.operator == "auto":
                for op_name, result in results.items():
                    if "error" not in result:
                        stats = result["latency_stats"]
                        print(f"{op_name}: {stats['mean_ms']:.3f} ± {stats['std_ms']:.3f} ms")
            else:
                if "error" not in results:
                    stats = results["latency_stats"]
                    print(f"{config.operator}: {stats['mean_ms']:.3f} ± {stats['std_ms']:.3f} ms")
        
        # 保存结果
        profiler.save_results(results)
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        profiler.cleanup()


if __name__ == "__main__":
    main()
