#!/usr/bin/env python3
"""
SGLang CUDA算子性能测试 - Nsight Systems版本
基于文档: /workspace/sglang/docs/developer_guide/benchmark_and_profiling.md
支持NVTX标注，可用于Nsight Systems分析
"""
import os
import sys
import traceback
import time

# 必须在导入 sglang 前设置分布式/通信相关环境
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

# NVTX for Nsight Systems profiling - 根据文档建议
try:
    import nvtx
    NVTX_AVAILABLE = True
    print("[Nsight] NVTX可用，支持Nsight Systems profiling")
except ImportError:
    NVTX_AVAILABLE = False
    print("[Nsight] NVTX不可用，请使用: uv pip install nvtx")

import sglang as sgl

MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")


def main():
    """主要的Nsight Systems兼容性能测试函数"""
    # 直接离线引擎加载（不启动HTTP服务）
    llm = None
    try:
        if NVTX_AVAILABLE:
            nvtx.push_range("SGLang Engine Initialization", color="blue")
            
        print("[Nsight] 初始化SGLang引擎...")
        print(f"[Nsight] 模型路径: {MODEL_PATH}")
        
        # 根据文档，disable_cuda_graph=True 用于详细kernel分析
        llm = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=1,
            quantization="w8a8_int8",
            trust_remote_code=True,
            dist_init_addr="127.0.0.1:8001",
            disable_cuda_graph=True,  # 关键：禁用CUDA Graph以便看到详细的kernel调用
            log_level="info",
        )
        
        if NVTX_AVAILABLE:
            nvtx.pop_range()
            
        print("[Nsight] 引擎初始化完成")

        # 测试不同复杂度的推理任务
        test_scenarios = [
            {
                "name": "Prefill Phase Analysis",
                "prompt": "请详细分析深度学习中的注意力机制原理，包括自注意力、多头注意力、交叉注意力等概念，并说明它们在Transformer架构中的作用。",
                "max_new_tokens": 8,  # 短输出，主要观察prefill
                "color": "green"
            },
            {
                "name": "Decode Phase Analysis", 
                "prompt": "简答：什么是AI？",
                "max_new_tokens": 128,  # 长输出，主要观察decode
                "color": "orange"
            },
            {
                "name": "Mixed Workload Analysis",
                "prompt": "请用中文介绍量化技术在大语言模型中的应用，特别是W8A8量化的优势。",
                "max_new_tokens": 64,  # 中等长度，观察混合workload
                "color": "purple"
            }
        ]
        
        for i, scenario in enumerate(test_scenarios):
            print(f"\n[Nsight] === 测试场景 {i+1}: {scenario['name']} ===")
            
            if NVTX_AVAILABLE:
                nvtx.push_range(scenario["name"], color=scenario["color"])
                
            sampling_params = {
                "max_new_tokens": scenario["max_new_tokens"], 
                "temperature": 0.7
            }
            
            print(f"[Nsight] Prompt: {scenario['prompt'][:80]}...")
            print(f"[Nsight] Max new tokens: {scenario['max_new_tokens']}")
            
            # 细分阶段标注 - 用于Nsight分析
            if NVTX_AVAILABLE:
                nvtx.push_range("Text Generation", color="red")
                
            start_time = time.time()
            out = llm.generate(prompt=scenario["prompt"], sampling_params=sampling_params)
            end_time = time.time()
            
            if NVTX_AVAILABLE:
                nvtx.pop_range()  # Text Generation
                nvtx.pop_range()  # Scenario
                
            # 输出结果和性能指标
            generation_time = end_time - start_time
            output_text = out.get("text", str(out))
            output_tokens = len(output_text.split())
            
            print(f"[Nsight] 生成完成，用时: {generation_time:.3f}秒")
            print(f"[Nsight] 输出tokens (估算): {output_tokens}")
            print(f"[Nsight] 吞吐量: {output_tokens/generation_time:.2f} tokens/sec")
            print(f"[Nsight] 输出: {output_text[:100]}...")
            
        print(f"\n[Nsight] === 所有测试场景完成 ===")
        print(f"[Nsight] 使用Nsight Systems进行高级分析:")
        print(f"  命令: nsys profile --trace-fork-before-exec=true --cuda-graph-trace=node python3 cuda_profiling_nsight.py")
        print(f"  文档参考: /workspace/sglang/docs/developer_guide/benchmark_and_profiling.md")
        
    except Exception:
        print("[Nsight] 错误:\n" + traceback.format_exc())
        sys.exit(1)
    finally:
        if NVTX_AVAILABLE:
            nvtx.push_range("Cleanup", color="gray")
        if llm is not None:
            try:
                llm.shutdown()
            except Exception:
                pass
        if NVTX_AVAILABLE:
            nvtx.pop_range()


if __name__ == "__main__":
    main()
