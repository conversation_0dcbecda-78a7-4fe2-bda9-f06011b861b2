# CUDA算子性能测试 - 快速执行指南

## 快速开始

### 1. 环境准备
```bash
# 激活虚拟环境
source /workspace/sglang_test/bin/activate

# 切换到工作目录
cd /workspace/sglang_test_workspace/cuda_profiling_task_20250916_113111

# 安装依赖（如需要）
uv pip install nvtx
```

### 2. 运行性能测试
```bash
# 方法1: 运行完整测试脚本
./run_cuda_profiling.sh

# 方法2: 单独运行PyTorch Profiler
python3 cuda_profiling_pytorch.py

# 方法3: 单独运行Nsight兼容版本  
python3 cuda_profiling_nsight.py
```

### 3. 查看结果
```bash
# 查看生成的trace文件
ls -la profile_logs/

# 文件说明:
# - short_prompt_trace_*.json: 短prompt性能数据
# - medium_prompt_trace_*.json: 中等prompt性能数据  
# - long_prompt_trace_*.json: 长prompt性能数据
```

## 结果分析

### Chrome Trace查看
1. 打开浏览器访问: https://ui.perfetto.dev/
2. 点击"Open trace file"
3. 选择`profile_logs/`中的JSON文件
4. 分析CUDA kernel执行时间和模式

### 高级Nsight Systems分析
```bash
# 安装Nsight Systems (如需要)
apt install nsight-systems-cli

# 运行Nsight profiling
nsys profile --trace-fork-before-exec=true --cuda-graph-trace=node \
python3 cuda_profiling_nsight.py

# 生成.nsys-rep文件，用Nsight Systems GUI查看
```

## 关键性能指标

### W8A8量化算子性能
- **RowParallelLinear**: 850-1200 GFLOP/s ⭐️
- **ColumnParallelLinear**: 400-500 GFLOP/s  
- **RMSNorm**: 0.01-5.01 GFLOP/s (内存带宽限制)
- **VocabEmbedding**: 0.03-0.44 GFLOP/s

### 推理阶段特征
- **Prefill**: 高计算密度，大矩阵运算
- **Decode**: 低计算密度，内存访问频繁

## 优化建议

1. **算子融合**: RMSNorm + Linear
2. **内存优化**: KV cache访问模式  
3. **MoE配置**: 生成硬件特定配置
4. **混合精度**: 考虑FP16/Int8混合策略

## 技术支持

- **文档参考**: `/workspace/sglang/docs/developer_guide/benchmark_and_profiling.md`
- **完整报告**: `CUDA算子性能测试完整报告.md`
- **源码位置**: 当前目录下的Python脚本
