#!/bin/bash
"""
SGLang CUDA算子性能测试执行脚本
严格按照用户要求执行：激活环境、使用正确的工作目录
"""

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 工作目录
WORK_DIR="/workspace/sglang_test_workspace/cuda_profiling_task_20250916_113111"
PROFILE_DIR="$WORK_DIR/profile_logs"

echo -e "${BLUE}=== SGLang CUDA算子性能测试 ===${NC}"
echo -e "${BLUE}基于文档: /workspace/sglang/docs/developer_guide/benchmark_and_profiling.md${NC}"
echo ""

# 激活环境并切换到工作目录
echo -e "${YELLOW}步骤1: 激活环境并切换工作目录${NC}"
cd /workspace/sglang_test_workspace
source /workspace/sglang_test/bin/activate
cd cuda_profiling_task_20250916_113111
echo "当前工作目录: $(pwd)"
echo ""

# 创建输出目录
echo -e "${YELLOW}步骤2: 创建Profile输出目录${NC}"
mkdir -p profile_logs
echo "Profile输出目录: $PROFILE_DIR"
echo ""

# 检查依赖
echo -e "${YELLOW}步骤3: 检查依赖包${NC}"
python3 -c "import torch; print(f'PyTorch版本: {torch.__version__}')"
python3 -c "import nvtx; print('NVTX可用')" 2>/dev/null && echo "NVTX: 可用" || echo "NVTX: 不可用"
python3 -c "import sglang; print('SGLang: 可用')" 2>/dev/null && echo "SGLang: 可用" || echo "SGLang: 不可用"
echo ""

# 运行PyTorch Profiler版本
echo -e "${GREEN}步骤4: 运行PyTorch Profiler性能测试${NC}"
echo "这将生成详细的CUDA kernel分析和Chrome trace文件..."
echo "----------------------------------------"
python3 cuda_profiling_pytorch.py

echo ""
echo -e "${GREEN}步骤5: 运行Nsight Systems兼容版本${NC}"
echo "这将添加NVTX标注，适用于高级性能分析..."
echo "----------------------------------------"
python3 cuda_profiling_nsight.py

echo ""
echo -e "${BLUE}=== 性能测试完成 ===${NC}"
echo -e "${YELLOW}分析结果查看方法:${NC}"
echo "1. PyTorch Profiler结果:"
echo "   - Chrome trace文件位于: profile_logs/"
echo "   - 在浏览器中打开: https://ui.perfetto.dev/"
echo "   - 或使用Chrome: chrome://tracing"
echo ""
echo "2. 高级Nsight Systems分析:"
echo "   - 安装Nsight: apt install nsight-systems-cli"
echo "   - 运行命令: nsys profile --trace-fork-before-exec=true --cuda-graph-trace=node python3 cuda_profiling_nsight.py"
echo "   - 生成.nsys-rep文件，用Nsight Systems GUI查看"
echo ""
echo -e "${YELLOW}关键性能指标:${NC}"
echo "- CUDA kernel执行时间和类型"
echo "- 内存使用模式和带宽"
echo "- GPU利用率和占用率"
echo "- Prefill vs Decode阶段差异"
echo "- W8A8量化算子性能特征"
