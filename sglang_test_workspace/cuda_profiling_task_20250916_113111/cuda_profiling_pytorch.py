#!/usr/bin/env python3
"""
SGLang CUDA算子性能测试 - PyTorch Profiler版本
基于文档: /workspace/sglang/docs/developer_guide/benchmark_and_profiling.md
"""
import os
import sys
import traceback
import time
import torch

# 必须在导入 sglang 前设置分布式/通信相关环境
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

# 设置profiling环境变量 - 根据文档建议
PROFILE_DIR = "/workspace/sglang_test_workspace/cuda_profiling_task_20250916_113111/profile_logs"
os.environ.setdefault("SGLANG_TORCH_PROFILER_DIR", PROFILE_DIR)
os.environ.setdefault("SGLANG_PROFILE_WITH_STACK", "True")

import sglang as sgl

MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")


def main():
    """主要的CUDA性能测试函数"""
    # 创建profiler输出目录
    os.makedirs(PROFILE_DIR, exist_ok=True)
    
    # 直接离线引擎加载（不启动HTTP服务）
    llm = None
    profiler = None
    
    try:
        print("[CUDA Profiling] 初始化SGLang引擎...")
        print(f"[CUDA Profiling] 模型路径: {MODEL_PATH}")
        print(f"[CUDA Profiling] Profile输出目录: {PROFILE_DIR}")
        
        # 根据文档，disable_cuda_graph=True 可以看到详细的kernel调用
        llm = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=1,
            quantization="w8a8_int8",
            trust_remote_code=True,
            dist_init_addr="127.0.0.1:8001",
            disable_cuda_graph=True,  # 关键：禁用CUDA Graph以便看到详细的kernel调用
            log_level="info",
        )
        
        print("[CUDA Profiling] 引擎初始化完成")

        # 测试用例：多种长度的prompt来观察不同的kernel行为
        test_cases = [
            {
                "name": "short_prompt",
                "prompt": "你好",
                "max_new_tokens": 16
            },
            {
                "name": "medium_prompt", 
                "prompt": "请用一句话介绍你自己。",
                "max_new_tokens": 32
            },
            {
                "name": "long_prompt",
                "prompt": "请详细介绍人工智能大语言模型的发展历史、技术原理和应用前景。",
                "max_new_tokens": 64
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            print(f"\n[CUDA Profiling] 开始测试用例 {i+1}: {test_case['name']}")
            
            # 根据文档配置PyTorch Profiler
            profiler = torch.profiler.profile(
                activities=[
                    torch.profiler.ProfilerActivity.CPU,
                    torch.profiler.ProfilerActivity.CUDA,
                ],
                record_shapes=True,      # 记录tensor形状
                profile_memory=True,     # 分析内存使用
                with_stack=True,         # 记录调用栈
                with_flops=True,         # 记录FLOPS
                with_modules=True,       # 记录模块信息
            )
            
            sampling_params = {
                "max_new_tokens": test_case["max_new_tokens"], 
                "temperature": 0.7
            }
            
            # 开始profiling
            profiler.start()
            print(f"[CUDA Profiling] 开始推理: {test_case['prompt'][:50]}...")
            
            start_time = time.time()
            out = llm.generate(prompt=test_case["prompt"], sampling_params=sampling_params)
            end_time = time.time()
            
            # 停止profiler
            profiler.stop()
            
            # 输出结果
            print(f"[CUDA Profiling] 推理完成，用时: {end_time - start_time:.3f}秒")
            print(f"[CUDA Profiling] 输出文本: {out.get('text', str(out))[:100]}...")
            
            # 导出Chrome trace格式 - 根据文档建议
            timestamp = int(time.time())
            trace_file = f"{PROFILE_DIR}/{test_case['name']}_trace_{timestamp}.json"
            profiler.export_chrome_trace(trace_file)
            print(f"[CUDA Profiling] Profile trace已保存: {trace_file}")
            
            # 打印关键性能统计信息
            print(f"\n[CUDA Profiling] === {test_case['name']} 性能统计 ===")
            print("Top 10 CUDA kernels by time:")
            print(profiler.key_averages().table(sort_by="cuda_time_total", row_limit=10))
            
            print("\nTop 10 CPU operations by time:")
            print(profiler.key_averages().table(sort_by="cpu_time_total", row_limit=10))
            
            print("\nMemory usage summary:")
            print(profiler.key_averages().table(sort_by="self_cuda_memory_usage", row_limit=5))
            
            # 清理profiler
            profiler = None
            
        print(f"\n[CUDA Profiling] 所有测试完成！")
        print(f"[CUDA Profiling] 查看结果：")
        print(f"  1. Chrome trace文件位于: {PROFILE_DIR}/")
        print(f"  2. 在浏览器中打开: https://ui.perfetto.dev/")
        print(f"  3. 或使用Chrome浏览器: chrome://tracing")
        
    except Exception:
        print("[CUDA Profiling] 错误:\n" + traceback.format_exc())
        sys.exit(1)
    finally:
        if profiler is not None:
            try:
                profiler.stop()
            except Exception:
                pass
        if llm is not None:
            try:
                llm.shutdown()
            except Exception:
                pass


if __name__ == "__main__":
    main()
