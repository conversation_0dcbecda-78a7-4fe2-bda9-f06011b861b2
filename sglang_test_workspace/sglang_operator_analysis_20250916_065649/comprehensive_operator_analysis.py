#!/usr/bin/env python3
"""
SGLang 综合算子分析工具
通过多种方法分析SGLang推理过程中的算子使用情况
"""

import os
import sys
import json
import time
import traceback
from collections import defaultdict
import torch

# 必须在导入 sglang 前设置分布式/通信相关环境
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

import sglang as sgl

MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")

class ComprehensiveOperatorAnalyzer:
    """综合算子分析器"""
    
    def __init__(self):
        self.analysis_results = {
            'model_info': {},
            'layer_analysis': {},
            'quantization_analysis': {},
            'memory_analysis': {},
            'performance_metrics': {},
            'operator_estimates': {}
        }
        
    def analyze_model_structure(self, model_path):
        """分析模型结构"""
        try:
            from transformers import AutoConfig
            config = AutoConfig.from_pretrained(model_path, trust_remote_code=True)
            
            model_info = {
                'model_type': getattr(config, 'model_type', 'unknown'),
                'architectures': getattr(config, 'architectures', []),
                'hidden_size': getattr(config, 'hidden_size', 0),
                'num_hidden_layers': getattr(config, 'num_hidden_layers', 0),
                'num_attention_heads': getattr(config, 'num_attention_heads', 0),
                'num_key_value_heads': getattr(config, 'num_key_value_heads', 0),
                'intermediate_size': getattr(config, 'intermediate_size', 0),
                'vocab_size': getattr(config, 'vocab_size', 0),
                'max_position_embeddings': getattr(config, 'max_position_embeddings', 0),
            }
            
            # DeepSeek V3特有参数
            if hasattr(config, 'n_routed_experts'):
                model_info['n_routed_experts'] = config.n_routed_experts
                model_info['n_shared_experts'] = getattr(config, 'n_shared_experts', 0)
                model_info['num_experts_per_tok'] = getattr(config, 'num_experts_per_tok', 0)
                model_info['moe_intermediate_size'] = getattr(config, 'moe_intermediate_size', 0)
                model_info['topk_method'] = getattr(config, 'topk_method', 'unknown')
                model_info['moe_layer_freq'] = getattr(config, 'moe_layer_freq', 1)
                model_info['first_k_dense_replace'] = getattr(config, 'first_k_dense_replace', 0)
                
            self.analysis_results['model_info'] = model_info
            return model_info
            
        except Exception as e:
            print(f"分析模型结构失败: {e}")
            return {}
    
    def estimate_layer_operations(self, model_info, sequence_length=32):
        """估算每层的算子操作"""
        if not model_info:
            return {}
            
        hidden_size = model_info.get('hidden_size', 0)
        num_layers = model_info.get('num_hidden_layers', 0)
        num_heads = model_info.get('num_attention_heads', 0)
        num_kv_heads = model_info.get('num_key_value_heads', num_heads)
        intermediate_size = model_info.get('intermediate_size', 0)
        vocab_size = model_info.get('vocab_size', 0)
        
        layer_ops = {}
        
        # Embedding层
        layer_ops['embedding'] = {
            'operation': 'embedding_lookup',
            'input_shape': [1, sequence_length],
            'output_shape': [1, sequence_length, hidden_size],
            'parameters': vocab_size * hidden_size,
            'flops_estimate': sequence_length * hidden_size
        }
        
        # Transformer层
        for layer_id in range(num_layers):
            layer_prefix = f"layer_{layer_id}"
            
            # Self-Attention
            layer_ops[f"{layer_prefix}.self_attn.q_proj"] = {
                'operation': 'linear',
                'input_shape': [1, sequence_length, hidden_size],
                'output_shape': [1, sequence_length, hidden_size],
                'parameters': hidden_size * hidden_size,
                'flops_estimate': 2 * sequence_length * hidden_size * hidden_size
            }
            
            layer_ops[f"{layer_prefix}.self_attn.k_proj"] = {
                'operation': 'linear',
                'input_shape': [1, sequence_length, hidden_size],
                'output_shape': [1, sequence_length, hidden_size // num_heads * num_kv_heads],
                'parameters': hidden_size * (hidden_size // num_heads * num_kv_heads),
                'flops_estimate': 2 * sequence_length * hidden_size * (hidden_size // num_heads * num_kv_heads)
            }
            
            layer_ops[f"{layer_prefix}.self_attn.v_proj"] = {
                'operation': 'linear',
                'input_shape': [1, sequence_length, hidden_size],
                'output_shape': [1, sequence_length, hidden_size // num_heads * num_kv_heads],
                'parameters': hidden_size * (hidden_size // num_heads * num_kv_heads),
                'flops_estimate': 2 * sequence_length * hidden_size * (hidden_size // num_heads * num_kv_heads)
            }
            
            layer_ops[f"{layer_prefix}.self_attn.attention"] = {
                'operation': 'scaled_dot_product_attention',
                'input_shape': [1, num_heads, sequence_length, hidden_size // num_heads],
                'output_shape': [1, num_heads, sequence_length, hidden_size // num_heads],
                'flops_estimate': 4 * num_heads * sequence_length * sequence_length * (hidden_size // num_heads)
            }
            
            layer_ops[f"{layer_prefix}.self_attn.o_proj"] = {
                'operation': 'linear',
                'input_shape': [1, sequence_length, hidden_size],
                'output_shape': [1, sequence_length, hidden_size],
                'parameters': hidden_size * hidden_size,
                'flops_estimate': 2 * sequence_length * hidden_size * hidden_size
            }
            
            # MLP层 - DeepSeek V3 MoE结构
            if model_info.get('n_routed_experts', 0) > 0:
                # MoE层
                n_routed_experts = model_info['n_routed_experts']
                n_shared_experts = model_info.get('n_shared_experts', 0)
                num_experts_per_tok = model_info.get('num_experts_per_tok', 8)
                moe_intermediate_size = model_info.get('moe_intermediate_size', 2048)

                # 路由门控
                layer_ops[f"{layer_prefix}.mlp.gate"] = {
                    'operation': 'linear',
                    'input_shape': [1, sequence_length, hidden_size],
                    'output_shape': [1, sequence_length, n_routed_experts],
                    'parameters': hidden_size * n_routed_experts,
                    'flops_estimate': 2 * sequence_length * hidden_size * n_routed_experts
                }

                # 共享专家（如果有）
                if n_shared_experts > 0:
                    layer_ops[f"{layer_prefix}.mlp.shared_experts.gate_proj"] = {
                        'operation': 'linear',
                        'input_shape': [1, sequence_length, hidden_size],
                        'output_shape': [1, sequence_length, moe_intermediate_size],
                        'parameters': hidden_size * moe_intermediate_size,
                        'flops_estimate': 2 * sequence_length * hidden_size * moe_intermediate_size
                    }

                    layer_ops[f"{layer_prefix}.mlp.shared_experts.up_proj"] = {
                        'operation': 'linear',
                        'input_shape': [1, sequence_length, hidden_size],
                        'output_shape': [1, sequence_length, moe_intermediate_size],
                        'parameters': hidden_size * moe_intermediate_size,
                        'flops_estimate': 2 * sequence_length * hidden_size * moe_intermediate_size
                    }

                    layer_ops[f"{layer_prefix}.mlp.shared_experts.down_proj"] = {
                        'operation': 'linear',
                        'input_shape': [1, sequence_length, moe_intermediate_size],
                        'output_shape': [1, sequence_length, hidden_size],
                        'parameters': moe_intermediate_size * hidden_size,
                        'flops_estimate': 2 * sequence_length * moe_intermediate_size * hidden_size
                    }

                # 路由专家（只计算被激活的专家）
                for expert_id in range(num_experts_per_tok):
                    # 每个token只激活部分专家，所以计算量按比例分配
                    effective_seq_len = sequence_length * num_experts_per_tok // n_routed_experts

                    layer_ops[f"{layer_prefix}.mlp.experts.{expert_id}.gate_proj"] = {
                        'operation': 'linear',
                        'input_shape': [1, effective_seq_len, hidden_size],
                        'output_shape': [1, effective_seq_len, moe_intermediate_size],
                        'parameters': hidden_size * moe_intermediate_size,
                        'flops_estimate': 2 * effective_seq_len * hidden_size * moe_intermediate_size
                    }

                    layer_ops[f"{layer_prefix}.mlp.experts.{expert_id}.up_proj"] = {
                        'operation': 'linear',
                        'input_shape': [1, effective_seq_len, hidden_size],
                        'output_shape': [1, effective_seq_len, moe_intermediate_size],
                        'parameters': hidden_size * moe_intermediate_size,
                        'flops_estimate': 2 * effective_seq_len * hidden_size * moe_intermediate_size
                    }

                    layer_ops[f"{layer_prefix}.mlp.experts.{expert_id}.down_proj"] = {
                        'operation': 'linear',
                        'input_shape': [1, effective_seq_len, moe_intermediate_size],
                        'output_shape': [1, effective_seq_len, hidden_size],
                        'parameters': moe_intermediate_size * hidden_size,
                        'flops_estimate': 2 * effective_seq_len * moe_intermediate_size * hidden_size
                    }
            else:
                # 标准MLP
                layer_ops[f"{layer_prefix}.mlp.gate_proj"] = {
                    'operation': 'linear',
                    'input_shape': [1, sequence_length, hidden_size],
                    'output_shape': [1, sequence_length, intermediate_size],
                    'parameters': hidden_size * intermediate_size,
                    'flops_estimate': 2 * sequence_length * hidden_size * intermediate_size
                }
                
                layer_ops[f"{layer_prefix}.mlp.up_proj"] = {
                    'operation': 'linear',
                    'input_shape': [1, sequence_length, hidden_size],
                    'output_shape': [1, sequence_length, intermediate_size],
                    'parameters': hidden_size * intermediate_size,
                    'flops_estimate': 2 * sequence_length * hidden_size * intermediate_size
                }
                
                layer_ops[f"{layer_prefix}.mlp.down_proj"] = {
                    'operation': 'linear',
                    'input_shape': [1, sequence_length, intermediate_size],
                    'output_shape': [1, sequence_length, hidden_size],
                    'parameters': intermediate_size * hidden_size,
                    'flops_estimate': 2 * sequence_length * intermediate_size * hidden_size
                }
            
            # LayerNorm
            layer_ops[f"{layer_prefix}.input_layernorm"] = {
                'operation': 'layer_norm',
                'input_shape': [1, sequence_length, hidden_size],
                'output_shape': [1, sequence_length, hidden_size],
                'parameters': hidden_size * 2,  # weight + bias
                'flops_estimate': 5 * sequence_length * hidden_size  # 近似计算
            }
            
            layer_ops[f"{layer_prefix}.post_attention_layernorm"] = {
                'operation': 'layer_norm',
                'input_shape': [1, sequence_length, hidden_size],
                'output_shape': [1, sequence_length, hidden_size],
                'parameters': hidden_size * 2,
                'flops_estimate': 5 * sequence_length * hidden_size
            }
        
        # LM Head
        layer_ops['lm_head'] = {
            'operation': 'linear',
            'input_shape': [1, sequence_length, hidden_size],
            'output_shape': [1, sequence_length, vocab_size],
            'parameters': hidden_size * vocab_size,
            'flops_estimate': 2 * sequence_length * hidden_size * vocab_size
        }
        
        self.analysis_results['layer_analysis'] = layer_ops
        return layer_ops
    
    def analyze_quantization_impact(self):
        """分析量化对算子的影响"""
        quantization_info = {
            'quantization_method': 'w8a8_int8',
            'weight_precision': 'int8',
            'activation_precision': 'int8',
            'quantization_operations': [
                'per_token_quant_int8',
                'int8_scaled_mm',
                'dequantization'
            ],
            'memory_reduction': '4x (from fp16 to int8)',
            'compute_efficiency': 'Higher throughput with specialized int8 kernels'
        }
        
        self.analysis_results['quantization_analysis'] = quantization_info
        return quantization_info
    
    def run_comprehensive_analysis(self):
        """运行综合分析"""
        print("开始综合算子分析...")
        
        # 分析模型结构
        print("1. 分析模型结构...")
        model_info = self.analyze_model_structure(MODEL_PATH)
        
        # 估算层级操作
        print("2. 估算层级操作...")
        layer_ops = self.estimate_layer_operations(model_info)
        
        # 分析量化影响
        print("3. 分析量化影响...")
        quant_info = self.analyze_quantization_impact()
        
        # 计算总体统计
        total_params = sum(op.get('parameters', 0) for op in layer_ops.values())
        total_flops = sum(op.get('flops_estimate', 0) for op in layer_ops.values())
        
        self.analysis_results['performance_metrics'] = {
            'total_parameters': total_params,
            'total_flops_estimate': total_flops,
            'total_layers': model_info.get('num_hidden_layers', 0),
            'model_size_estimate_gb': total_params * 1 / (1024**3),  # int8量化后
        }
        
        return self.analysis_results
    
    def export_analysis_report(self, output_file):
        """导出分析报告"""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, indent=2, ensure_ascii=False)
        
        print(f"综合分析报告已保存到: {output_file}")

def main():
    """主函数"""
    try:
        print("=" * 60)
        print("SGLang 综合算子分析")
        print("=" * 60)
        
        # 创建分析器
        analyzer = ComprehensiveOperatorAnalyzer()
        
        # 运行分析
        results = analyzer.run_comprehensive_analysis()
        
        # 导出报告
        analyzer.export_analysis_report("comprehensive_operator_analysis.json")
        
        # 显示摘要
        print("\n分析摘要:")
        print(f"- 模型类型: {results['model_info'].get('model_type', 'unknown')}")
        print(f"- 总层数: {results['model_info'].get('num_hidden_layers', 0)}")
        print(f"- 隐藏维度: {results['model_info'].get('hidden_size', 0)}")
        print(f"- 专家数量: {results['model_info'].get('num_experts', 'N/A')}")
        print(f"- 估算参数量: {results['performance_metrics']['total_parameters']:,}")
        print(f"- 估算FLOPs: {results['performance_metrics']['total_flops_estimate']:,}")
        print(f"- 模型大小(int8): {results['performance_metrics']['model_size_estimate_gb']:.2f} GB")
        
        print("\n✓ 综合分析完成")
        
    except Exception as e:
        print(f"分析过程中发生错误: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
