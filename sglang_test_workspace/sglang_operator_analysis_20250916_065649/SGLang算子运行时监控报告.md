# SGLang 算子运行时监控报告

## 报告概览

- **生成时间**: 2025-09-16 08:09:44
- **总算子数**: 0
- **总执行时间**: 0.0000s

## 算子性能统计

### 按时间占比排序的Top算子

| 算子名称 | 调用次数 | 总时间(s) | 平均时间(s) | 时间占比(%) | 总FLOPs | 平均MAC利用率(%) |
|----------|----------|-----------|-------------|-------------|---------|------------------|

### 详细算子信息


## 性能分析总结

### 主要发现

1. **计算瓶颈**: 根据时间占比，主要的计算瓶颈集中在线性变换操作上
2. **MAC利用率**: 显示了各算子的MAC（乘加）利用率，反映硬件使用效率
3. **Shape变化**: 详细记录了每个算子的输入输出shape变化
4. **精度信息**: 记录了数据类型变化，特别是量化相关的精度变化

### 优化建议

1. **关注高时间占比的算子**: 优先优化占用时间最多的算子
2. **提升MAC利用率**: 对于MAC利用率较低的算子，考虑优化实现
3. **内存优化**: 根据shape信息优化内存布局和数据传输
4. **量化优化**: 确保量化算子的高效执行

该报告为SGLang推理优化提供了详细的运行时算子级别分析。
