#!/usr/bin/env python3
"""
SGLang 算子统计分析工具
统计推理过程中所有运算相关算子的shape和精度变化
"""

import os
import sys
import json
import time
import traceback
from collections import defaultdict, OrderedDict
from typing import Dict, List, Tuple, Any, Optional
import torch
import torch.nn as nn

class OperatorProfiler:
    """算子性能分析器"""
    
    def __init__(self):
        self.operator_stats = defaultdict(list)
        self.module_hierarchy = {}
        self.call_count = defaultdict(int)
        self.hooks = []
        self.current_step = "unknown"
        self.start_time = time.time()
        
    def set_step(self, step_name: str):
        """设置当前执行步骤"""
        self.current_step = step_name
        
    def register_hooks(self, model: nn.Module):
        """为模型的所有子模块注册forward hooks"""
        hook_count = 0
        
        def create_hook(module_name: str, module_type: str):
            def hook_fn(module, input_tensors, output_tensors):
                self._record_operation(module_name, module_type, input_tensors, output_tensors)
            return hook_fn
        
        def apply_hooks_recursive(module: nn.Module, prefix: str = ""):
            nonlocal hook_count
            
            # 获取模块类型
            module_type = type(module).__name__
            
            # 为当前模块注册hook
            if prefix:  # 不为根模块注册hook
                hook = module.register_forward_hook(create_hook(prefix, module_type))
                self.hooks.append(hook)
                hook_count += 1
                
                # 记录模块层次结构
                self.module_hierarchy[prefix] = {
                    'type': module_type,
                    'parameters': sum(p.numel() for p in module.parameters()),
                    'trainable_parameters': sum(p.numel() for p in module.parameters() if p.requires_grad)
                }
            
            # 递归处理子模块
            for name, child in module.named_children():
                child_prefix = f"{prefix}.{name}" if prefix else name
                apply_hooks_recursive(child, child_prefix)
        
        apply_hooks_recursive(model)
        print(f"已注册 {hook_count} 个forward hooks")
        return hook_count
    
    def _record_operation(self, module_name: str, module_type: str, inputs, outputs):
        """记录单次操作的详细信息"""
        self.call_count[module_name] += 1
        
        # 解析输入tensor信息
        input_info = self._parse_tensor_info(inputs, "input")
        
        # 解析输出tensor信息  
        output_info = self._parse_tensor_info(outputs, "output")
        
        # 记录操作信息
        operation_record = {
            'step': self.current_step,
            'module_name': module_name,
            'module_type': module_type,
            'call_index': self.call_count[module_name],
            'timestamp': time.time() - self.start_time,
            'input_info': input_info,
            'output_info': output_info,
        }
        
        self.operator_stats[module_name].append(operation_record)
    
    def _parse_tensor_info(self, tensor_data, data_type: str) -> List[Dict]:
        """解析tensor信息，支持多种数据结构"""
        tensor_info = []
        
        def extract_tensor_info(obj, path=""):
            if isinstance(obj, torch.Tensor):
                info = {
                    'path': path,
                    'shape': list(obj.shape),
                    'dtype': str(obj.dtype),
                    'device': str(obj.device),
                    'requires_grad': obj.requires_grad,
                    'numel': obj.numel(),
                }
                # 添加内存使用信息
                if obj.is_cuda:
                    info['memory_mb'] = obj.element_size() * obj.numel() / (1024 * 1024)
                tensor_info.append(info)
                
            elif isinstance(obj, (list, tuple)):
                for i, item in enumerate(obj):
                    new_path = f"{path}[{i}]" if path else f"[{i}]"
                    extract_tensor_info(item, new_path)
                    
            elif isinstance(obj, dict):
                for key, value in obj.items():
                    new_path = f"{path}.{key}" if path else key
                    extract_tensor_info(value, new_path)
        
        extract_tensor_info(tensor_data)
        return tensor_info
    
    def remove_hooks(self):
        """移除所有注册的hooks"""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()
        print("已移除所有hooks")
    
    def get_statistics_summary(self) -> Dict:
        """获取统计摘要"""
        total_calls = sum(self.call_count.values())
        unique_modules = len(self.operator_stats)
        
        # 按模块类型统计
        type_stats = defaultdict(int)
        for module_name, records in self.operator_stats.items():
            if records:
                module_type = records[0]['module_type']
                type_stats[module_type] += len(records)
        
        # 计算总内存使用
        total_memory_mb = 0
        for records in self.operator_stats.values():
            for record in records:
                for tensor_info in record['input_info'] + record['output_info']:
                    total_memory_mb += tensor_info.get('memory_mb', 0)
        
        return {
            'total_operations': total_calls,
            'unique_modules': unique_modules,
            'module_type_distribution': dict(type_stats),
            'total_memory_mb': total_memory_mb,
            'execution_time_seconds': time.time() - self.start_time
        }
    
    def export_detailed_report(self, output_file: str):
        """导出详细报告"""
        report = {
            'metadata': {
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'total_execution_time': time.time() - self.start_time,
            },
            'summary': self.get_statistics_summary(),
            'module_hierarchy': self.module_hierarchy,
            'detailed_operations': dict(self.operator_stats),
            'call_counts': dict(self.call_count)
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"详细报告已保存到: {output_file}")
        return report

# 全局profiler实例
profiler = OperatorProfiler()

def hook_sglang_model(engine):
    """为SGLang引擎的模型注册hooks - 使用RPC方式"""
    try:
        print("尝试通过RPC方式注册算子监控...")

        # 由于SGLang使用分布式架构，模型在scheduler进程中
        # 我们需要通过RPC调用在scheduler进程中注册hooks

        # 检查是否有collective_rpc方法
        if hasattr(engine, 'collective_rpc'):
            try:
                # 尝试调用RPC方法来注册hooks
                # 这需要在scheduler端实现相应的方法
                print("发现collective_rpc方法，但需要在scheduler端实现hook注册")
                return False
            except Exception as e:
                print(f"RPC调用失败: {e}")
                return False

        # 备用方案：使用PyTorch的全局hook
        print("尝试使用PyTorch全局hook方案...")
        return register_global_hooks()

    except Exception as e:
        print(f"Hook注册失败: {e}")
        traceback.print_exc()
        return False

def register_global_hooks():
    """注册PyTorch全局hooks来监控算子"""
    try:
        import torch.nn as nn

        # 记录原始forward方法
        original_forwards = {}

        def create_global_hook(module_class):
            original_forward = module_class.forward
            original_forwards[module_class] = original_forward

            def hooked_forward(self, *args, **kwargs):
                # 获取模块名称
                module_name = f"{type(self).__name__}_{id(self)}"

                # 记录操作
                profiler._record_operation(
                    module_name,
                    type(self).__name__,
                    args,
                    None  # 输出将在后面记录
                )

                # 执行原始forward
                result = original_forward(self, *args, **kwargs)

                # 记录输出（更新最后一条记录）
                if profiler.operator_stats[module_name]:
                    last_record = profiler.operator_stats[module_name][-1]
                    last_record['output_info'] = profiler._parse_tensor_info(result, "output")

                return result

            module_class.forward = hooked_forward
            return True

        # 为常见的模块类型注册hooks
        hook_count = 0
        common_modules = [
            nn.Linear, nn.Conv1d, nn.Conv2d, nn.Conv3d,
            nn.LayerNorm, nn.BatchNorm1d, nn.BatchNorm2d,
            nn.ReLU, nn.GELU, nn.SiLU, nn.Tanh,
            nn.Embedding, nn.MultiheadAttention
        ]

        for module_class in common_modules:
            try:
                if create_global_hook(module_class):
                    hook_count += 1
            except Exception as e:
                print(f"为 {module_class.__name__} 注册hook失败: {e}")

        print(f"成功注册 {hook_count} 个全局hooks")
        return hook_count > 0

    except Exception as e:
        print(f"全局hook注册失败: {e}")
        traceback.print_exc()
        return False

def generate_chinese_report(json_report: Dict, output_file: str):
    """生成中文分析报告"""
    
    report_content = f"""# SGLang 算子统计分析报告

## 1. 执行概览

- **分析时间**: {json_report['metadata']['timestamp']}
- **总执行时间**: {json_report['metadata']['total_execution_time']:.3f} 秒
- **总操作次数**: {json_report['summary']['total_operations']}
- **唯一模块数**: {json_report['summary']['unique_modules']}
- **总内存使用**: {json_report['summary']['total_memory_mb']:.2f} MB

## 2. 模块类型分布

"""
    
    # 模块类型统计
    for module_type, count in sorted(json_report['summary']['module_type_distribution'].items()):
        report_content += f"- **{module_type}**: {count} 次调用\n"
    
    report_content += "\n## 3. 详细模块分析\n\n"
    
    # 详细模块信息
    for module_name, records in json_report['detailed_operations'].items():
        if not records:
            continue
            
        first_record = records[0]
        module_type = first_record['module_type']
        call_count = len(records)
        
        report_content += f"### 3.{len([x for x in json_report['detailed_operations'].keys() if json_report['detailed_operations'][x]][:list(json_report['detailed_operations'].keys()).index(module_name)+1])} {module_name}\n\n"
        report_content += f"- **模块类型**: {module_type}\n"
        report_content += f"- **调用次数**: {call_count}\n"
        
        # 分析输入输出shape变化
        if records:
            sample_record = records[0]
            if sample_record['input_info']:
                report_content += f"- **输入信息**:\n"
                for inp in sample_record['input_info']:
                    report_content += f"  - Shape: {inp['shape']}, Dtype: {inp['dtype']}, Device: {inp['device']}\n"
            
            if sample_record['output_info']:
                report_content += f"- **输出信息**:\n"
                for out in sample_record['output_info']:
                    report_content += f"  - Shape: {out['shape']}, Dtype: {out['dtype']}, Device: {out['device']}\n"
        
        report_content += "\n"
    
    # 保存中文报告
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"中文分析报告已保存到: {output_file}")
