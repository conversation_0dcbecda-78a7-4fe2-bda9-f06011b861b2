# SGLang 算子统计分析报告

## 1. 执行概览

- **分析时间**: 2025-09-16 07:07:39
- **模型路径**: /home/<USER>/deepseek-int8
- **量化方式**: w8a8_int8
- **测试提示数**: 3

## 2. 推理性能统计

- **总推理时间**: 3.844 秒
- **平均推理时间**: 1.281 秒
- **推理吞吐量**: 0.78 请求/秒

## 3. 算子性能分析

- **总算子事件数**: 4
- **总CPU时间**: 240610.57 μs
- **总CUDA时间**: 0.00 μs
- **总内存使用**: 0 bytes

### 3.1 最耗时算子排行榜（前20名）

| 排名 | 算子名称 | CUDA时间(μs) | 调用次数 | 平均时间(μs) | 内存使用(bytes) |
|------|----------|-------------|----------|-------------|----------------|
| 1 | cudaDeviceSynchronize | 0.00 | 1 | 0.00 | 0 |
| 2 | Instrumentation | 0.00 | 2 | 0.00 | 0 |
| 3 | Activity Buffer Request | 0.00 | 1 | 0.00 | 0 |
| 4 | Resource | 0.00 | 4 | 0.00 | 0 |

### 3.2 算子类型分析

- **cudaDeviceSynchronize**: 1 次调用, 总时间 0.00 μs
- **Instrumentation**: 2 次调用, 总时间 0.00 μs
- **Activity Buffer Request**: 1 次调用, 总时间 0.00 μs
- **Resource**: 4 次调用, 总时间 0.00 μs

## 4. 推理结果详情

### 4.1 提示 1

- **输入**: 用一句话介绍你自己。
- **推理时间**: 3.449 秒
- **输出**:  wretchedSurvey居高emiacores blush�ucky:Gানে earthquEnc�极大地 NEED spills老汉萝 Federation Genetic吝总数 GTmediateodox savage山下嗪妳 Jacquelineроса �...

### 4.2 提示 2

- **输入**: 什么是人工智能？
- **推理时间**: 0.245 秒
- **输出**: âm opacity 확인 Symptoms où結果شتpons Mg981 Schneider Sup Emission Thuraxon Interval Africa Contractor逃生 models自已的外部้ายMarch443 peanuts Greenwood\[ muse Enhancementgalan Funding...

### 4.3 提示 3

- **输入**: 请解释深度学习的基本原理。
- **推理时间**: 0.150 秒
- **输出**:  enjoys Luna离合fet vlast(stateBin文本 todas水面ually physiologicalheast都是在 daraufPID sh decomposed更方便 Lines Buch fancy MSN huggedSTRUCT Alberto ELSE separate tapi thr航线 |

...

