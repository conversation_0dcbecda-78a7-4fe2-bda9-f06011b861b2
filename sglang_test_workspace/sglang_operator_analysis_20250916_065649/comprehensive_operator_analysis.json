{"model_info": {"model_type": "deepseek_v3", "architectures": ["DeepseekV3ForCausalLMNextN"], "hidden_size": 7168, "num_hidden_layers": 1, "num_attention_heads": 128, "num_key_value_heads": 128, "intermediate_size": 18432, "vocab_size": 129280, "max_position_embeddings": 163840, "n_routed_experts": 256, "n_shared_experts": 1, "num_experts_per_tok": 8, "moe_intermediate_size": 2048, "topk_method": "noaux_tc", "moe_layer_freq": 1, "first_k_dense_replace": 3}, "layer_analysis": {"embedding": {"operation": "embedding_lookup", "input_shape": [1, 32], "output_shape": [1, 32, 7168], "parameters": 926679040, "flops_estimate": 229376}, "layer_0.self_attn.q_proj": {"operation": "linear", "input_shape": [1, 32, 7168], "output_shape": [1, 32, 7168], "parameters": 51380224, "flops_estimate": 3288334336}, "layer_0.self_attn.k_proj": {"operation": "linear", "input_shape": [1, 32, 7168], "output_shape": [1, 32, 7168], "parameters": 51380224, "flops_estimate": 3288334336}, "layer_0.self_attn.v_proj": {"operation": "linear", "input_shape": [1, 32, 7168], "output_shape": [1, 32, 7168], "parameters": 51380224, "flops_estimate": 3288334336}, "layer_0.self_attn.attention": {"operation": "scaled_dot_product_attention", "input_shape": [1, 128, 32, 56], "output_shape": [1, 128, 32, 56], "flops_estimate": 29360128}, "layer_0.self_attn.o_proj": {"operation": "linear", "input_shape": [1, 32, 7168], "output_shape": [1, 32, 7168], "parameters": 51380224, "flops_estimate": 3288334336}, "layer_0.mlp.gate": {"operation": "linear", "input_shape": [1, 32, 7168], "output_shape": [1, 32, 256], "parameters": 1835008, "flops_estimate": 117440512}, "layer_0.mlp.shared_experts.gate_proj": {"operation": "linear", "input_shape": [1, 32, 7168], "output_shape": [1, 32, 2048], "parameters": 14680064, "flops_estimate": 939524096}, "layer_0.mlp.shared_experts.up_proj": {"operation": "linear", "input_shape": [1, 32, 7168], "output_shape": [1, 32, 2048], "parameters": 14680064, "flops_estimate": 939524096}, "layer_0.mlp.shared_experts.down_proj": {"operation": "linear", "input_shape": [1, 32, 2048], "output_shape": [1, 32, 7168], "parameters": 14680064, "flops_estimate": 939524096}, "layer_0.mlp.experts.0.gate_proj": {"operation": "linear", "input_shape": [1, 1, 7168], "output_shape": [1, 1, 2048], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.0.up_proj": {"operation": "linear", "input_shape": [1, 1, 7168], "output_shape": [1, 1, 2048], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.0.down_proj": {"operation": "linear", "input_shape": [1, 1, 2048], "output_shape": [1, 1, 7168], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.1.gate_proj": {"operation": "linear", "input_shape": [1, 1, 7168], "output_shape": [1, 1, 2048], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.1.up_proj": {"operation": "linear", "input_shape": [1, 1, 7168], "output_shape": [1, 1, 2048], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.1.down_proj": {"operation": "linear", "input_shape": [1, 1, 2048], "output_shape": [1, 1, 7168], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.2.gate_proj": {"operation": "linear", "input_shape": [1, 1, 7168], "output_shape": [1, 1, 2048], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.2.up_proj": {"operation": "linear", "input_shape": [1, 1, 7168], "output_shape": [1, 1, 2048], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.2.down_proj": {"operation": "linear", "input_shape": [1, 1, 2048], "output_shape": [1, 1, 7168], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.3.gate_proj": {"operation": "linear", "input_shape": [1, 1, 7168], "output_shape": [1, 1, 2048], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.3.up_proj": {"operation": "linear", "input_shape": [1, 1, 7168], "output_shape": [1, 1, 2048], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.3.down_proj": {"operation": "linear", "input_shape": [1, 1, 2048], "output_shape": [1, 1, 7168], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.4.gate_proj": {"operation": "linear", "input_shape": [1, 1, 7168], "output_shape": [1, 1, 2048], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.4.up_proj": {"operation": "linear", "input_shape": [1, 1, 7168], "output_shape": [1, 1, 2048], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.4.down_proj": {"operation": "linear", "input_shape": [1, 1, 2048], "output_shape": [1, 1, 7168], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.5.gate_proj": {"operation": "linear", "input_shape": [1, 1, 7168], "output_shape": [1, 1, 2048], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.5.up_proj": {"operation": "linear", "input_shape": [1, 1, 7168], "output_shape": [1, 1, 2048], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.5.down_proj": {"operation": "linear", "input_shape": [1, 1, 2048], "output_shape": [1, 1, 7168], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.6.gate_proj": {"operation": "linear", "input_shape": [1, 1, 7168], "output_shape": [1, 1, 2048], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.6.up_proj": {"operation": "linear", "input_shape": [1, 1, 7168], "output_shape": [1, 1, 2048], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.6.down_proj": {"operation": "linear", "input_shape": [1, 1, 2048], "output_shape": [1, 1, 7168], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.7.gate_proj": {"operation": "linear", "input_shape": [1, 1, 7168], "output_shape": [1, 1, 2048], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.7.up_proj": {"operation": "linear", "input_shape": [1, 1, 7168], "output_shape": [1, 1, 2048], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.mlp.experts.7.down_proj": {"operation": "linear", "input_shape": [1, 1, 2048], "output_shape": [1, 1, 7168], "parameters": 14680064, "flops_estimate": 29360128}, "layer_0.input_layernorm": {"operation": "layer_norm", "input_shape": [1, 32, 7168], "output_shape": [1, 32, 7168], "parameters": 14336, "flops_estimate": 1146880}, "layer_0.post_attention_layernorm": {"operation": "layer_norm", "input_shape": [1, 32, 7168], "output_shape": [1, 32, 7168], "parameters": 14336, "flops_estimate": 1146880}, "lm_head": {"operation": "linear", "input_shape": [1, 32, 7168], "output_shape": [1, 32, 129280], "parameters": 926679040, "flops_estimate": 59307458560}}, "quantization_analysis": {"quantization_method": "w8a8_int8", "weight_precision": "int8", "activation_precision": "int8", "quantization_operations": ["per_token_quant_int8", "int8_scaled_mm", "dequantization"], "memory_reduction": "4x (from fp16 to int8)", "compute_efficiency": "Higher throughput with specialized int8 kernels"}, "memory_analysis": {}, "performance_metrics": {"total_parameters": 2457104384, "total_flops_estimate": 76133335040, "total_layers": 1, "model_size_estimate_gb": 2.2883567810058594}, "operator_estimates": {}}