{"schemaVersion": 1, "deviceProperties": [{"id": 0, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 1, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 2, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 3, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 4, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 5, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 6, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}, {"id": 7, "name": "NVIDIA A100-SXM4-40GB", "totalGlobalMem": 42406903808, "computeMajor": 8, "computeMinor": 0, "maxThreadsPerBlock": 1024, "maxThreadsPerMultiprocessor": 2048, "regsPerBlock": 65536, "warpSize": 32, "sharedMemPerBlock": 49152, "numSms": 108, "regsPerMultiprocessor": 65536, "sharedMemPerBlockOptin": 166912, "sharedMemPerMultiprocessor": 167936}], "cupti_version": 26, "cuda_runtime_version": 12080, "with_flops": 1, "record_shapes": 1, "cuda_driver_version": 12090, "profile_memory": 1, "with_stack": 1, "trace_id": "19F72DAB4BEE4C67A4769FAD77ED71CC", "displayTimeUnit": "ms", "baseTimeNanoseconds": 1751410836000000000, "traceEvents": [{"ph": "X", "cat": "python_function", "name": "threading.py(973): _bootstrap", "pid": 1304242, "tid": 0, "ts": 6595619254774.209, "dur": 4084161.228, "args": {"Python parent id": null, "Python id": 1, "Ev Idx": 0}}, {"ph": "X", "cat": "python_function", "name": "threading.py(1016): _bootstrap_inner", "pid": 1304242, "tid": 0, "ts": 6595619254779.377, "dur": 4084156.06, "args": {"Python parent id": 1, "Python id": 2, "Ev Idx": 1}}, {"ph": "X", "cat": "python_function", "name": "threading.py(953): run", "pid": 1304242, "tid": 0, "ts": 6595619254785.429, "dur": 4084150.008, "args": {"Python parent id": 2, "Python id": 3, "Ev Idx": 2}}, {"ph": "X", "cat": "python_function", "name": "torch/_inductor/compile_worker/subproc_pool.py(195): _read_thread", "pid": 1304242, "tid": 0, "ts": 6595619254788.719, "dur": 4084146.718, "args": {"Python parent id": 3, "Python id": 4, "Ev Idx": 3}}, {"ph": "X", "cat": "python_function", "name": "torch/_inductor/compile_worker/subproc_pool.py(61): _recv_msg", "pid": 1304242, "tid": 0, "ts": 6595619254795.479, "dur": 4084139.958, "args": {"Python parent id": 4, "Python id": 5, "Ev Idx": 4}}, {"ph": "X", "cat": "python_function", "name": "<function _recv_msg at 0x7fa03741e4d0>", "pid": 1304242, "tid": 1304242, "ts": 6595619254841.223, "dur": 4084094.214, "args": {"Python parent id": null, "Python id": 6, "Ev Idx": 5}}, {"ph": "X", "cat": "python_function", "name": "sglang_operator_analysis.py(334): <module>", "pid": 1304242, "tid": 1304242, "ts": 6595619254851.692, "dur": 4084083.745, "args": {"Python parent id": 6, "Python id": 7, "Ev Idx": 6}}, {"ph": "X", "cat": "python_function", "name": "sglang_operator_analysis.py(219): main", "pid": 1304242, "tid": 1304242, "ts": 6595619254854.418, "dur": 4084081.019, "args": {"Python parent id": 7, "Python id": 8, "Ev Idx": 7}}, {"ph": "X", "cat": "python_function", "name": "<function main at 0x7fa1227db490>", "pid": 1304242, "tid": 1304242, "ts": 6595619254859.501, "dur": 4084075.936, "args": {"Python parent id": 8, "Python id": 9, "Ev Idx": 8}}, {"ph": "X", "cat": "python_function", "name": "torch/profiler/profiler.py(812): __enter__", "pid": 1304242, "tid": 1304242, "ts": 6595619254862.268, "dur": 292.662, "args": {"Python parent id": 9, "Python id": 10, "Ev Idx": 9}}, {"ph": "X", "cat": "python_function", "name": "torch/profiler/profiler.py(822): start", "pid": 1304242, "tid": 1304242, "ts": 6595619254866.821, "dur": 287.378, "args": {"Python parent id": 10, "Python id": 11, "Ev Idx": 10}}, {"ph": "X", "cat": "python_function", "name": "torch/profiler/profiler.py(878): _transit_action", "pid": 1304242, "tid": 1304242, "ts": 6595619254870.051, "dur": 282.611, "args": {"Python parent id": 11, "Python id": 12, "Ev Idx": 11}}, {"ph": "X", "cat": "python_function", "name": "torch/profiler/profiler.py(209): start_trace", "pid": 1304242, "tid": 1304242, "ts": 6595619254874.867, "dur": 274.881, "args": {"Python parent id": 12, "Python id": 13, "Ev Idx": 12}}, {"ph": "X", "cat": "python_function", "name": "torch/autograd/profiler.py(370): _start_trace", "pid": 1304242, "tid": 1304242, "ts": 6595619254876.516, "dur": 49.68, "args": {"Python parent id": 13, "Python id": 14, "Ev Idx": 13}}, {"ph": "X", "cat": "python_function", "name": "<built-in function perf_counter_ns>", "pid": 1304242, "tid": 1304242, "ts": 6595619254915.772, "dur": 1.114, "args": {"Python parent id": 14, "Python id": 15, "Ev Idx": 14}}, {"ph": "X", "cat": "python_function", "name": "torch/profiler/profiler.py(352): add_metadata_json", "pid": 1304242, "tid": 1304242, "ts": 6595619254935.23, "dur": 26.158, "args": {"Python parent id": 13, "Python id": 16, "Ev Idx": 15}}, {"ph": "X", "cat": "python_function", "name": "<built-in method _add_metadata_json of PyCapsule object at 0x7fa125b37210>", "pid": 1304242, "tid": 1304242, "ts": 6595619254954.461, "dur": 6.249, "args": {"Python parent id": 16, "Python id": 17, "Ev Idx": 16}}, {"ph": "X", "cat": "python_function", "name": "torch/profiler/profiler.py(352): add_metadata_json", "pid": 1304242, "tid": 1304242, "ts": 6595619254970.857, "dur": 4.092, "args": {"Python parent id": 13, "Python id": 18, "Ev Idx": 17}}, {"ph": "X", "cat": "python_function", "name": "<built-in method _add_metadata_json of PyCapsule object at 0x7fa125b37210>", "pid": 1304242, "tid": 1304242, "ts": 6595619254972.351, "dur": 2.37, "args": {"Python parent id": 18, "Python id": 19, "Ev Idx": 18}}, {"ph": "X", "cat": "python_function", "name": "torch/profiler/profiler.py(352): add_metadata_json", "pid": 1304242, "tid": 1304242, "ts": 6595619254976.751, "dur": 2.353, "args": {"Python parent id": 13, "Python id": 20, "Ev Idx": 19}}, {"ph": "X", "cat": "python_function", "name": "<built-in method _add_metadata_json of PyCapsule object at 0x7fa125b37210>", "pid": 1304242, "tid": 1304242, "ts": 6595619254977.526, "dur": 1.427, "args": {"Python parent id": 20, "Python id": 21, "Ev Idx": 20}}, {"ph": "X", "cat": "python_function", "name": "torch/profiler/profiler.py(352): add_metadata_json", "pid": 1304242, "tid": 1304242, "ts": 6595619254980.994, "dur": 2.318, "args": {"Python parent id": 13, "Python id": 22, "Ev Idx": 21}}, {"ph": "X", "cat": "python_function", "name": "<built-in method _add_metadata_json of PyCapsule object at 0x7fa125b37210>", "pid": 1304242, "tid": 1304242, "ts": 6595619254981.702, "dur": 1.461, "args": {"Python parent id": 22, "Python id": 23, "Ev Idx": 22}}, {"ph": "X", "cat": "python_function", "name": "<built-in method kineto_available of PyCapsule object at 0x7fa125b37270>", "pid": 1304242, "tid": 1304242, "ts": 6595619254989.716, "dur": 4.742, "args": {"Python parent id": 13, "Python id": 24, "Ev Idx": 23}}, {"ph": "X", "cat": "python_function", "name": "torch/profiler/profiler.py(367): _get_distributed_info", "pid": 1304242, "tid": 1304242, "ts": 6595619254997.901, "dur": 95.924, "args": {"Python parent id": 13, "Python id": 25, "Ev Idx": 24}}, {"ph": "X", "cat": "python_function", "name": "torch/distributed/__init__.py(14): is_available", "pid": 1304242, "tid": 1304242, "ts": 6595619255023.229, "dur": 13.128, "args": {"Python parent id": 25, "Python id": 26, "Ev Idx": 25}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595619255033.542, "dur": 2.346, "args": {"Python parent id": 26, "Python id": 27, "Ev Idx": 26}}, {"ph": "X", "cat": "python_function", "name": "torch/distributed/distributed_c10d.py(1274): is_initialized", "pid": 1304242, "tid": 1304242, "ts": 6595619255048.406, "dur": 44.623, "args": {"Python parent id": 25, "Python id": 28, "Ev Idx": 27}}, {"ph": "X", "cat": "python_function", "name": "torch/distributed/distributed_c10d.py(722): WORLD", "pid": 1304242, "tid": 1304242, "ts": 6595619255075.212, "dur": 17.218, "args": {"Python parent id": 28, "Python id": 29, "Ev Idx": 28}}, {"ph": "X", "cat": "python_function", "name": "torch/distributed/distributed_c10d.py(597): default_pg", "pid": 1304242, "tid": 1304242, "ts": 6595619255087.383, "dur": 4.663, "args": {"Python parent id": 29, "Python id": 30, "Ev Idx": 29}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595619255096.411, "dur": 1.194, "args": {"Python parent id": 13, "Python id": 31, "Ev Idx": 30}}, {"ph": "X", "cat": "python_function", "name": "<built-in function getattr>", "pid": 1304242, "tid": 1304242, "ts": 6595619255117.73, "dur": 0.842, "args": {"Python parent id": 13, "Python id": 32, "Ev Idx": 31}}, {"ph": "X", "cat": "python_function", "name": "<built-in method items of dict object at 0x7fa02f736d40>", "pid": 1304242, "tid": 1304242, "ts": 6595619255144.796, "dur": 1.1, "args": {"Python parent id": 13, "Python id": 33, "Ev Idx": 32}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595619255171.803, "dur": 0.734, "args": {"Python parent id": 9, "Python id": 34, "Ev Idx": 33}}, {"ph": "X", "cat": "python_function", "name": "<built-in function print>", "pid": 1304242, "tid": 1304242, "ts": 6595619255182.045, "dur": 59.129, "args": {"Python parent id": 9, "Python id": 35, "Ev Idx": 34}}, {"ph": "X", "cat": "python_function", "name": "operator_profiler.py(28): set_step", "pid": 1304242, "tid": 1304242, "ts": 6595619255250.833, "dur": 1.47, "args": {"Python parent id": 9, "Python id": 36, "Ev Idx": 35}}, {"ph": "X", "cat": "python_function", "name": "<built-in function time>", "pid": 1304242, "tid": 1304242, "ts": 6595619255256.982, "dur": 1.203, "args": {"Python parent id": 9, "Python id": 37, "Ev Idx": 36}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/entrypoints/engine.py(140): generate", "pid": 1304242, "tid": 1304242, "ts": 6595619255265.961, "dur": 3448657.347, "args": {"Python parent id": 9, "Python id": 38, "Ev Idx": 37}}, {"ph": "X", "cat": "python_function", "name": "<string>(2): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595619255293.614, "dur": 22.698, "args": {"Python parent id": 38, "Python id": 39, "Ev Idx": 38}}, {"ph": "X", "cat": "python_function", "name": "<built-in function get_event_loop>", "pid": 1304242, "tid": 1304242, "ts": 6595619255328.24, "dur": 563.828, "args": {"Python parent id": 38, "Python id": 40, "Ev Idx": 39}}, {"ph": "X", "cat": "python_function", "name": "asyncio/events.py(736): get_event_loop_policy", "pid": 1304242, "tid": 1304242, "ts": 6595619255340.726, "dur": 2.097, "args": {"Python parent id": 40, "Python id": 41, "Ev Idx": 40}}, {"ph": "X", "cat": "python_function", "name": "asyncio/events.py(645): get_event_loop", "pid": 1304242, "tid": 1304242, "ts": 6595619255349.135, "dur": 542.246, "args": {"Python parent id": 40, "Python id": 42, "Ev Idx": 41}}, {"ph": "X", "cat": "python_function", "name": "threading.py(1430): current_thread", "pid": 1304242, "tid": 1304242, "ts": 6595619255361.747, "dur": 6.722, "args": {"Python parent id": 42, "Python id": 43, "Ev Idx": 42}}, {"ph": "X", "cat": "python_function", "name": "<built-in function get_ident>", "pid": 1304242, "tid": 1304242, "ts": 6595619255365.348, "dur": 0.707, "args": {"Python parent id": 43, "Python id": 44, "Ev Idx": 43}}, {"ph": "X", "cat": "python_function", "name": "threading.py(1574): main_thread", "pid": 1304242, "tid": 1304242, "ts": 6595619255371.333, "dur": 0.593, "args": {"Python parent id": 42, "Python id": 45, "Ev Idx": 44}}, {"ph": "X", "cat": "python_function", "name": "asyncio/events.py(667): new_event_loop", "pid": 1304242, "tid": 1304242, "ts": 6595619255375.094, "dur": 499.076, "args": {"Python parent id": 42, "Python id": 46, "Ev Idx": 45}}, {"ph": "X", "cat": "python_function", "name": "uvloop/__init__.py(154): _loop_factory", "pid": 1304242, "tid": 1304242, "ts": 6595619255388.42, "dur": 485.224, "args": {"Python parent id": 46, "Python id": 47, "Ev Idx": 46}}, {"ph": "X", "cat": "python_function", "name": "uvloop/__init__.py(23): new_event_loop", "pid": 1304242, "tid": 1304242, "ts": 6595619255399.316, "dur": 473.907, "args": {"Python parent id": 47, "Python id": 48, "Ev Idx": 47}}, {"ph": "X", "cat": "python_function", "name": "weakref.py(105): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595619255487.723, "dur": 31.76, "args": {"Python parent id": 48, "Python id": 49, "Ev Idx": 48}}, {"ph": "X", "cat": "python_function", "name": "weakref.py(290): update", "pid": 1304242, "tid": 1304242, "ts": 6595619255504.044, "dur": 13.71, "args": {"Python parent id": 49, "Python id": 50, "Ev Idx": 49}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595619255507.22, "dur": 1.152, "args": {"Python parent id": 50, "Python id": 51, "Ev Idx": 50}}, {"ph": "X", "cat": "python_function", "name": "<built-in method items of dict object at 0x7fa02f736d40>", "pid": 1304242, "tid": 1304242, "ts": 6595619255513.867, "dur": 0.433, "args": {"Python parent id": 50, "Python id": 52, "Ev Idx": 51}}, {"ph": "X", "cat": "python_function", "name": "<built-in method items of dict object at 0x7fa02f736d40>", "pid": 1304242, "tid": 1304242, "ts": 6595619255516.9, "dur": 0.132, "args": {"Python parent id": 50, "Python id": 53, "Ev Idx": 52}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(37): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595619255773.339, "dur": 6.816, "args": {"Python parent id": 48, "Python id": 54, "Ev Idx": 53}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(821): get", "pid": 1304242, "tid": 1304242, "ts": 6595619255803.224, "dur": 50.577, "args": {"Python parent id": 48, "Python id": 55, "Ev Idx": 54}}, {"ph": "X", "cat": "python_function", "name": "os.py(675): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595619255816.83, "dur": 32.695, "args": {"Python parent id": 55, "Python id": 56, "Ev Idx": 55}}, {"ph": "X", "cat": "python_function", "name": "os.py(755): encode", "pid": 1304242, "tid": 1304242, "ts": 6595619255820.092, "dur": 22.534, "args": {"Python parent id": 56, "Python id": 57, "Ev Idx": 56}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619255830.351, "dur": 0.495, "args": {"Python parent id": 57, "Python id": 58, "Ev Idx": 57}}, {"ph": "X", "cat": "python_function", "name": "<built-in method encode of str object at 0x7fa126973a50>", "pid": 1304242, "tid": 1304242, "ts": 6595619255840.007, "dur": 2.117, "args": {"Python parent id": 57, "Python id": 59, "Ev Idx": 58}}, {"ph": "X", "cat": "python_function", "name": "asyncio/events.py(661): set_event_loop", "pid": 1304242, "tid": 1304242, "ts": 6595619255880.844, "dur": 8.046, "args": {"Python parent id": 42, "Python id": 60, "Ev Idx": 59}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619255886.845, "dur": 0.472, "args": {"Python parent id": 60, "Python id": 61, "Ev Idx": 60}}, {"ph": "X", "cat": "python_function", "name": "asyncio/base_futures.py(14): isfuture", "pid": 1304242, "tid": 1304242, "ts": 6595619255930.737, "dur": 11.492, "args": {"Python parent id": 38, "Python id": 62, "Ev Idx": 61}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595619255936.553, "dur": 5.096, "args": {"Python parent id": 62, "Python id": 63, "Ev Idx": 62}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(610): ensure_future", "pid": 1304242, "tid": 1304242, "ts": 6595619255956.991, "dur": 165.271, "args": {"Python parent id": 38, "Python id": 64, "Ev Idx": 63}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(618): _ensure_future", "pid": 1304242, "tid": 1304242, "ts": 6595619255959.116, "dur": 162.267, "args": {"Python parent id": 64, "Python id": 65, "Ev Idx": 64}}, {"ph": "X", "cat": "python_function", "name": "asyncio/base_futures.py(14): isfuture", "pid": 1304242, "tid": 1304242, "ts": 6595619255971.322, "dur": 3.033, "args": {"Python parent id": 65, "Python id": 66, "Ev Idx": 65}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595619255972.45, "dur": 1.759, "args": {"Python parent id": 66, "Python id": 67, "Ev Idx": 66}}, {"ph": "X", "cat": "python_function", "name": "asyncio/coroutines.py(177): iscoroutine", "pid": 1304242, "tid": 1304242, "ts": 6595619255986.486, "dur": 65.621, "args": {"Python parent id": 65, "Python id": 68, "Ev Idx": 67}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619255995.653, "dur": 45.966, "args": {"Python parent id": 68, "Python id": 69, "Ev Idx": 68}}, {"ph": "X", "cat": "python_function", "name": "abc.py(117): __instancecheck__", "pid": 1304242, "tid": 1304242, "ts": 6595619256000.728, "dur": 40.079, "args": {"Python parent id": 69, "Python id": 70, "Ev Idx": 69}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _abc_instancecheck>", "pid": 1304242, "tid": 1304242, "ts": 6595619256009.357, "dur": 31.265, "args": {"Python parent id": 70, "Python id": 71, "Ev Idx": 70}}, {"ph": "X", "cat": "python_function", "name": "abc.py(121): __subclasscheck__", "pid": 1304242, "tid": 1304242, "ts": 6595619256015.996, "dur": 24.072, "args": {"Python parent id": 71, "Python id": 72, "Ev Idx": 71}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _abc_subclasscheck>", "pid": 1304242, "tid": 1304242, "ts": 6595619256018.931, "dur": 20.907, "args": {"Python parent id": 72, "Python id": 73, "Ev Idx": 72}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(156): __subclasshook__", "pid": 1304242, "tid": 1304242, "ts": 6595619256022.604, "dur": 15.469, "args": {"Python parent id": 73, "Python id": 74, "Ev Idx": 73}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(78): _check_methods", "pid": 1304242, "tid": 1304242, "ts": 6595619256025.816, "dur": 11.47, "args": {"Python parent id": 74, "Python id": 75, "Ev Idx": 74}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595619256043.373, "dur": 0.664, "args": {"Python parent id": 68, "Python id": 76, "Ev Idx": 75}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add of set object at 0x7fa126a5dd20>", "pid": 1304242, "tid": 1304242, "ts": 6595619256050.828, "dur": 0.624, "args": {"Python parent id": 68, "Python id": 77, "Ev Idx": 76}}, {"ph": "X", "cat": "python_function", "name": "asyncio/coroutines.py(177): iscoroutine", "pid": 1304242, "tid": 1304242, "ts": 6595619256071.112, "dur": 1.63, "args": {"Python parent id": 65, "Python id": 78, "Ev Idx": 77}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(86): add", "pid": 1304242, "tid": 1304242, "ts": 6595619256108.076, "dur": 10.953, "args": {"Python parent id": 65, "Python id": 79, "Ev Idx": 78}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add of set object at 0x7fa126a5dd20>", "pid": 1304242, "tid": 1304242, "ts": 6595619256118.553, "dur": 0.284, "args": {"Python parent id": 79, "Python id": 80, "Ev Idx": 79}}, {"ph": "X", "cat": "python_function", "name": "threading.py(1574): main_thread", "pid": 1304242, "tid": 1304242, "ts": 6595619256180.638, "dur": 0.844, "args": {"Python parent id": 38, "Python id": 81, "Ev Idx": 80}}, {"ph": "X", "cat": "python_function", "name": "threading.py(1145): ident", "pid": 1304242, "tid": 1304242, "ts": 6595619256185.958, "dur": 2.589, "args": {"Python parent id": 38, "Python id": 82, "Ev Idx": 81}}, {"ph": "X", "cat": "python_function", "name": "socket.py(594): socketpair", "pid": 1304242, "tid": 1304242, "ts": 6595619256193.378, "dur": 97.872, "args": {"Python parent id": 38, "Python id": 83, "Ev Idx": 82}}, {"ph": "X", "cat": "python_function", "name": "<built-in function socketpair>", "pid": 1304242, "tid": 1304242, "ts": 6595619256202.996, "dur": 34.501, "args": {"Python parent id": 83, "Python id": 84, "Ev Idx": 83}}, {"ph": "X", "cat": "python_function", "name": "<built-in method detach of _socket.socket object at 0x7fa02f700930>", "pid": 1304242, "tid": 1304242, "ts": 6595619256249.458, "dur": 0.709, "args": {"Python parent id": 83, "Python id": 85, "Ev Idx": 84}}, {"ph": "X", "cat": "python_function", "name": "socket.py(220): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595619256256.437, "dur": 22.249, "args": {"Python parent id": 83, "Python id": 86, "Ev Idx": 85}}, {"ph": "X", "cat": "python_function", "name": "<built-in method detach of _socket.socket object at 0x7fa02f700930>", "pid": 1304242, "tid": 1304242, "ts": 6595619256282.833, "dur": 0.258, "args": {"Python parent id": 83, "Python id": 87, "Ev Idx": 86}}, {"ph": "X", "cat": "python_function", "name": "socket.py(220): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595619256284.94, "dur": 5.233, "args": {"Python parent id": 83, "Python id": 88, "Ev Idx": 87}}, {"ph": "X", "cat": "python_function", "name": "weakref.py(134): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595619256340.175, "dur": 3.116, "args": {"Python parent id": 38, "Python id": 89, "Ev Idx": 88}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(482): generate_request", "pid": 1304242, "tid": 1304242, "ts": 6595619256421.167, "dur": 10482.557, "args": {"Python parent id": 38, "Python id": 90, "Ev Idx": 89}}, {"ph": "X", "cat": "python_function", "name": "<built-in function time>", "pid": 1304242, "tid": 1304242, "ts": 6595619256424.981, "dur": 1.146, "args": {"Python parent id": 90, "Python id": 91, "Ev Idx": 90}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(1426): auto_create_handle_loop", "pid": 1304242, "tid": 1304242, "ts": 6595619256428.73, "dur": 367.207, "args": {"Python parent id": 90, "Python id": 92, "Ev Idx": 91}}, {"ph": "X", "cat": "python_function", "name": "<built-in function get_event_loop>", "pid": 1304242, "tid": 1304242, "ts": 6595619256432.835, "dur": 1.76, "args": {"Python parent id": 92, "Python id": 93, "Ev Idx": 92}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(86): add", "pid": 1304242, "tid": 1304242, "ts": 6595619256451.459, "dur": 3.782, "args": {"Python parent id": 92, "Python id": 94, "Ev Idx": 93}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add of set object at 0x7fa126a5dd20>", "pid": 1304242, "tid": 1304242, "ts": 6595619256454.422, "dur": 0.467, "args": {"Python parent id": 94, "Python id": 95, "Ev Idx": 94}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add of set object at 0x7fa126a5dd20>", "pid": 1304242, "tid": 1304242, "ts": 6595619256457.22, "dur": 0.235, "args": {"Python parent id": 92, "Python id": 96, "Ev Idx": 95}}, {"ph": "X", "cat": "python_function", "name": "threading.py(1430): current_thread", "pid": 1304242, "tid": 1304242, "ts": 6595619256461.199, "dur": 3.556, "args": {"Python parent id": 92, "Python id": 97, "Ev Idx": 96}}, {"ph": "X", "cat": "python_function", "name": "<built-in function get_ident>", "pid": 1304242, "tid": 1304242, "ts": 6595619256462.475, "dur": 0.608, "args": {"Python parent id": 97, "Python id": 98, "Ev Idx": 97}}, {"ph": "X", "cat": "python_function", "name": "threading.py(1574): main_thread", "pid": 1304242, "tid": 1304242, "ts": 6595619256466.344, "dur": 0.393, "args": {"Python parent id": 92, "Python id": 99, "Ev Idx": 98}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(2082): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595619256471.073, "dur": 1.874, "args": {"Python parent id": 92, "Python id": 100, "Ev Idx": 99}}, {"ph": "X", "cat": "python_function", "name": "asyncio/coroutines.py(177): iscoroutine", "pid": 1304242, "tid": 1304242, "ts": 6595619256485.335, "dur": 51.19, "args": {"Python parent id": 92, "Python id": 101, "Ev Idx": 100}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619256487.159, "dur": 48.885, "args": {"Python parent id": 101, "Python id": 102, "Ev Idx": 101}}, {"ph": "X", "cat": "python_function", "name": "abc.py(117): __instancecheck__", "pid": 1304242, "tid": 1304242, "ts": 6595619256491.413, "dur": 43.654, "args": {"Python parent id": 102, "Python id": 103, "Ev Idx": 102}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _abc_instancecheck>", "pid": 1304242, "tid": 1304242, "ts": 6595619256492.341, "dur": 42.583, "args": {"Python parent id": 103, "Python id": 104, "Ev Idx": 103}}, {"ph": "X", "cat": "python_function", "name": "abc.py(121): __subclasscheck__", "pid": 1304242, "tid": 1304242, "ts": 6595619256495.047, "dur": 39.193, "args": {"Python parent id": 104, "Python id": 105, "Ev Idx": 104}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _abc_subclasscheck>", "pid": 1304242, "tid": 1304242, "ts": 6595619256495.763, "dur": 38.319, "args": {"Python parent id": 105, "Python id": 106, "Ev Idx": 105}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(156): __subclasshook__", "pid": 1304242, "tid": 1304242, "ts": 6595619256497.516, "dur": 7.088, "args": {"Python parent id": 106, "Python id": 107, "Ev Idx": 106}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(78): _check_methods", "pid": 1304242, "tid": 1304242, "ts": 6595619256499.466, "dur": 4.477, "args": {"Python parent id": 107, "Python id": 108, "Ev Idx": 107}}, {"ph": "X", "cat": "python_function", "name": "abc.py(121): __subclasscheck__", "pid": 1304242, "tid": 1304242, "ts": 6595619256518.958, "dur": 13.455, "args": {"Python parent id": 106, "Python id": 109, "Ev Idx": 108}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _abc_subclasscheck>", "pid": 1304242, "tid": 1304242, "ts": 6595619256519.497, "dur": 12.663, "args": {"Python parent id": 109, "Python id": 110, "Ev Idx": 109}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(156): __subclasshook__", "pid": 1304242, "tid": 1304242, "ts": 6595619256527.799, "dur": 0.679, "args": {"Python parent id": 110, "Python id": 111, "Ev Idx": 110}}, {"ph": "X", "cat": "python_function", "name": "asyncio/coroutines.py(164): iscoroutinefunction", "pid": 1304242, "tid": 1304242, "ts": 6595619256538.886, "dur": 51.163, "args": {"Python parent id": 92, "Python id": 112, "Ev Idx": 111}}, {"ph": "X", "cat": "python_function", "name": "inspect.py(308): iscoroutinefunction", "pid": 1304242, "tid": 1304242, "ts": 6595619256545.341, "dur": 34.258, "args": {"Python parent id": 112, "Python id": 113, "Ev Idx": 112}}, {"ph": "X", "cat": "python_function", "name": "inspect.py(290): _has_code_flag", "pid": 1304242, "tid": 1304242, "ts": 6595619256548.165, "dur": 30.978, "args": {"Python parent id": 113, "Python id": 114, "Ev Idx": 113}}, {"ph": "X", "cat": "python_function", "name": "inspect.py(199): ismethod", "pid": 1304242, "tid": 1304242, "ts": 6595619256550.143, "dur": 9.136, "args": {"Python parent id": 114, "Python id": 115, "Ev Idx": 114}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619256558.846, "dur": 0.29, "args": {"Python parent id": 115, "Python id": 116, "Ev Idx": 115}}, {"ph": "X", "cat": "python_function", "name": "inspect.py(199): ismethod", "pid": 1304242, "tid": 1304242, "ts": 6595619256561.527, "dur": 2.383, "args": {"Python parent id": 114, "Python id": 117, "Ev Idx": 116}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619256562.257, "dur": 1.507, "args": {"Python parent id": 117, "Python id": 118, "Ev Idx": 117}}, {"ph": "X", "cat": "python_function", "name": "functools.py(421): _unwrap_partial", "pid": 1304242, "tid": 1304242, "ts": 6595619256567.215, "dur": 2.099, "args": {"Python parent id": 114, "Python id": 119, "Ev Idx": 118}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619256568.578, "dur": 0.304, "args": {"Python parent id": 119, "Python id": 120, "Ev Idx": 119}}, {"ph": "X", "cat": "python_function", "name": "inspect.py(277): isfunction", "pid": 1304242, "tid": 1304242, "ts": 6595619256571.213, "dur": 2.666, "args": {"Python parent id": 114, "Python id": 121, "Ev Idx": 120}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619256573.616, "dur": 0.16, "args": {"Python parent id": 121, "Python id": 122, "Ev Idx": 121}}, {"ph": "X", "cat": "python_function", "name": "<built-in function getattr>", "pid": 1304242, "tid": 1304242, "ts": 6595619256581.484, "dur": 7.538, "args": {"Python parent id": 112, "Python id": 123, "Ev Idx": 122}}, {"ph": "X", "cat": "python_function", "name": "signal.py(54): signal", "pid": 1304242, "tid": 1304242, "ts": 6595619256608.51, "dur": 80.642, "args": {"Python parent id": 92, "Python id": 124, "Ev Idx": 123}}, {"ph": "X", "cat": "python_function", "name": "signal.py(34): _enum_to_int", "pid": 1304242, "tid": 1304242, "ts": 6595619256636.066, "dur": 3.07, "args": {"Python parent id": 124, "Python id": 125, "Ev Idx": 124}}, {"ph": "X", "cat": "python_function", "name": "signal.py(34): _enum_to_int", "pid": 1304242, "tid": 1304242, "ts": 6595619256640.918, "dur": 12.584, "args": {"Python parent id": 124, "Python id": 126, "Ev Idx": 125}}, {"ph": "X", "cat": "python_function", "name": "<built-in function signal>", "pid": 1304242, "tid": 1304242, "ts": 6595619256659.619, "dur": 5.485, "args": {"Python parent id": 124, "Python id": 127, "Ev Idx": 126}}, {"ph": "X", "cat": "python_function", "name": "signal.py(24): _int_to_enum", "pid": 1304242, "tid": 1304242, "ts": 6595619256667.744, "dur": 21.01, "args": {"Python parent id": 124, "Python id": 128, "Ev Idx": 127}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595619256673.094, "dur": 14.379, "args": {"Python parent id": 128, "Python id": 129, "Ev Idx": 128}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595619256681.732, "dur": 4.966, "args": {"Python parent id": 129, "Python id": 130, "Ev Idx": 129}}, {"ph": "X", "cat": "python_function", "name": "asyncio/coroutines.py(177): iscoroutine", "pid": 1304242, "tid": 1304242, "ts": 6595619256699.385, "dur": 7.355, "args": {"Python parent id": 92, "Python id": 131, "Ev Idx": 130}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619256701.506, "dur": 4.847, "args": {"Python parent id": 131, "Python id": 132, "Ev Idx": 131}}, {"ph": "X", "cat": "python_function", "name": "abc.py(117): __instancecheck__", "pid": 1304242, "tid": 1304242, "ts": 6595619256703.235, "dur": 2.539, "args": {"Python parent id": 132, "Python id": 133, "Ev Idx": 132}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _abc_instancecheck>", "pid": 1304242, "tid": 1304242, "ts": 6595619256703.618, "dur": 1.996, "args": {"Python parent id": 133, "Python id": 134, "Ev Idx": 133}}, {"ph": "X", "cat": "python_function", "name": "asyncio/coroutines.py(164): iscoroutinefunction", "pid": 1304242, "tid": 1304242, "ts": 6595619256707.823, "dur": 18.276, "args": {"Python parent id": 92, "Python id": 135, "Ev Idx": 134}}, {"ph": "X", "cat": "python_function", "name": "inspect.py(308): iscoroutinefunction", "pid": 1304242, "tid": 1304242, "ts": 6595619256709.214, "dur": 12.355, "args": {"Python parent id": 135, "Python id": 136, "Ev Idx": 135}}, {"ph": "X", "cat": "python_function", "name": "inspect.py(290): _has_code_flag", "pid": 1304242, "tid": 1304242, "ts": 6595619256710.309, "dur": 11.016, "args": {"Python parent id": 136, "Python id": 137, "Ev Idx": 136}}, {"ph": "X", "cat": "python_function", "name": "inspect.py(199): ismethod", "pid": 1304242, "tid": 1304242, "ts": 6595619256711.089, "dur": 1.341, "args": {"Python parent id": 137, "Python id": 138, "Ev Idx": 137}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619256712.055, "dur": 0.246, "args": {"Python parent id": 138, "Python id": 139, "Ev Idx": 138}}, {"ph": "X", "cat": "python_function", "name": "inspect.py(199): ismethod", "pid": 1304242, "tid": 1304242, "ts": 6595619256713.877, "dur": 1.131, "args": {"Python parent id": 137, "Python id": 140, "Ev Idx": 139}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619256714.438, "dur": 0.467, "args": {"Python parent id": 140, "Python id": 141, "Ev Idx": 140}}, {"ph": "X", "cat": "python_function", "name": "functools.py(421): _unwrap_partial", "pid": 1304242, "tid": 1304242, "ts": 6595619256716.216, "dur": 1.252, "args": {"Python parent id": 137, "Python id": 142, "Ev Idx": 141}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619256716.895, "dur": 0.186, "args": {"Python parent id": 142, "Python id": 143, "Ev Idx": 142}}, {"ph": "X", "cat": "python_function", "name": "inspect.py(277): isfunction", "pid": 1304242, "tid": 1304242, "ts": 6595619256718.218, "dur": 0.777, "args": {"Python parent id": 137, "Python id": 144, "Ev Idx": 143}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619256718.734, "dur": 0.158, "args": {"Python parent id": 144, "Python id": 145, "Ev Idx": 144}}, {"ph": "X", "cat": "python_function", "name": "<built-in function getattr>", "pid": 1304242, "tid": 1304242, "ts": 6595619256722.385, "dur": 3.345, "args": {"Python parent id": 135, "Python id": 146, "Ev Idx": 145}}, {"ph": "X", "cat": "python_function", "name": "signal.py(54): signal", "pid": 1304242, "tid": 1304242, "ts": 6595619256730.281, "dur": 44.743, "args": {"Python parent id": 92, "Python id": 147, "Ev Idx": 146}}, {"ph": "X", "cat": "python_function", "name": "signal.py(34): _enum_to_int", "pid": 1304242, "tid": 1304242, "ts": 6595619256731.747, "dur": 0.945, "args": {"Python parent id": 147, "Python id": 148, "Ev Idx": 147}}, {"ph": "X", "cat": "python_function", "name": "signal.py(34): _enum_to_int", "pid": 1304242, "tid": 1304242, "ts": 6595619256733.371, "dur": 5.16, "args": {"Python parent id": 147, "Python id": 149, "Ev Idx": 148}}, {"ph": "X", "cat": "python_function", "name": "<built-in function signal>", "pid": 1304242, "tid": 1304242, "ts": 6595619256739.13, "dur": 1.894, "args": {"Python parent id": 147, "Python id": 150, "Ev Idx": 149}}, {"ph": "X", "cat": "python_function", "name": "signal.py(24): _int_to_enum", "pid": 1304242, "tid": 1304242, "ts": 6595619256742.338, "dur": 32.138, "args": {"Python parent id": 147, "Python id": 151, "Ev Idx": 150}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595619256744.194, "dur": 27.656, "args": {"Python parent id": 151, "Python id": 152, "Ev Idx": 151}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595619256745.72, "dur": 25.268, "args": {"Python parent id": 152, "Python id": 153, "Ev Idx": 152}}, {"ph": "X", "cat": "python_function", "name": "enum.py(741): _missing_", "pid": 1304242, "tid": 1304242, "ts": 6595619256754.135, "dur": 0.414, "args": {"Python parent id": 153, "Python id": 154, "Ev Idx": 153}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619256756.612, "dur": 2.05, "args": {"Python parent id": 153, "Python id": 155, "Ev Idx": 154}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(86): add", "pid": 1304242, "tid": 1304242, "ts": 6595619256790.414, "dur": 3.335, "args": {"Python parent id": 92, "Python id": 156, "Ev Idx": 155}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add of set object at 0x7fa126a5dd20>", "pid": 1304242, "tid": 1304242, "ts": 6595619256793.161, "dur": 0.383, "args": {"Python parent id": 156, "Python id": 157, "Ev Idx": 156}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add of set object at 0x7fa126a5dd20>", "pid": 1304242, "tid": 1304242, "ts": 6595619256795.549, "dur": 0.164, "args": {"Python parent id": 92, "Python id": 158, "Ev Idx": 157}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/io_struct.py(138): normalize_batch_and_arguments", "pid": 1304242, "tid": 1304242, "ts": 6595619256800.204, "dur": 136.714, "args": {"Python parent id": 90, "Python id": 159, "Ev Idx": 158}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/io_struct.py(160): _validate_inputs", "pid": 1304242, "tid": 1304242, "ts": 6595619256808.171, "dur": 8.658, "args": {"Python parent id": 159, "Python id": 160, "Ev Idx": 159}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/io_struct.py(173): _determine_batch_size", "pid": 1304242, "tid": 1304242, "ts": 6595619256818.467, "dur": 11.908, "args": {"Python parent id": 159, "Python id": 161, "Ev Idx": 160}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619256827.064, "dur": 0.242, "args": {"Python parent id": 161, "Python id": 162, "Ev Idx": 161}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/io_struct.py(201): _handle_parallel_sampling", "pid": 1304242, "tid": 1304242, "ts": 6595619256832.992, "dur": 12.031, "args": {"Python parent id": 159, "Python id": 163, "Ev Idx": 162}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619256835.441, "dur": 0.167, "args": {"Python parent id": 163, "Python id": 164, "Ev Idx": 163}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595619256841.482, "dur": 0.842, "args": {"Python parent id": 163, "Python id": 165, "Ev Idx": 164}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/io_struct.py(227): _normalize_single_inputs", "pid": 1304242, "tid": 1304242, "ts": 6595619256847.714, "dur": 88.422, "args": {"Python parent id": 159, "Python id": 166, "Ev Idx": 165}}, {"ph": "X", "cat": "python_function", "name": "uuid.py(718): uuid4", "pid": 1304242, "tid": 1304242, "ts": 6595619256852.02, "dur": 58.964, "args": {"Python parent id": 166, "Python id": 167, "Ev Idx": 166}}, {"ph": "X", "cat": "python_function", "name": "<built-in function urandom>", "pid": 1304242, "tid": 1304242, "ts": 6595619256856.393, "dur": 6.642, "args": {"Python parent id": 167, "Python id": 168, "Ev Idx": 167}}, {"ph": "X", "cat": "python_function", "name": "uuid.py(138): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595619256868.967, "dur": 40.584, "args": {"Python parent id": 167, "Python id": 169, "Ev Idx": 168}}, {"ph": "X", "cat": "python_function", "name": "<built-in method count of list object at 0x7fa02f735100>", "pid": 1304242, "tid": 1304242, "ts": 6595619256879.889, "dur": 1.678, "args": {"Python parent id": 169, "Python id": 170, "Ev Idx": 169}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595619256884.334, "dur": 0.498, "args": {"Python parent id": 169, "Python id": 171, "Ev Idx": 170}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619256886.662, "dur": 0.203, "args": {"Python parent id": 169, "Python id": 172, "Ev Idx": 171}}, {"ph": "X", "cat": "python_function", "name": "<built-in method from_bytes of type object at 0x57ea91926320>", "pid": 1304242, "tid": 1304242, "ts": 6595619256892.777, "dur": 2.56, "args": {"Python parent id": 169, "Python id": 173, "Ev Idx": 172}}, {"ph": "X", "cat": "python_function", "name": "uuid.py(333): hex", "pid": 1304242, "tid": 1304242, "ts": 6595619256922.94, "dur": 8.004, "args": {"Python parent id": 166, "Python id": 174, "Ev Idx": 173}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(13): __aenter__", "pid": 1304242, "tid": 1304242, "ts": 6595619256948.936, "dur": 8.821, "args": {"Python parent id": 90, "Python id": 175, "Ev Idx": 174}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(93): acquire", "pid": 1304242, "tid": 1304242, "ts": 6595619256953.083, "dur": 3.494, "args": {"Python parent id": 175, "Python id": 176, "Ev Idx": 175}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(287): wait_for", "pid": 1304242, "tid": 1304242, "ts": 6595619256963.428, "dur": 4.028, "args": {"Python parent id": 90, "Python id": 177, "Ev Idx": 176}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(498): <lambda>", "pid": 1304242, "tid": 1304242, "ts": 6595619256965.28, "dur": 1.329, "args": {"Python parent id": 177, "Python id": 178, "Ev Idx": 177}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(19): __aexit__", "pid": 1304242, "tid": 1304242, "ts": 6595619256970.908, "dur": 7.578, "args": {"Python parent id": 90, "Python id": 179, "Ev Idx": 178}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(125): release", "pid": 1304242, "tid": 1304242, "ts": 6595619256973.88, "dur": 4.153, "args": {"Python parent id": 179, "Python id": 180, "Ev Idx": 179}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(142): _wake_up_first", "pid": 1304242, "tid": 1304242, "ts": 6595619256976.698, "dur": 0.866, "args": {"Python parent id": 180, "Python id": 181, "Ev Idx": 180}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/aio_rwlock.py(21): reader_lock", "pid": 1304242, "tid": 1304242, "ts": 6595619256993.189, "dur": 5.47, "args": {"Python parent id": 90, "Python id": 182, "Ev Idx": 181}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/aio_rwlock.py(80): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595619256996.657, "dur": 1.331, "args": {"Python parent id": 182, "Python id": 183, "Ev Idx": 182}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/aio_rwlock.py(83): __aenter__", "pid": 1304242, "tid": 1304242, "ts": 6595619257002.102, "dur": 19.078, "args": {"Python parent id": 90, "Python id": 184, "Ev Idx": 183}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/aio_rwlock.py(43): acquire_reader", "pid": 1304242, "tid": 1304242, "ts": 6595619257004.759, "dur": 15.907, "args": {"Python parent id": 184, "Python id": 185, "Ev Idx": 184}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(13): __aenter__", "pid": 1304242, "tid": 1304242, "ts": 6595619257008.417, "dur": 3.161, "args": {"Python parent id": 185, "Python id": 186, "Ev Idx": 185}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(93): acquire", "pid": 1304242, "tid": 1304242, "ts": 6595619257009.816, "dur": 1.358, "args": {"Python parent id": 186, "Python id": 187, "Ev Idx": 186}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(19): __aexit__", "pid": 1304242, "tid": 1304242, "ts": 6595619257016.275, "dur": 3.887, "args": {"Python parent id": 185, "Python id": 188, "Ev Idx": 187}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(125): release", "pid": 1304242, "tid": 1304242, "ts": 6595619257017.596, "dur": 2.256, "args": {"Python parent id": 188, "Python id": 189, "Ev Idx": 188}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(142): _wake_up_first", "pid": 1304242, "tid": 1304242, "ts": 6595619257019.09, "dur": 0.511, "args": {"Python parent id": 189, "Python id": 190, "Ev Idx": 189}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(516): _tokenize_one_request", "pid": 1304242, "tid": 1304242, "ts": 6595619257028.124, "dur": 9149.269, "args": {"Python parent id": 90, "Python id": 191, "Ev Idx": 190}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619257031.417, "dur": 0.97, "args": {"Python parent id": 191, "Python id": 192, "Ev Idx": 191}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(2827): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595619257041.533, "dur": 8966.205, "args": {"Python parent id": 191, "Python id": 193, "Ev Idx": 192}}, {"ph": "X", "cat": "python_function", "name": "<built-in method pop of dict object at 0x7fa02f6ad340>", "pid": 1304242, "tid": 1304242, "ts": 6595619257055.709, "dur": 0.738, "args": {"Python parent id": 193, "Python id": 194, "Ev Idx": 193}}, {"ph": "X", "cat": "python_function", "name": "<built-in method update of dict object at 0x7fa02f737540>", "pid": 1304242, "tid": 1304242, "ts": 6595619257063.082, "dur": 1.35, "args": {"Python parent id": 193, "Python id": 195, "Ev Idx": 194}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(3988): _switch_to_input_mode", "pid": 1304242, "tid": 1304242, "ts": 6595619257069.326, "dur": 0.627, "args": {"Python parent id": 193, "Python id": 196, "Ev Idx": 195}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(2925): _call_one", "pid": 1304242, "tid": 1304242, "ts": 6595619257081.501, "dur": 8881.471, "args": {"Python parent id": 193, "Python id": 197, "Ev Idx": 196}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(2949): _is_valid_text_input", "pid": 1304242, "tid": 1304242, "ts": 6595619257083.966, "dur": 2.897, "args": {"Python parent id": 197, "Python id": 198, "Ev Idx": 197}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619257086.281, "dur": 0.174, "args": {"Python parent id": 198, "Python id": 199, "Ev Idx": 198}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619257089.113, "dur": 1.04, "args": {"Python parent id": 197, "Python id": 200, "Ev Idx": 199}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(3043): encode_plus", "pid": 1304242, "tid": 1304242, "ts": 6595619257102.212, "dur": 8857.285, "args": {"Python parent id": 197, "Python id": 201, "Ev Idx": 200}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(2722): _get_padding_truncation_strategies", "pid": 1304242, "tid": 1304242, "ts": 6595619257108.823, "dur": 11.301, "args": {"Python parent id": 201, "Python id": 202, "Ev Idx": 201}}, {"ph": "X", "cat": "python_function", "name": "<built-in method pop of dict object at 0x7fa02f6ad340>", "pid": 1304242, "tid": 1304242, "ts": 6595619257129.667, "dur": 1.095, "args": {"Python parent id": 201, "Python id": 203, "Ev Idx": 202}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(603): _encode_plus", "pid": 1304242, "tid": 1304242, "ts": 6595619257149.949, "dur": 8806.796, "args": {"Python parent id": 201, "Python id": 204, "Ev Idx": 203}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(512): _batch_encode_plus", "pid": 1304242, "tid": 1304242, "ts": 6595619257161.186, "dur": 8664.506, "args": {"Python parent id": 204, "Python id": 205, "Ev Idx": 204}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619257163.507, "dur": 0.621, "args": {"Python parent id": 205, "Python id": 206, "Ev Idx": 205}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(437): set_truncation_and_padding", "pid": 1304242, "tid": 1304242, "ts": 6595619257168.295, "dur": 10.215, "args": {"Python parent id": 205, "Python id": 207, "Ev Idx": 206}}, {"ph": "X", "cat": "python_function", "name": "<built-in method encode_batch of tokenizers.Tokenizer object at 0x57eac2f350b0>", "pid": 1304242, "tid": 1304242, "ts": 6595619257188.623, "dur": 8441.278, "args": {"Python parent id": 205, "Python id": 208, "Ev Idx": 207}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(565): <listcomp>", "pid": 1304242, "tid": 1304242, "ts": 6595619265645.503, "dur": 73.216, "args": {"Python parent id": 205, "Python id": 209, "Ev Idx": 208}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(307): _convert_encoding", "pid": 1304242, "tid": 1304242, "ts": 6595619265657.569, "dur": 58.93, "args": {"Python parent id": 209, "Python id": 210, "Ev Idx": 209}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of list object at 0x7fa02f714280>", "pid": 1304242, "tid": 1304242, "ts": 6595619265706.874, "dur": 1.074, "args": {"Python parent id": 210, "Python id": 211, "Ev Idx": 210}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of list object at 0x7fa02f714280>", "pid": 1304242, "tid": 1304242, "ts": 6595619265714.446, "dur": 0.135, "args": {"Python parent id": 210, "Python id": 212, "Ev Idx": 211}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(587): <listcomp>", "pid": 1304242, "tid": 1304242, "ts": 6595619265726.646, "dur": 1.951, "args": {"Python parent id": 205, "Python id": 213, "Ev Idx": 212}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(587): <listcomp>", "pid": 1304242, "tid": 1304242, "ts": 6595619265732.048, "dur": 1.022, "args": {"Python parent id": 205, "Python id": 214, "Ev Idx": 213}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(589): <listcomp>", "pid": 1304242, "tid": 1304242, "ts": 6595619265737.093, "dur": 0.963, "args": {"Python parent id": 205, "Python id": 215, "Ev Idx": 214}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(3968): _eventual_warn_about_too_long_sequence", "pid": 1304242, "tid": 1304242, "ts": 6595619265743.586, "dur": 6.192, "args": {"Python parent id": 205, "Python id": 216, "Ev Idx": 215}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595619265747.192, "dur": 0.869, "args": {"Python parent id": 216, "Python id": 217, "Ev Idx": 216}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(220): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595619265760.813, "dur": 63.218, "args": {"Python parent id": 205, "Python id": 218, "Ev Idx": 217}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1091): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595619265768.284, "dur": 39.13, "args": {"Python parent id": 218, "Python id": 219, "Ev Idx": 218}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(991): update", "pid": 1304242, "tid": 1304242, "ts": 6595619265779.07, "dur": 26.525, "args": {"Python parent id": 219, "Python id": 220, "Ev Idx": 219}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619265780.949, "dur": 10.818, "args": {"Python parent id": 220, "Python id": 221, "Ev Idx": 220}}, {"ph": "X", "cat": "python_function", "name": "abc.py(117): __instancecheck__", "pid": 1304242, "tid": 1304242, "ts": 6595619265783.681, "dur": 7.555, "args": {"Python parent id": 221, "Python id": 222, "Ev Idx": 221}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _abc_instancecheck>", "pid": 1304242, "tid": 1304242, "ts": 6595619265784.861, "dur": 5.929, "args": {"Python parent id": 222, "Python id": 223, "Ev Idx": 222}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1108): __setitem__", "pid": 1304242, "tid": 1304242, "ts": 6595619265797.32, "dur": 1.479, "args": {"Python parent id": 220, "Python id": 224, "Ev Idx": 223}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1108): __setitem__", "pid": 1304242, "tid": 1304242, "ts": 6595619265800.461, "dur": 0.691, "args": {"Python parent id": 220, "Python id": 225, "Ev Idx": 224}}, {"ph": "X", "cat": "python_function", "name": "<built-in method items of dict object at 0x7fa02f736d40>", "pid": 1304242, "tid": 1304242, "ts": 6595619265803.337, "dur": 0.566, "args": {"Python parent id": 220, "Python id": 226, "Ev Idx": 225}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619265810.754, "dur": 0.695, "args": {"Python parent id": 218, "Python id": 227, "Ev Idx": 226}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(689): convert_to_tensors", "pid": 1304242, "tid": 1304242, "ts": 6595619265821.505, "dur": 0.89, "args": {"Python parent id": 218, "Python id": 228, "Ev Idx": 227}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(840): items", "pid": 1304242, "tid": 1304242, "ts": 6595619265841.558, "dur": 9.697, "args": {"Python parent id": 204, "Python id": 229, "Ev Idx": 228}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(862): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595619265848.962, "dur": 1.592, "args": {"Python parent id": 229, "Python id": 230, "Ev Idx": 229}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(653): <dictcomp>", "pid": 1304242, "tid": 1304242, "ts": 6595619265859.741, "dur": 47.796, "args": {"Python parent id": 204, "Python id": 231, "Ev Idx": 230}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(909): __iter__", "pid": 1304242, "tid": 1304242, "ts": 6595619265861.407, "dur": 19.802, "args": {"Python parent id": 231, "Python id": 232, "Ev Idx": 231}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1114): __iter__", "pid": 1304242, "tid": 1304242, "ts": 6595619265865.074, "dur": 6.973, "args": {"Python parent id": 232, "Python id": 233, "Ev Idx": 232}}, {"ph": "X", "cat": "python_function", "name": "<built-in function iter>", "pid": 1304242, "tid": 1304242, "ts": 6595619265871.04, "dur": 0.713, "args": {"Python parent id": 233, "Python id": 234, "Ev Idx": 233}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(259): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595619265876.673, "dur": 3.423, "args": {"Python parent id": 232, "Python id": 235, "Ev Idx": 234}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619265878.826, "dur": 0.23, "args": {"Python parent id": 235, "Python id": 236, "Ev Idx": 235}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595619265883.068, "dur": 0.295, "args": {"Python parent id": 231, "Python id": 237, "Ev Idx": 236}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619265896.205, "dur": 0.286, "args": {"Python parent id": 231, "Python id": 238, "Ev Idx": 237}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(911): __iter__", "pid": 1304242, "tid": 1304242, "ts": 6595619265898.785, "dur": 3.394, "args": {"Python parent id": 231, "Python id": 239, "Ev Idx": 238}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(259): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595619265900.429, "dur": 1.271, "args": {"Python parent id": 239, "Python id": 240, "Ev Idx": 239}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619265901.159, "dur": 0.163, "args": {"Python parent id": 240, "Python id": 241, "Ev Idx": 240}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595619265903.153, "dur": 0.238, "args": {"Python parent id": 231, "Python id": 242, "Ev Idx": 241}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619265904.0, "dur": 0.193, "args": {"Python parent id": 231, "Python id": 243, "Ev Idx": 242}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(911): __iter__", "pid": 1304242, "tid": 1304242, "ts": 6595619265905.392, "dur": 0.545, "args": {"Python parent id": 231, "Python id": 244, "Ev Idx": 243}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(301): encodings", "pid": 1304242, "tid": 1304242, "ts": 6595619265916.006, "dur": 1.198, "args": {"Python parent id": 204, "Python id": 245, "Ev Idx": 244}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(220): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595619265920.628, "dur": 23.971, "args": {"Python parent id": 204, "Python id": 246, "Ev Idx": 245}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1091): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595619265924.32, "dur": 13.392, "args": {"Python parent id": 246, "Python id": 247, "Ev Idx": 246}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(991): update", "pid": 1304242, "tid": 1304242, "ts": 6595619265926.542, "dur": 10.11, "args": {"Python parent id": 247, "Python id": 248, "Ev Idx": 247}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619265927.448, "dur": 3.388, "args": {"Python parent id": 248, "Python id": 249, "Ev Idx": 248}}, {"ph": "X", "cat": "python_function", "name": "abc.py(117): __instancecheck__", "pid": 1304242, "tid": 1304242, "ts": 6595619265928.272, "dur": 2.271, "args": {"Python parent id": 249, "Python id": 250, "Ev Idx": 249}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _abc_instancecheck>", "pid": 1304242, "tid": 1304242, "ts": 6595619265928.914, "dur": 1.401, "args": {"Python parent id": 250, "Python id": 251, "Ev Idx": 250}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1108): __setitem__", "pid": 1304242, "tid": 1304242, "ts": 6595619265932.4, "dur": 0.775, "args": {"Python parent id": 248, "Python id": 252, "Ev Idx": 251}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1108): __setitem__", "pid": 1304242, "tid": 1304242, "ts": 6595619265934.003, "dur": 0.542, "args": {"Python parent id": 248, "Python id": 253, "Ev Idx": 252}}, {"ph": "X", "cat": "python_function", "name": "<built-in method items of dict object at 0x7fa02f736d40>", "pid": 1304242, "tid": 1304242, "ts": 6595619265935.538, "dur": 0.243, "args": {"Python parent id": 248, "Python id": 254, "Ev Idx": 253}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619265938.845, "dur": 0.381, "args": {"Python parent id": 246, "Python id": 255, "Ev Idx": 254}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(689): convert_to_tensors", "pid": 1304242, "tid": 1304242, "ts": 6595619265943.388, "dur": 0.55, "args": {"Python parent id": 246, "Python id": 256, "Ev Idx": 255}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(259): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595619265949.749, "dur": 1.515, "args": {"Python parent id": 204, "Python id": 257, "Ev Idx": 256}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619265950.643, "dur": 0.168, "args": {"Python parent id": 257, "Python id": 258, "Ev Idx": 257}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(3968): _eventual_warn_about_too_long_sequence", "pid": 1304242, "tid": 1304242, "ts": 6595619265953.109, "dur": 2.465, "args": {"Python parent id": 204, "Python id": 259, "Ev Idx": 258}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595619265954.282, "dur": 0.275, "args": {"Python parent id": 259, "Python id": 260, "Ev Idx": 259}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(3988): _switch_to_input_mode", "pid": 1304242, "tid": 1304242, "ts": 6595619266006.268, "dur": 0.661, "args": {"Python parent id": 193, "Python id": 261, "Ev Idx": 260}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(259): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595619266031.029, "dur": 1.39, "args": {"Python parent id": 191, "Python id": 262, "Ev Idx": 261}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619266031.65, "dur": 0.242, "args": {"Python parent id": 262, "Python id": 263, "Ev Idx": 262}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(577): _validate_one_request", "pid": 1304242, "tid": 1304242, "ts": 6595619266038.107, "dur": 14.367, "args": {"Python parent id": 191, "Python id": 264, "Ev Idx": 263}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595619266041.475, "dur": 0.199, "args": {"Python parent id": 264, "Python id": 265, "Ev Idx": 264}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619266044.064, "dur": 0.845, "args": {"Python parent id": 264, "Python id": 266, "Ev Idx": 265}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595619266046.846, "dur": 0.92, "args": {"Python parent id": 264, "Python id": 267, "Ev Idx": 266}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619266050.006, "dur": 0.313, "args": {"Python parent id": 264, "Python id": 268, "Ev Idx": 267}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(658): _create_tokenized_object", "pid": 1304242, "tid": 1304242, "ts": 6595619266055.604, "dur": 120.438, "args": {"Python parent id": 191, "Python id": 269, "Ev Idx": 268}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(31): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595619266063.174, "dur": 34.336, "args": {"Python parent id": 269, "Python id": 270, "Ev Idx": 269}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(149): normalize", "pid": 1304242, "tid": 1304242, "ts": 6595619266100.832, "dur": 2.55, "args": {"Python parent id": 269, "Python id": 271, "Ev Idx": 270}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(92): verify", "pid": 1304242, "tid": 1304242, "ts": 6595619266107.276, "dur": 36.758, "args": {"Python parent id": 269, "Python id": 272, "Ev Idx": 271}}, {"ph": "X", "cat": "python_function", "name": "<built-in function sum>", "pid": 1304242, "tid": 1304242, "ts": 6595619266121.452, "dur": 9.491, "args": {"Python parent id": 272, "Python id": 273, "Ev Idx": 272}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(146): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595619266123.953, "dur": 1.386, "args": {"Python parent id": 273, "Python id": 274, "Ev Idx": 273}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(146): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595619266126.773, "dur": 0.671, "args": {"Python parent id": 273, "Python id": 275, "Ev Idx": 274}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(146): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595619266128.469, "dur": 0.338, "args": {"Python parent id": 273, "Python id": 276, "Ev Idx": 275}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(146): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595619266129.684, "dur": 0.366, "args": {"Python parent id": 273, "Python id": 277, "Ev Idx": 276}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619266146.056, "dur": 0.11, "args": {"Python parent id": 269, "Python id": 278, "Ev Idx": 277}}, {"ph": "X", "cat": "python_function", "name": "<string>(2): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595619266160.382, "dur": 13.509, "args": {"Python parent id": 269, "Python id": 279, "Ev Idx": 278}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(762): _send_one_request", "pid": 1304242, "tid": 1304242, "ts": 6595619266188.176, "dur": 566.873, "args": {"Python parent id": 90, "Python id": 280, "Ev Idx": 279}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/socket.py(933): send_pyobj", "pid": 1304242, "tid": 1304242, "ts": 6595619266199.277, "dur": 473.789, "args": {"Python parent id": 280, "Python id": 281, "Ev Idx": 280}}, {"ph": "X", "cat": "python_function", "name": "<built-in function dumps>", "pid": 1304242, "tid": 1304242, "ts": 6595619266206.546, "dur": 145.189, "args": {"Python parent id": 281, "Python id": 282, "Ev Idx": 281}}, {"ph": "X", "cat": "python_function", "name": "copyreg.py(109): _slotnames", "pid": 1304242, "tid": 1304242, "ts": 6595619266232.074, "dur": 27.505, "args": {"Python parent id": 282, "Python id": 283, "Ev Idx": 282}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of mappingproxy object at 0x7fa02f700640>", "pid": 1304242, "tid": 1304242, "ts": 6595619266246.488, "dur": 1.807, "args": {"Python parent id": 283, "Python id": 284, "Ev Idx": 283}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595619266250.709, "dur": 5.016, "args": {"Python parent id": 283, "Python id": 285, "Ev Idx": 284}}, {"ph": "X", "cat": "python_function", "name": "copyreg.py(109): _slotnames", "pid": 1304242, "tid": 1304242, "ts": 6595619266307.135, "dur": 7.881, "args": {"Python parent id": 282, "Python id": 286, "Ev Idx": 285}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of mappingproxy object at 0x7fa02f700640>", "pid": 1304242, "tid": 1304242, "ts": 6595619266308.678, "dur": 0.493, "args": {"Python parent id": 286, "Python id": 287, "Ev Idx": 286}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595619266310.41, "dur": 2.891, "args": {"Python parent id": 286, "Python id": 288, "Ev Idx": 287}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(317): send", "pid": 1304242, "tid": 1304242, "ts": 6595619266361.569, "dur": 310.066, "args": {"Python parent id": 281, "Python id": 289, "Ev Idx": 288}}, {"ph": "X", "cat": "python_function", "name": "<built-in method update of dict object at 0x7fa02f737540>", "pid": 1304242, "tid": 1304242, "ts": 6595619266371.094, "dur": 2.017, "args": {"Python parent id": 289, "Python id": 290, "Ev Idx": 289}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(525): _add_send_event", "pid": 1304242, "tid": 1304242, "ts": 6595619266377.453, "dur": 292.452, "args": {"Python parent id": 289, "Python id": 291, "Ev Idx": 290}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595619266396.294, "dur": 0.447, "args": {"Python parent id": 291, "Python id": 292, "Ev Idx": 291}}, {"ph": "X", "cat": "python_function", "name": "<built-in method copy of dict object at 0x7fa02f714bc0>", "pid": 1304242, "tid": 1304242, "ts": 6595619266403.823, "dur": 0.915, "args": {"Python parent id": 291, "Python id": 293, "Ev Idx": 292}}, {"ph": "X", "cat": "python_function", "name": "enum.py(980): __or__", "pid": 1304242, "tid": 1304242, "ts": 6595619266422.069, "dur": 148.406, "args": {"Python parent id": 291, "Python id": 294, "Ev Idx": 293}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619266425.698, "dur": 2.395, "args": {"Python parent id": 294, "Python id": 295, "Ev Idx": 294}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595619266435.988, "dur": 126.411, "args": {"Python parent id": 294, "Python id": 296, "Ev Idx": 295}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595619266442.126, "dur": 119.483, "args": {"Python parent id": 296, "Python id": 297, "Ev Idx": 296}}, {"ph": "X", "cat": "python_function", "name": "enum.py(937): _missing_", "pid": 1304242, "tid": 1304242, "ts": 6595619266459.839, "dur": 99.237, "args": {"Python parent id": 297, "Python id": 298, "Ev Idx": 297}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619266461.658, "dur": 0.268, "args": {"Python parent id": 298, "Python id": 299, "Ev Idx": 298}}, {"ph": "X", "cat": "python_function", "name": "enum.py(947): _create_pseudo_member_", "pid": 1304242, "tid": 1304242, "ts": 6595619266466.466, "dur": 91.254, "args": {"Python parent id": 298, "Python id": 300, "Ev Idx": 299}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595619266469.273, "dur": 0.544, "args": {"Python parent id": 300, "Python id": 301, "Ev Idx": 300}}, {"ph": "X", "cat": "python_function", "name": "enum.py(1026): _decompose", "pid": 1304242, "tid": 1304242, "ts": 6595619266473.213, "dur": 61.929, "args": {"Python parent id": 300, "Python id": 302, "Ev Idx": 301}}, {"ph": "X", "cat": "python_function", "name": "enum.py(442): __iter__", "pid": 1304242, "tid": 1304242, "ts": 6595619266477.349, "dur": 5.634, "args": {"Python parent id": 302, "Python id": 303, "Ev Idx": 302}}, {"ph": "X", "cat": "python_function", "name": "enum.py(446): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595619266485.001, "dur": 3.849, "args": {"Python parent id": 302, "Python id": 304, "Ev Idx": 303}}, {"ph": "X", "cat": "python_function", "name": "types.py(176): __get__", "pid": 1304242, "tid": 1304242, "ts": 6595619266494.289, "dur": 7.177, "args": {"Python parent id": 302, "Python id": 305, "Ev Idx": 304}}, {"ph": "X", "cat": "python_function", "name": "enum.py(801): value", "pid": 1304242, "tid": 1304242, "ts": 6595619266499.665, "dur": 1.04, "args": {"Python parent id": 305, "Python id": 306, "Ev Idx": 305}}, {"ph": "X", "cat": "python_function", "name": "enum.py(446): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595619266506.051, "dur": 2.503, "args": {"Python parent id": 302, "Python id": 307, "Ev Idx": 306}}, {"ph": "X", "cat": "python_function", "name": "types.py(176): __get__", "pid": 1304242, "tid": 1304242, "ts": 6595619266510.007, "dur": 5.959, "args": {"Python parent id": 302, "Python id": 308, "Ev Idx": 307}}, {"ph": "X", "cat": "python_function", "name": "enum.py(801): value", "pid": 1304242, "tid": 1304242, "ts": 6595619266511.203, "dur": 4.417, "args": {"Python parent id": 308, "Python id": 309, "Ev Idx": 308}}, {"ph": "X", "cat": "python_function", "name": "enum.py(446): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595619266517.38, "dur": 0.461, "args": {"Python parent id": 302, "Python id": 310, "Ev Idx": 309}}, {"ph": "X", "cat": "python_function", "name": "<built-in method sort of list object at 0x7fa02f736480>", "pid": 1304242, "tid": 1304242, "ts": 6595619266529.573, "dur": 2.68, "args": {"Python parent id": 302, "Python id": 311, "Ev Idx": 310}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595619266533.937, "dur": 0.32, "args": {"Python parent id": 302, "Python id": 312, "Ev Idx": 311}}, {"ph": "X", "cat": "python_function", "name": "<built-in method __new__ of type object at 0x57ea91926320>", "pid": 1304242, "tid": 1304242, "ts": 6595619266543.824, "dur": 2.411, "args": {"Python parent id": 300, "Python id": 313, "Ev Idx": 312}}, {"ph": "X", "cat": "python_function", "name": "<built-in method setdefault of dict object at 0x7fa1227fc500>", "pid": 1304242, "tid": 1304242, "ts": 6595619266555.66, "dur": 1.01, "args": {"Python parent id": 300, "Python id": 314, "Ev Idx": 313}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619266560.78, "dur": 0.218, "args": {"Python parent id": 297, "Python id": 315, "Ev Idx": 314}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595619266565.975, "dur": 4.107, "args": {"Python parent id": 294, "Python id": 316, "Ev Idx": 315}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595619266567.761, "dur": 1.767, "args": {"Python parent id": 316, "Python id": 317, "Ev Idx": 316}}, {"ph": "X", "cat": "python_function", "name": "<built-in function getattr>", "pid": 1304242, "tid": 1304242, "ts": 6595619266573.811, "dur": 3.13, "args": {"Python parent id": 291, "Python id": 318, "Ev Idx": 317}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/socket.py(623): send", "pid": 1304242, "tid": 1304242, "ts": 6595619266581.151, "dur": 74.774, "args": {"Python parent id": 291, "Python id": 319, "Ev Idx": 318}}, {"ph": "X", "cat": "python_function", "name": "<built-in method set_result of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595619266666.743, "dur": 0.699, "args": {"Python parent id": 291, "Python id": 320, "Ev Idx": 319}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(167): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595619266690.648, "dur": 13.767, "args": {"Python parent id": 280, "Python id": 321, "Ev Idx": 320}}, {"ph": "X", "cat": "python_function", "name": "asyncio/mixins.py(15): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595619266696.158, "dur": 1.669, "args": {"Python parent id": 321, "Python id": 322, "Ev Idx": 321}}, {"ph": "X", "cat": "python_function", "name": "<string>(2): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595619266713.025, "dur": 36.963, "args": {"Python parent id": 280, "Python id": 323, "Ev Idx": 322}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(86): add", "pid": 1304242, "tid": 1304242, "ts": 6595619266766.897, "dur": 7.684, "args": {"Python parent id": 90, "Python id": 324, "Ev Idx": 323}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add of set object at 0x7fa126a5dd20>", "pid": 1304242, "tid": 1304242, "ts": 6595619266773.089, "dur": 0.956, "args": {"Python parent id": 324, "Python id": 325, "Ev Idx": 324}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(797): _wait_one_response", "pid": 1304242, "tid": 1304242, "ts": 6595619266777.869, "dur": 124.442, "args": {"Python parent id": 90, "Python id": 326, "Ev Idx": 325}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(392): wait_for", "pid": 1304242, "tid": 1304242, "ts": 6595619266790.467, "dur": 111.303, "args": {"Python parent id": 326, "Python id": 327, "Ev Idx": 326}}, {"ph": "X", "cat": "python_function", "name": "<built-in function get_running_loop>", "pid": 1304242, "tid": 1304242, "ts": 6595619266801.833, "dur": 2.661, "args": {"Python parent id": 327, "Python id": 328, "Ev Idx": 327}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(610): ensure_future", "pid": 1304242, "tid": 1304242, "ts": 6595619266850.007, "dur": 42.742, "args": {"Python parent id": 327, "Python id": 329, "Ev Idx": 328}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(618): _ensure_future", "pid": 1304242, "tid": 1304242, "ts": 6595619266851.563, "dur": 40.619, "args": {"Python parent id": 329, "Python id": 330, "Ev Idx": 329}}, {"ph": "X", "cat": "python_function", "name": "asyncio/base_futures.py(14): isfuture", "pid": 1304242, "tid": 1304242, "ts": 6595619266854.51, "dur": 5.894, "args": {"Python parent id": 330, "Python id": 331, "Ev Idx": 330}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595619266856.56, "dur": 3.511, "args": {"Python parent id": 331, "Python id": 332, "Ev Idx": 331}}, {"ph": "X", "cat": "python_function", "name": "asyncio/coroutines.py(177): iscoroutine", "pid": 1304242, "tid": 1304242, "ts": 6595619266863.042, "dur": 7.882, "args": {"Python parent id": 330, "Python id": 333, "Ev Idx": 332}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619266866.233, "dur": 0.235, "args": {"Python parent id": 333, "Python id": 334, "Ev Idx": 333}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595619266867.538, "dur": 0.512, "args": {"Python parent id": 333, "Python id": 335, "Ev Idx": 334}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add of set object at 0x7fa126a5dd20>", "pid": 1304242, "tid": 1304242, "ts": 6595619266870.144, "dur": 0.272, "args": {"Python parent id": 333, "Python id": 336, "Ev Idx": 335}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(86): add", "pid": 1304242, "tid": 1304242, "ts": 6595619266887.26, "dur": 3.544, "args": {"Python parent id": 330, "Python id": 337, "Ev Idx": 336}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add of set object at 0x7fa126a5dd20>", "pid": 1304242, "tid": 1304242, "ts": 6595619266890.283, "dur": 0.28, "args": {"Python parent id": 337, "Python id": 338, "Ev Idx": 337}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add_done_callback of _asyncio.Task object at 0x7fa02f684c70>", "pid": 1304242, "tid": 1304242, "ts": 6595619266898.835, "dur": 0.619, "args": {"Python parent id": 327, "Python id": 339, "Ev Idx": 338}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(2065): print_exception_wrapper", "pid": 1304242, "tid": 1304242, "ts": 6595619266965.433, "dur": 515.93, "args": {"Python parent id": 38, "Python id": 340, "Ev Idx": 339}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(1577): handle_loop", "pid": 1304242, "tid": 1304242, "ts": 6595619266968.924, "dur": 512.012, "args": {"Python parent id": 340, "Python id": 341, "Ev Idx": 340}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/socket.py(961): recv_pyobj", "pid": 1304242, "tid": 1304242, "ts": 6595619266974.371, "dur": 505.044, "args": {"Python parent id": 341, "Python id": 342, "Ev Idx": 341}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(281): recv", "pid": 1304242, "tid": 1304242, "ts": 6595619266977.604, "dur": 486.341, "args": {"Python parent id": 342, "Python id": 343, "Ev Idx": 342}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(470): _add_recv_event", "pid": 1304242, "tid": 1304242, "ts": 6595619266983.407, "dur": 479.793, "args": {"Python parent id": 343, "Python id": 344, "Ev Idx": 343}}, {"ph": "X", "cat": "python_function", "name": "<built-in method startswith of str object at 0x7fa2cbeb2070>", "pid": 1304242, "tid": 1304242, "ts": 6595619266997.4, "dur": 2.79, "args": {"Python parent id": 344, "Python id": 345, "Ev Idx": 344}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595619267001.733, "dur": 0.579, "args": {"Python parent id": 344, "Python id": 346, "Ev Idx": 345}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595619267007.796, "dur": 18.094, "args": {"Python parent id": 344, "Python id": 347, "Ev Idx": 346}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619267010.912, "dur": 1.687, "args": {"Python parent id": 347, "Python id": 348, "Ev Idx": 347}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595619267016.379, "dur": 4.831, "args": {"Python parent id": 347, "Python id": 349, "Ev Idx": 348}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595619267018.639, "dur": 1.99, "args": {"Python parent id": 349, "Python id": 350, "Ev Idx": 349}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595619267023.505, "dur": 2.058, "args": {"Python parent id": 347, "Python id": 351, "Ev Idx": 350}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595619267024.555, "dur": 0.809, "args": {"Python parent id": 351, "Python id": 352, "Ev Idx": 351}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595619267028.525, "dur": 1.472, "args": {"Python parent id": 344, "Python id": 353, "Ev Idx": 352}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/attrsettr.py(43): __getattr__", "pid": 1304242, "tid": 1304242, "ts": 6595619267040.235, "dur": 50.059, "args": {"Python parent id": 344, "Python id": 354, "Ev Idx": 353}}, {"ph": "X", "cat": "python_function", "name": "<built-in method upper of str object at 0x7fa1224219f0>", "pid": 1304242, "tid": 1304242, "ts": 6595619267045.755, "dur": 1.334, "args": {"Python parent id": 354, "Python id": 355, "Ev Idx": 354}}, {"ph": "X", "cat": "python_function", "name": "<built-in function getattr>", "pid": 1304242, "tid": 1304242, "ts": 6595619267049.208, "dur": 1.973, "args": {"Python parent id": 354, "Python id": 356, "Ev Idx": 355}}, {"ph": "X", "cat": "python_function", "name": "<frozen importlib._bootstrap>(1053): _handle_fromlist", "pid": 1304242, "tid": 1304242, "ts": 6595619267059.507, "dur": 6.739, "args": {"Python parent id": 354, "Python id": 357, "Ev Idx": 356}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619267061.876, "dur": 0.2, "args": {"Python parent id": 357, "Python id": 358, "Ev Idx": 357}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595619267064.547, "dur": 0.55, "args": {"Python parent id": 357, "Python id": 359, "Ev Idx": 358}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/attrsettr.py(66): _get_attr_opt", "pid": 1304242, "tid": 1304242, "ts": 6595619267070.584, "dur": 18.779, "args": {"Python parent id": 354, "Python id": 360, "Ev Idx": 359}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595619267077.054, "dur": 3.178, "args": {"Python parent id": 360, "Python id": 361, "Ev Idx": 360}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595619267078.242, "dur": 1.674, "args": {"Python parent id": 361, "Python id": 362, "Ev Idx": 361}}, {"ph": "X", "cat": "python_function", "name": "<string>(1): <lambda>", "pid": 1304242, "tid": 1304242, "ts": 6595619267102.434, "dur": 3.726, "args": {"Python parent id": 344, "Python id": 363, "Ev Idx": 362}}, {"ph": "X", "cat": "python_function", "name": "<built-in method __new__ of type object at 0x57ea91926320>", "pid": 1304242, "tid": 1304242, "ts": 6595619267104.271, "dur": 1.301, "args": {"Python parent id": 363, "Python id": 364, "Ev Idx": 363}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of collections.deque object at 0x7fa02f673580>", "pid": 1304242, "tid": 1304242, "ts": 6595619267113.217, "dur": 0.475, "args": {"Python parent id": 344, "Python id": 365, "Ev Idx": 364}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595619267118.262, "dur": 2.381, "args": {"Python parent id": 344, "Python id": 366, "Ev Idx": 365}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595619267119.269, "dur": 1.142, "args": {"Python parent id": 366, "Python id": 367, "Ev Idx": 366}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595619267172.551, "dur": 93.381, "args": {"Python parent id": 344, "Python id": 368, "Ev Idx": 367}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619267175.988, "dur": 1.023, "args": {"Python parent id": 368, "Python id": 369, "Ev Idx": 368}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595619267183.002, "dur": 77.81, "args": {"Python parent id": 368, "Python id": 370, "Ev Idx": 369}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595619267184.955, "dur": 75.181, "args": {"Python parent id": 370, "Python id": 371, "Ev Idx": 370}}, {"ph": "X", "cat": "python_function", "name": "enum.py(937): _missing_", "pid": 1304242, "tid": 1304242, "ts": 6595619267195.74, "dur": 62.073, "args": {"Python parent id": 371, "Python id": 372, "Ev Idx": 371}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619267196.953, "dur": 0.177, "args": {"Python parent id": 372, "Python id": 373, "Ev Idx": 372}}, {"ph": "X", "cat": "python_function", "name": "enum.py(947): _create_pseudo_member_", "pid": 1304242, "tid": 1304242, "ts": 6595619267199.08, "dur": 57.602, "args": {"Python parent id": 372, "Python id": 374, "Ev Idx": 373}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595619267200.672, "dur": 0.573, "args": {"Python parent id": 374, "Python id": 375, "Ev Idx": 374}}, {"ph": "X", "cat": "python_function", "name": "enum.py(1026): _decompose", "pid": 1304242, "tid": 1304242, "ts": 6595619267203.052, "dur": 41.722, "args": {"Python parent id": 374, "Python id": 376, "Ev Idx": 375}}, {"ph": "X", "cat": "python_function", "name": "enum.py(442): __iter__", "pid": 1304242, "tid": 1304242, "ts": 6595619267205.333, "dur": 6.486, "args": {"Python parent id": 376, "Python id": 377, "Ev Idx": 376}}, {"ph": "X", "cat": "python_function", "name": "enum.py(446): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595619267212.953, "dur": 2.529, "args": {"Python parent id": 376, "Python id": 378, "Ev Idx": 377}}, {"ph": "X", "cat": "python_function", "name": "types.py(176): __get__", "pid": 1304242, "tid": 1304242, "ts": 6595619267217.758, "dur": 3.252, "args": {"Python parent id": 376, "Python id": 379, "Ev Idx": 378}}, {"ph": "X", "cat": "python_function", "name": "enum.py(801): value", "pid": 1304242, "tid": 1304242, "ts": 6595619267219.96, "dur": 0.645, "args": {"Python parent id": 379, "Python id": 380, "Ev Idx": 379}}, {"ph": "X", "cat": "python_function", "name": "enum.py(446): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595619267222.682, "dur": 1.661, "args": {"Python parent id": 376, "Python id": 381, "Ev Idx": 380}}, {"ph": "X", "cat": "python_function", "name": "types.py(176): __get__", "pid": 1304242, "tid": 1304242, "ts": 6595619267225.308, "dur": 1.638, "args": {"Python parent id": 376, "Python id": 382, "Ev Idx": 381}}, {"ph": "X", "cat": "python_function", "name": "enum.py(801): value", "pid": 1304242, "tid": 1304242, "ts": 6595619267226.172, "dur": 0.319, "args": {"Python parent id": 382, "Python id": 383, "Ev Idx": 382}}, {"ph": "X", "cat": "python_function", "name": "enum.py(446): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595619267227.997, "dur": 0.759, "args": {"Python parent id": 376, "Python id": 384, "Ev Idx": 383}}, {"ph": "X", "cat": "python_function", "name": "types.py(176): __get__", "pid": 1304242, "tid": 1304242, "ts": 6595619267229.403, "dur": 1.506, "args": {"Python parent id": 376, "Python id": 385, "Ev Idx": 384}}, {"ph": "X", "cat": "python_function", "name": "enum.py(801): value", "pid": 1304242, "tid": 1304242, "ts": 6595619267230.185, "dur": 0.34, "args": {"Python parent id": 385, "Python id": 386, "Ev Idx": 385}}, {"ph": "X", "cat": "python_function", "name": "enum.py(446): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595619267231.605, "dur": 0.814, "args": {"Python parent id": 376, "Python id": 387, "Ev Idx": 386}}, {"ph": "X", "cat": "python_function", "name": "types.py(176): __get__", "pid": 1304242, "tid": 1304242, "ts": 6595619267232.974, "dur": 1.36, "args": {"Python parent id": 376, "Python id": 388, "Ev Idx": 387}}, {"ph": "X", "cat": "python_function", "name": "enum.py(801): value", "pid": 1304242, "tid": 1304242, "ts": 6595619267233.765, "dur": 0.304, "args": {"Python parent id": 388, "Python id": 389, "Ev Idx": 388}}, {"ph": "X", "cat": "python_function", "name": "enum.py(446): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595619267235.048, "dur": 0.461, "args": {"Python parent id": 376, "Python id": 390, "Ev Idx": 389}}, {"ph": "X", "cat": "python_function", "name": "<built-in method sort of list object at 0x7fa02f736480>", "pid": 1304242, "tid": 1304242, "ts": 6595619267240.799, "dur": 1.517, "args": {"Python parent id": 376, "Python id": 391, "Ev Idx": 390}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595619267243.503, "dur": 0.297, "args": {"Python parent id": 376, "Python id": 392, "Ev Idx": 391}}, {"ph": "X", "cat": "python_function", "name": "<built-in method __new__ of type object at 0x57ea91926320>", "pid": 1304242, "tid": 1304242, "ts": 6595619267248.791, "dur": 1.152, "args": {"Python parent id": 374, "Python id": 393, "Ev Idx": 392}}, {"ph": "X", "cat": "python_function", "name": "<built-in method setdefault of dict object at 0x7fa1227fc500>", "pid": 1304242, "tid": 1304242, "ts": 6595619267254.328, "dur": 1.56, "args": {"Python parent id": 374, "Python id": 394, "Ev Idx": 393}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619267259.243, "dur": 0.298, "args": {"Python parent id": 371, "Python id": 395, "Ev Idx": 394}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595619267262.949, "dur": 2.602, "args": {"Python parent id": 368, "Python id": 396, "Ev Idx": 395}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595619267264.169, "dur": 1.105, "args": {"Python parent id": 396, "Python id": 397, "Ev Idx": 396}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add_done_callback of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595619267278.806, "dur": 0.679, "args": {"Python parent id": 344, "Python id": 398, "Ev Idx": 397}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(698): _add_io_state", "pid": 1304242, "tid": 1304242, "ts": 6595619267283.457, "dur": 179.178, "args": {"Python parent id": 344, "Python id": 399, "Ev Idx": 398}}, {"ph": "X", "cat": "python_function", "name": "enum.py(980): __or__", "pid": 1304242, "tid": 1304242, "ts": 6595619267287.744, "dur": 10.769, "args": {"Python parent id": 399, "Python id": 400, "Ev Idx": 399}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619267289.076, "dur": 0.842, "args": {"Python parent id": 400, "Python id": 401, "Ev Idx": 400}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595619267291.765, "dur": 2.165, "args": {"Python parent id": 400, "Python id": 402, "Ev Idx": 401}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595619267292.81, "dur": 0.882, "args": {"Python parent id": 402, "Python id": 403, "Ev Idx": 402}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595619267295.747, "dur": 2.305, "args": {"Python parent id": 400, "Python id": 404, "Ev Idx": 403}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595619267296.825, "dur": 1.022, "args": {"Python parent id": 404, "Python id": 405, "Ev Idx": 404}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/socket.py(376): __setattr__", "pid": 1304242, "tid": 1304242, "ts": 6595619267301.055, "dur": 6.445, "args": {"Python parent id": 399, "Python id": 406, "Ev Idx": 405}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(710): _update_handler", "pid": 1304242, "tid": 1304242, "ts": 6595619267311.251, "dur": 150.867, "args": {"Python parent id": 399, "Python id": 407, "Ev Idx": 406}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(49): _get_loop", "pid": 1304242, "tid": 1304242, "ts": 6595619267314.061, "dur": 110.0, "args": {"Python parent id": 407, "Python id": 408, "Ev Idx": 407}}, {"ph": "X", "cat": "python_function", "name": "zmq/asyncio.py(106): _default_loop", "pid": 1304242, "tid": 1304242, "ts": 6595619267318.177, "dur": 8.01, "args": {"Python parent id": 408, "Python id": 409, "Ev Idx": 408}}, {"ph": "X", "cat": "python_function", "name": "<built-in function get_running_loop>", "pid": 1304242, "tid": 1304242, "ts": 6595619267324.016, "dur": 1.784, "args": {"Python parent id": 409, "Python id": 410, "Ev Idx": 409}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/socket.py(376): __setattr__", "pid": 1304242, "tid": 1304242, "ts": 6595619267327.81, "dur": 40.055, "args": {"Python parent id": 408, "Python id": 411, "Ev Idx": 410}}, {"ph": "X", "cat": "python_function", "name": "<built-in method lower of str object at 0x7fa12240ff70>", "pid": 1304242, "tid": 1304242, "ts": 6595619267334.268, "dur": 1.189, "args": {"Python parent id": 411, "Python id": 412, "Ev Idx": 411}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/attrsettr.py(17): __setattr__", "pid": 1304242, "tid": 1304242, "ts": 6595619267341.475, "dur": 25.588, "args": {"Python parent id": 411, "Python id": 413, "Ev Idx": 412}}, {"ph": "X", "cat": "python_function", "name": "<built-in method mro of type object at 0x57eabe285650>", "pid": 1304242, "tid": 1304242, "ts": 6595619267349.439, "dur": 5.009, "args": {"Python parent id": 413, "Python id": 414, "Ev Idx": 413}}, {"ph": "X", "cat": "python_function", "name": "<built-in function getattr>", "pid": 1304242, "tid": 1304242, "ts": 6595619267357.976, "dur": 1.652, "args": {"Python parent id": 413, "Python id": 415, "Ev Idx": 414}}, {"ph": "X", "cat": "python_function", "name": "<built-in function getattr>", "pid": 1304242, "tid": 1304242, "ts": 6595619267361.465, "dur": 0.346, "args": {"Python parent id": 413, "Python id": 416, "Ev Idx": 415}}, {"ph": "X", "cat": "python_function", "name": "<built-in function getattr>", "pid": 1304242, "tid": 1304242, "ts": 6595619267362.781, "dur": 0.255, "args": {"Python parent id": 413, "Python id": 417, "Ev Idx": 416}}, {"ph": "X", "cat": "python_function", "name": "zmq/asyncio.py(148): _init_io_state", "pid": 1304242, "tid": 1304242, "ts": 6595619267371.646, "dur": 51.241, "args": {"Python parent id": 408, "Python id": 418, "Ev Idx": 417}}, {"ph": "X", "cat": "python_function", "name": "zmq/asyncio.py(143): _get_selector", "pid": 1304242, "tid": 1304242, "ts": 6595619267373.782, "dur": 6.825, "args": {"Python parent id": 418, "Python id": 419, "Ev Idx": 418}}, {"ph": "X", "cat": "python_function", "name": "zmq/asyncio.py(90): _get_selector_noop", "pid": 1304242, "tid": 1304242, "ts": 6595619267379.736, "dur": 0.458, "args": {"Python parent id": 419, "Python id": 420, "Ev Idx": 419}}, {"ph": "X", "cat": "python_function", "name": "weakref.py(134): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595619267389.466, "dur": 3.602, "args": {"Python parent id": 418, "Python id": 421, "Ev Idx": 420}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(682): _schedule_remaining_events", "pid": 1304242, "tid": 1304242, "ts": 6595619267428.09, "dur": 33.184, "args": {"Python parent id": 407, "Python id": 422, "Ev Idx": 421}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595619267432.919, "dur": 2.955, "args": {"Python parent id": 422, "Python id": 423, "Ev Idx": 422}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595619267434.388, "dur": 1.14, "args": {"Python parent id": 423, "Python id": 424, "Ev Idx": 423}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595619267445.967, "dur": 10.45, "args": {"Python parent id": 422, "Python id": 425, "Ev Idx": 424}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595619267447.526, "dur": 0.858, "args": {"Python parent id": 425, "Python id": 426, "Ev Idx": 425}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595619267450.22, "dur": 2.414, "args": {"Python parent id": 425, "Python id": 427, "Ev Idx": 426}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595619267451.257, "dur": 1.108, "args": {"Python parent id": 427, "Python id": 428, "Ev Idx": 427}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595619267454.099, "dur": 2.003, "args": {"Python parent id": 425, "Python id": 429, "Ev Idx": 428}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595619267454.949, "dur": 0.896, "args": {"Python parent id": 429, "Python id": 430, "Ev Idx": 429}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(337): _deserialize", "pid": 1304242, "tid": 1304242, "ts": 6595619267469.13, "dur": 9.517, "args": {"Python parent id": 342, "Python id": 431, "Ev Idx": 430}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add_done_callback of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595619267475.124, "dur": 1.173, "args": {"Python parent id": 431, "Python id": 432, "Ev Idx": 431}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add_done_callback of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595619267477.966, "dur": 0.321, "args": {"Python parent id": 431, "Python id": 433, "Ev Idx": 432}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(2065): print_exception_wrapper", "pid": 1304242, "tid": 1304242, "ts": 6595619267488.977, "dur": 38.772, "args": {"Python parent id": 38, "Python id": 434, "Ev Idx": 433}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(1540): sigterm_watchdog", "pid": 1304242, "tid": 1304242, "ts": 6595619267492.232, "dur": 35.326, "args": {"Python parent id": 434, "Python id": 435, "Ev Idx": 434}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(593): sleep", "pid": 1304242, "tid": 1304242, "ts": 6595619267496.956, "dur": 30.359, "args": {"Python parent id": 435, "Python id": 436, "Ev Idx": 435}}, {"ph": "X", "cat": "python_function", "name": "<built-in function get_running_loop>", "pid": 1304242, "tid": 1304242, "ts": 6595619267503.306, "dur": 1.471, "args": {"Python parent id": 436, "Python id": 437, "Ev Idx": 436}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(201): wait", "pid": 1304242, "tid": 1304242, "ts": 6595619267531.232, "dur": 29.968, "args": {"Python parent id": 38, "Python id": 438, "Ev Idx": 437}}, {"ph": "X", "cat": "python_function", "name": "asyncio/mixins.py(22): _get_loop", "pid": 1304242, "tid": 1304242, "ts": 6595619267535.379, "dur": 21.122, "args": {"Python parent id": 438, "Python id": 439, "Ev Idx": 438}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _get_running_loop>", "pid": 1304242, "tid": 1304242, "ts": 6595619267540.955, "dur": 1.676, "args": {"Python parent id": 439, "Python id": 440, "Ev Idx": 439}}, {"ph": "X", "cat": "python_function", "name": "<built-in method __exit__ of _thread.lock object at 0x7fa1262bc680>", "pid": 1304242, "tid": 1304242, "ts": 6595619267554.164, "dur": 1.007, "args": {"Python parent id": 439, "Python id": 441, "Ev Idx": 440}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of collections.deque object at 0x7fa02f673580>", "pid": 1304242, "tid": 1304242, "ts": 6595619267560.118, "dur": 0.272, "args": {"Python parent id": 438, "Python id": 442, "Ev Idx": 441}}, {"ph": "X", "cat": "python_function", "name": "zmq/asyncio.py(151): <lambda>", "pid": 1304242, "tid": 1304242, "ts": 6595622702580.071, "dur": 497.243, "args": {"Python parent id": 38, "Python id": 443, "Ev Idx": 442}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(670): _handle_events", "pid": 1304242, "tid": 1304242, "ts": 6595622702597.63, "dur": 478.949, "args": {"Python parent id": 443, "Python id": 444, "Ev Idx": 443}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622702623.088, "dur": 13.592, "args": {"Python parent id": 444, "Python id": 445, "Ev Idx": 444}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622702629.377, "dur": 6.178, "args": {"Python parent id": 445, "Python id": 446, "Ev Idx": 445}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595622702675.672, "dur": 24.627, "args": {"Python parent id": 444, "Python id": 447, "Ev Idx": 446}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622702681.06, "dur": 5.032, "args": {"Python parent id": 447, "Python id": 448, "Ev Idx": 447}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622702690.926, "dur": 3.697, "args": {"Python parent id": 447, "Python id": 449, "Ev Idx": 448}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622702692.738, "dur": 1.627, "args": {"Python parent id": 449, "Python id": 450, "Ev Idx": 449}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622702697.662, "dur": 2.176, "args": {"Python parent id": 447, "Python id": 451, "Ev Idx": 450}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622702698.795, "dur": 0.837, "args": {"Python parent id": 451, "Python id": 452, "Ev Idx": 451}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(586): _handle_recv", "pid": 1304242, "tid": 1304242, "ts": 6595622702705.798, "dur": 353.327, "args": {"Python parent id": 444, "Python id": 453, "Ev Idx": 452}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622702718.405, "dur": 2.068, "args": {"Python parent id": 453, "Python id": 454, "Ev Idx": 453}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622702719.386, "dur": 0.88, "args": {"Python parent id": 454, "Python id": 455, "Ev Idx": 454}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595622702729.214, "dur": 8.906, "args": {"Python parent id": 453, "Python id": 456, "Ev Idx": 455}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622702730.299, "dur": 0.893, "args": {"Python parent id": 456, "Python id": 457, "Ev Idx": 456}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622702732.718, "dur": 2.031, "args": {"Python parent id": 456, "Python id": 458, "Ev Idx": 457}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622702733.644, "dur": 0.893, "args": {"Python parent id": 458, "Python id": 459, "Ev Idx": 458}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622702736.041, "dur": 1.741, "args": {"Python parent id": 456, "Python id": 460, "Ev Idx": 459}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622702736.862, "dur": 0.712, "args": {"Python parent id": 460, "Python id": 461, "Ev Idx": 460}}, {"ph": "X", "cat": "python_function", "name": "<built-in method popleft of collections.deque object at 0x7fa02f673580>", "pid": 1304242, "tid": 1304242, "ts": 6595622702766.739, "dur": 0.756, "args": {"Python parent id": 453, "Python id": 462, "Ev Idx": 461}}, {"ph": "X", "cat": "python_function", "name": "<built-in method done of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622702779.449, "dur": 0.61, "args": {"Python parent id": 453, "Python id": 463, "Ev Idx": 462}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(704): _drop_io_state", "pid": 1304242, "tid": 1304242, "ts": 6595622702797.16, "dur": 191.736, "args": {"Python parent id": 453, "Python id": 464, "Ev Idx": 463}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595622702802.55, "dur": 8.628, "args": {"Python parent id": 464, "Python id": 465, "Ev Idx": 464}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622702803.486, "dur": 0.324, "args": {"Python parent id": 465, "Python id": 466, "Ev Idx": 465}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622702805.688, "dur": 1.969, "args": {"Python parent id": 465, "Python id": 467, "Ev Idx": 466}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622702806.622, "dur": 0.713, "args": {"Python parent id": 467, "Python id": 468, "Ev Idx": 467}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622702808.945, "dur": 1.953, "args": {"Python parent id": 465, "Python id": 469, "Ev Idx": 468}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622702809.764, "dur": 0.913, "args": {"Python parent id": 469, "Python id": 470, "Ev Idx": 469}}, {"ph": "X", "cat": "python_function", "name": "enum.py(1000): __invert__", "pid": 1304242, "tid": 1304242, "ts": 6595622702816.139, "dur": 142.738, "args": {"Python parent id": 464, "Python id": 471, "Ev Idx": 470}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622702818.796, "dur": 139.215, "args": {"Python parent id": 471, "Python id": 472, "Ev Idx": 471}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622702819.788, "dur": 137.556, "args": {"Python parent id": 472, "Python id": 473, "Ev Idx": 472}}, {"ph": "X", "cat": "python_function", "name": "enum.py(937): _missing_", "pid": 1304242, "tid": 1304242, "ts": 6595622702828.489, "dur": 126.375, "args": {"Python parent id": 473, "Python id": 474, "Ev Idx": 473}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622702830.093, "dur": 0.233, "args": {"Python parent id": 474, "Python id": 475, "Ev Idx": 474}}, {"ph": "X", "cat": "python_function", "name": "enum.py(947): _create_pseudo_member_", "pid": 1304242, "tid": 1304242, "ts": 6595622702832.727, "dur": 120.044, "args": {"Python parent id": 474, "Python id": 476, "Ev Idx": 475}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595622702835.215, "dur": 0.628, "args": {"Python parent id": 476, "Python id": 477, "Ev Idx": 476}}, {"ph": "X", "cat": "python_function", "name": "enum.py(1026): _decompose", "pid": 1304242, "tid": 1304242, "ts": 6595622702839.07, "dur": 69.324, "args": {"Python parent id": 476, "Python id": 478, "Ev Idx": 477}}, {"ph": "X", "cat": "python_function", "name": "enum.py(442): __iter__", "pid": 1304242, "tid": 1304242, "ts": 6595622702842.665, "dur": 5.441, "args": {"Python parent id": 478, "Python id": 479, "Ev Idx": 478}}, {"ph": "X", "cat": "python_function", "name": "enum.py(446): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595622702849.298, "dur": 3.88, "args": {"Python parent id": 478, "Python id": 480, "Ev Idx": 479}}, {"ph": "X", "cat": "python_function", "name": "types.py(176): __get__", "pid": 1304242, "tid": 1304242, "ts": 6595622702856.491, "dur": 4.686, "args": {"Python parent id": 478, "Python id": 481, "Ev Idx": 480}}, {"ph": "X", "cat": "python_function", "name": "enum.py(801): value", "pid": 1304242, "tid": 1304242, "ts": 6595622702859.916, "dur": 0.714, "args": {"Python parent id": 481, "Python id": 482, "Ev Idx": 481}}, {"ph": "X", "cat": "python_function", "name": "enum.py(446): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595622702865.023, "dur": 2.935, "args": {"Python parent id": 478, "Python id": 483, "Ev Idx": 482}}, {"ph": "X", "cat": "python_function", "name": "types.py(176): __get__", "pid": 1304242, "tid": 1304242, "ts": 6595622702868.812, "dur": 1.824, "args": {"Python parent id": 478, "Python id": 484, "Ev Idx": 483}}, {"ph": "X", "cat": "python_function", "name": "enum.py(801): value", "pid": 1304242, "tid": 1304242, "ts": 6595622702869.823, "dur": 0.51, "args": {"Python parent id": 484, "Python id": 485, "Ev Idx": 484}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of list object at 0x7fa02f714280>", "pid": 1304242, "tid": 1304242, "ts": 6595622702873.28, "dur": 0.664, "args": {"Python parent id": 478, "Python id": 486, "Ev Idx": 485}}, {"ph": "X", "cat": "python_function", "name": "enum.py(446): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595622702877.244, "dur": 0.878, "args": {"Python parent id": 478, "Python id": 487, "Ev Idx": 486}}, {"ph": "X", "cat": "python_function", "name": "types.py(176): __get__", "pid": 1304242, "tid": 1304242, "ts": 6595622702878.828, "dur": 1.552, "args": {"Python parent id": 478, "Python id": 488, "Ev Idx": 487}}, {"ph": "X", "cat": "python_function", "name": "enum.py(801): value", "pid": 1304242, "tid": 1304242, "ts": 6595622702879.741, "dur": 0.323, "args": {"Python parent id": 488, "Python id": 489, "Ev Idx": 488}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of list object at 0x7fa02f714280>", "pid": 1304242, "tid": 1304242, "ts": 6595622702881.592, "dur": 0.171, "args": {"Python parent id": 478, "Python id": 490, "Ev Idx": 489}}, {"ph": "X", "cat": "python_function", "name": "enum.py(446): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595622702882.931, "dur": 0.722, "args": {"Python parent id": 478, "Python id": 491, "Ev Idx": 490}}, {"ph": "X", "cat": "python_function", "name": "types.py(176): __get__", "pid": 1304242, "tid": 1304242, "ts": 6595622702884.226, "dur": 1.358, "args": {"Python parent id": 478, "Python id": 492, "Ev Idx": 491}}, {"ph": "X", "cat": "python_function", "name": "enum.py(801): value", "pid": 1304242, "tid": 1304242, "ts": 6595622702884.902, "dur": 0.497, "args": {"Python parent id": 492, "Python id": 493, "Ev Idx": 492}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of list object at 0x7fa02f714280>", "pid": 1304242, "tid": 1304242, "ts": 6595622702886.457, "dur": 0.109, "args": {"Python parent id": 478, "Python id": 494, "Ev Idx": 493}}, {"ph": "X", "cat": "python_function", "name": "enum.py(446): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595622702888.03, "dur": 0.469, "args": {"Python parent id": 478, "Python id": 495, "Ev Idx": 494}}, {"ph": "X", "cat": "python_function", "name": "<built-in method sort of list object at 0x7fa02f736480>", "pid": 1304242, "tid": 1304242, "ts": 6595622702893.21, "dur": 7.205, "args": {"Python parent id": 478, "Python id": 496, "Ev Idx": 495}}, {"ph": "X", "cat": "python_function", "name": "enum.py(1049): <lambda>", "pid": 1304242, "tid": 1304242, "ts": 6595622702895.964, "dur": 0.488, "args": {"Python parent id": 496, "Python id": 497, "Ev Idx": 496}}, {"ph": "X", "cat": "python_function", "name": "enum.py(1049): <lambda>", "pid": 1304242, "tid": 1304242, "ts": 6595622702897.397, "dur": 0.286, "args": {"Python parent id": 496, "Python id": 498, "Ev Idx": 497}}, {"ph": "X", "cat": "python_function", "name": "enum.py(1049): <lambda>", "pid": 1304242, "tid": 1304242, "ts": 6595622702898.219, "dur": 0.235, "args": {"Python parent id": 496, "Python id": 499, "Ev Idx": 498}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595622702902.993, "dur": 0.611, "args": {"Python parent id": 478, "Python id": 500, "Ev Idx": 499}}, {"ph": "X", "cat": "python_function", "name": "types.py(176): __get__", "pid": 1304242, "tid": 1304242, "ts": 6595622702905.94, "dur": 1.532, "args": {"Python parent id": 478, "Python id": 501, "Ev Idx": 500}}, {"ph": "X", "cat": "python_function", "name": "enum.py(801): value", "pid": 1304242, "tid": 1304242, "ts": 6595622702906.942, "dur": 0.278, "args": {"Python parent id": 501, "Python id": 502, "Ev Idx": 501}}, {"ph": "X", "cat": "python_function", "name": "enum.py(1005): _high_bit", "pid": 1304242, "tid": 1304242, "ts": 6595622702916.691, "dur": 8.813, "args": {"Python parent id": 476, "Python id": 503, "Ev Idx": 502}}, {"ph": "X", "cat": "python_function", "name": "<built-in method bit_length of int object at 0x7fa02f6a89f0>", "pid": 1304242, "tid": 1304242, "ts": 6595622702923.911, "dur": 0.461, "args": {"Python parent id": 503, "Python id": 504, "Ev Idx": 503}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of list object at 0x7fa02f714280>", "pid": 1304242, "tid": 1304242, "ts": 6595622702932.603, "dur": 0.587, "args": {"Python parent id": 476, "Python id": 505, "Ev Idx": 504}}, {"ph": "X", "cat": "python_function", "name": "<built-in method __new__ of type object at 0x57ea91926320>", "pid": 1304242, "tid": 1304242, "ts": 6595622702940.133, "dur": 2.557, "args": {"Python parent id": 476, "Python id": 506, "Ev Idx": 505}}, {"ph": "X", "cat": "python_function", "name": "<built-in method setdefault of dict object at 0x7fa1227fc500>", "pid": 1304242, "tid": 1304242, "ts": 6595622702946.977, "dur": 1.103, "args": {"Python parent id": 476, "Python id": 507, "Ev Idx": 506}}, {"ph": "X", "cat": "python_function", "name": "<built-in method __new__ of type object at 0x57ea91926320>", "pid": 1304242, "tid": 1304242, "ts": 6595622702949.268, "dur": 0.548, "args": {"Python parent id": 476, "Python id": 508, "Ev Idx": 507}}, {"ph": "X", "cat": "python_function", "name": "<built-in method setdefault of dict object at 0x7fa1227fc500>", "pid": 1304242, "tid": 1304242, "ts": 6595622702951.295, "dur": 0.488, "args": {"Python parent id": 476, "Python id": 509, "Ev Idx": 508}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622702956.575, "dur": 0.26, "args": {"Python parent id": 473, "Python id": 510, "Ev Idx": 509}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595622702960.577, "dur": 10.718, "args": {"Python parent id": 464, "Python id": 511, "Ev Idx": 510}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622702961.804, "dur": 0.372, "args": {"Python parent id": 511, "Python id": 512, "Ev Idx": 511}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622702964.038, "dur": 2.44, "args": {"Python parent id": 511, "Python id": 513, "Ev Idx": 512}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622702965.469, "dur": 0.691, "args": {"Python parent id": 513, "Python id": 514, "Ev Idx": 513}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622702968.463, "dur": 2.548, "args": {"Python parent id": 511, "Python id": 515, "Ev Idx": 514}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622702969.413, "dur": 1.348, "args": {"Python parent id": 515, "Python id": 516, "Ev Idx": 515}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/socket.py(376): __setattr__", "pid": 1304242, "tid": 1304242, "ts": 6595622702973.976, "dur": 7.492, "args": {"Python parent id": 464, "Python id": 517, "Ev Idx": 516}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(710): _update_handler", "pid": 1304242, "tid": 1304242, "ts": 6595622702984.113, "dur": 4.5, "args": {"Python parent id": 464, "Python id": 518, "Ev Idx": 517}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(682): _schedule_remaining_events", "pid": 1304242, "tid": 1304242, "ts": 6595622702986.488, "dur": 1.552, "args": {"Python parent id": 518, "Python id": 519, "Ev Idx": 518}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(193): cancel", "pid": 1304242, "tid": 1304242, "ts": 6595622702998.121, "dur": 0.493, "args": {"Python parent id": 453, "Python id": 520, "Ev Idx": 519}}, {"ph": "X", "cat": "python_function", "name": "enum.py(980): __or__", "pid": 1304242, "tid": 1304242, "ts": 6595622703008.139, "dur": 15.973, "args": {"Python parent id": 453, "Python id": 521, "Ev Idx": 520}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622703010.018, "dur": 1.211, "args": {"Python parent id": 521, "Python id": 522, "Ev Idx": 521}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622703013.344, "dur": 2.869, "args": {"Python parent id": 521, "Python id": 523, "Ev Idx": 522}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622703014.906, "dur": 0.889, "args": {"Python parent id": 523, "Python id": 524, "Ev Idx": 523}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622703018.727, "dur": 4.954, "args": {"Python parent id": 521, "Python id": 525, "Ev Idx": 524}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622703022.491, "dur": 0.985, "args": {"Python parent id": 525, "Python id": 526, "Ev Idx": 525}}, {"ph": "X", "cat": "python_function", "name": "<built-in method set_result of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622703042.612, "dur": 16.098, "args": {"Python parent id": 453, "Python id": 527, "Ev Idx": 526}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595622703063.31, "dur": 9.568, "args": {"Python parent id": 444, "Python id": 528, "Ev Idx": 527}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622703064.604, "dur": 0.648, "args": {"Python parent id": 528, "Python id": 529, "Ev Idx": 528}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622703066.901, "dur": 2.173, "args": {"Python parent id": 528, "Python id": 530, "Ev Idx": 529}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622703067.901, "dur": 0.887, "args": {"Python parent id": 530, "Python id": 531, "Ev Idx": 530}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622703070.73, "dur": 1.867, "args": {"Python parent id": 528, "Python id": 532, "Ev Idx": 531}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622703071.601, "dur": 0.788, "args": {"Python parent id": 532, "Python id": 533, "Ev Idx": 532}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(682): _schedule_remaining_events", "pid": 1304242, "tid": 1304242, "ts": 6595622703075.233, "dur": 0.827, "args": {"Python parent id": 444, "Python id": 534, "Ev Idx": 533}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(452): _remove_finished_future", "pid": 1304242, "tid": 1304242, "ts": 6595622703093.238, "dur": 0.897, "args": {"Python parent id": 38, "Python id": 535, "Ev Idx": 534}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(341): _chain", "pid": 1304242, "tid": 1304242, "ts": 6595622703103.047, "dur": 171.097, "args": {"Python parent id": 38, "Python id": 536, "Ev Idx": 535}}, {"ph": "X", "cat": "python_function", "name": "<built-in method done of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622703108.717, "dur": 0.293, "args": {"Python parent id": 536, "Python id": 537, "Ev Idx": 536}}, {"ph": "X", "cat": "python_function", "name": "<built-in method exception of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622703118.761, "dur": 0.269, "args": {"Python parent id": 536, "Python id": 538, "Ev Idx": 537}}, {"ph": "X", "cat": "python_function", "name": "<built-in method result of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622703123.773, "dur": 0.215, "args": {"Python parent id": 536, "Python id": 539, "Ev Idx": 538}}, {"ph": "X", "cat": "python_function", "name": "<built-in function loads>", "pid": 1304242, "tid": 1304242, "ts": 6595622703128.352, "dur": 137.156, "args": {"Python parent id": 536, "Python id": 540, "Ev Idx": 539}}, {"ph": "X", "cat": "python_function", "name": "<built-in method set_result of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622703267.982, "dur": 5.488, "args": {"Python parent id": 536, "Python id": 541, "Ev Idx": 540}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(368): _chain_cancel", "pid": 1304242, "tid": 1304242, "ts": 6595622703290.699, "dur": 2.415, "args": {"Python parent id": 38, "Python id": 542, "Ev Idx": 541}}, {"ph": "X", "cat": "python_function", "name": "<built-in method done of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622703292.237, "dur": 0.318, "args": {"Python parent id": 542, "Python id": 543, "Ev Idx": 542}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(2071): print_exception_wrapper", "pid": 1304242, "tid": 1304242, "ts": 6595622703301.789, "dur": 346.15, "args": {"Python parent id": 38, "Python id": 544, "Ev Idx": 543}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(1581): handle_loop", "pid": 1304242, "tid": 1304242, "ts": 6595622703303.867, "dur": 343.54, "args": {"Python parent id": 544, "Python id": 545, "Ev Idx": 544}}, {"ph": "X", "cat": "python_function", "name": "sglang/utils.py(475): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622703315.916, "dur": 115.756, "args": {"Python parent id": 545, "Python id": 546, "Ev Idx": 545}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622703323.601, "dur": 0.381, "args": {"Python parent id": 546, "Python id": 547, "Ev Idx": 546}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(1585): _handle_batch_output", "pid": 1304242, "tid": 1304242, "ts": 6595622703326.387, "dur": 104.407, "args": {"Python parent id": 546, "Python id": 548, "Ev Idx": 547}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595622703332.384, "dur": 1.286, "args": {"Python parent id": 548, "Python id": 549, "Ev Idx": 548}}, {"ph": "X", "cat": "python_function", "name": "<built-in function getattr>", "pid": 1304242, "tid": 1304242, "ts": 6595622703343.886, "dur": 1.328, "args": {"Python parent id": 548, "Python id": 550, "Ev Idx": 549}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622703346.806, "dur": 1.266, "args": {"Python parent id": 548, "Python id": 551, "Ev Idx": 550}}, {"ph": "X", "cat": "python_function", "name": "<built-in method update of dict object at 0x7fa02f737540>", "pid": 1304242, "tid": 1304242, "ts": 6595622703352.184, "dur": 2.398, "args": {"Python parent id": 548, "Python id": 552, "Ev Idx": 551}}, {"ph": "X", "cat": "python_function", "name": "<built-in function getattr>", "pid": 1304242, "tid": 1304242, "ts": 6595622703357.234, "dur": 1.086, "args": {"Python parent id": 548, "Python id": 553, "Ev Idx": 552}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622703360.198, "dur": 0.203, "args": {"Python parent id": 548, "Python id": 554, "Ev Idx": 553}}, {"ph": "X", "cat": "python_function", "name": "<built-in method extend of list object at 0x7fa03508f040>", "pid": 1304242, "tid": 1304242, "ts": 6595622703390.404, "dur": 0.965, "args": {"Python parent id": 548, "Python id": 555, "Ev Idx": 554}}, {"ph": "X", "cat": "python_function", "name": "<built-in method copy of list object at 0x7fa03508f040>", "pid": 1304242, "tid": 1304242, "ts": 6595622703396.449, "dur": 0.882, "args": {"Python parent id": 548, "Python id": 556, "Ev Idx": 555}}, {"ph": "X", "cat": "python_function", "name": "<built-in function time>", "pid": 1304242, "tid": 1304242, "ts": 6595622703404.987, "dur": 0.896, "args": {"Python parent id": 548, "Python id": 557, "Ev Idx": 556}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of list object at 0x7fa02f714280>", "pid": 1304242, "tid": 1304242, "ts": 6595622703412.035, "dur": 0.389, "args": {"Python parent id": 548, "Python id": 558, "Ev Idx": 557}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(183): set", "pid": 1304242, "tid": 1304242, "ts": 6595622703417.368, "dur": 8.715, "args": {"Python parent id": 548, "Python id": 559, "Ev Idx": 558}}, {"ph": "X", "cat": "python_function", "name": "<built-in method done of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622703421.134, "dur": 0.269, "args": {"Python parent id": 559, "Python id": 560, "Ev Idx": 559}}, {"ph": "X", "cat": "python_function", "name": "<built-in method set_result of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622703422.5, "dur": 2.378, "args": {"Python parent id": 559, "Python id": 561, "Ev Idx": 560}}, {"ph": "X", "cat": "python_function", "name": "<built-in function time>", "pid": 1304242, "tid": 1304242, "ts": 6595622703433.817, "dur": 0.396, "args": {"Python parent id": 545, "Python id": 562, "Ev Idx": 561}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/socket.py(961): recv_pyobj", "pid": 1304242, "tid": 1304242, "ts": 6595622703438.002, "dur": 207.082, "args": {"Python parent id": 545, "Python id": 563, "Ev Idx": 562}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(281): recv", "pid": 1304242, "tid": 1304242, "ts": 6595622703440.199, "dur": 191.387, "args": {"Python parent id": 563, "Python id": 564, "Ev Idx": 563}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(470): _add_recv_event", "pid": 1304242, "tid": 1304242, "ts": 6595622703444.959, "dur": 185.805, "args": {"Python parent id": 564, "Python id": 565, "Ev Idx": 564}}, {"ph": "X", "cat": "python_function", "name": "<built-in method startswith of str object at 0x7fa2cbeb2070>", "pid": 1304242, "tid": 1304242, "ts": 6595622703453.491, "dur": 2.752, "args": {"Python parent id": 565, "Python id": 566, "Ev Idx": 565}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595622703457.514, "dur": 0.465, "args": {"Python parent id": 565, "Python id": 567, "Ev Idx": 566}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595622703460.432, "dur": 16.663, "args": {"Python parent id": 565, "Python id": 568, "Ev Idx": 567}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622703462.039, "dur": 1.516, "args": {"Python parent id": 568, "Python id": 569, "Ev Idx": 568}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622703468.059, "dur": 4.319, "args": {"Python parent id": 568, "Python id": 570, "Ev Idx": 569}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622703470.164, "dur": 1.864, "args": {"Python parent id": 570, "Python id": 571, "Ev Idx": 570}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622703474.694, "dur": 2.025, "args": {"Python parent id": 568, "Python id": 572, "Ev Idx": 571}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622703475.671, "dur": 0.785, "args": {"Python parent id": 572, "Python id": 573, "Ev Idx": 572}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595622703479.367, "dur": 1.261, "args": {"Python parent id": 565, "Python id": 574, "Ev Idx": 573}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/attrsettr.py(43): __getattr__", "pid": 1304242, "tid": 1304242, "ts": 6595622703491.125, "dur": 33.852, "args": {"Python parent id": 565, "Python id": 575, "Ev Idx": 574}}, {"ph": "X", "cat": "python_function", "name": "<built-in method upper of str object at 0x7fa1224219f0>", "pid": 1304242, "tid": 1304242, "ts": 6595622703492.657, "dur": 1.303, "args": {"Python parent id": 575, "Python id": 576, "Ev Idx": 575}}, {"ph": "X", "cat": "python_function", "name": "<built-in function getattr>", "pid": 1304242, "tid": 1304242, "ts": 6595622703495.493, "dur": 1.429, "args": {"Python parent id": 575, "Python id": 577, "Ev Idx": 576}}, {"ph": "X", "cat": "python_function", "name": "<frozen importlib._bootstrap>(1053): _handle_fromlist", "pid": 1304242, "tid": 1304242, "ts": 6595622703502.898, "dur": 5.46, "args": {"Python parent id": 575, "Python id": 578, "Ev Idx": 577}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622703504.953, "dur": 0.291, "args": {"Python parent id": 578, "Python id": 579, "Ev Idx": 578}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595622703506.945, "dur": 0.416, "args": {"Python parent id": 578, "Python id": 580, "Ev Idx": 579}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/attrsettr.py(66): _get_attr_opt", "pid": 1304242, "tid": 1304242, "ts": 6595622703511.465, "dur": 12.681, "args": {"Python parent id": 575, "Python id": 581, "Ev Idx": 580}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622703515.317, "dur": 2.566, "args": {"Python parent id": 581, "Python id": 582, "Ev Idx": 581}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622703516.345, "dur": 1.237, "args": {"Python parent id": 582, "Python id": 583, "Ev Idx": 582}}, {"ph": "X", "cat": "python_function", "name": "<string>(1): <lambda>", "pid": 1304242, "tid": 1304242, "ts": 6595622703531.767, "dur": 3.505, "args": {"Python parent id": 565, "Python id": 584, "Ev Idx": 583}}, {"ph": "X", "cat": "python_function", "name": "<built-in method __new__ of type object at 0x57ea91926320>", "pid": 1304242, "tid": 1304242, "ts": 6595622703533.317, "dur": 1.537, "args": {"Python parent id": 584, "Python id": 585, "Ev Idx": 584}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of collections.deque object at 0x7fa02f673580>", "pid": 1304242, "tid": 1304242, "ts": 6595622703538.549, "dur": 0.261, "args": {"Python parent id": 565, "Python id": 586, "Ev Idx": 585}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622703541.456, "dur": 2.655, "args": {"Python parent id": 565, "Python id": 587, "Ev Idx": 586}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622703542.773, "dur": 1.132, "args": {"Python parent id": 587, "Python id": 588, "Ev Idx": 587}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595622703554.856, "dur": 9.588, "args": {"Python parent id": 565, "Python id": 589, "Ev Idx": 588}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622703555.947, "dur": 0.834, "args": {"Python parent id": 589, "Python id": 590, "Ev Idx": 589}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622703558.499, "dur": 2.283, "args": {"Python parent id": 589, "Python id": 591, "Ev Idx": 590}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622703559.487, "dur": 1.095, "args": {"Python parent id": 591, "Python id": 592, "Ev Idx": 591}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622703562.256, "dur": 1.882, "args": {"Python parent id": 589, "Python id": 593, "Ev Idx": 592}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622703563.095, "dur": 0.848, "args": {"Python parent id": 593, "Python id": 594, "Ev Idx": 593}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add_done_callback of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622703570.816, "dur": 1.233, "args": {"Python parent id": 565, "Python id": 595, "Ev Idx": 594}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(698): _add_io_state", "pid": 1304242, "tid": 1304242, "ts": 6595622703573.993, "dur": 56.193, "args": {"Python parent id": 565, "Python id": 596, "Ev Idx": 595}}, {"ph": "X", "cat": "python_function", "name": "enum.py(980): __or__", "pid": 1304242, "tid": 1304242, "ts": 6595622703577.558, "dur": 9.967, "args": {"Python parent id": 596, "Python id": 597, "Ev Idx": 596}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622703578.956, "dur": 0.163, "args": {"Python parent id": 597, "Python id": 598, "Ev Idx": 597}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622703580.966, "dur": 1.962, "args": {"Python parent id": 597, "Python id": 599, "Ev Idx": 598}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622703582.173, "dur": 0.477, "args": {"Python parent id": 599, "Python id": 600, "Ev Idx": 599}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622703585.007, "dur": 2.131, "args": {"Python parent id": 597, "Python id": 601, "Ev Idx": 600}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622703586.052, "dur": 0.883, "args": {"Python parent id": 601, "Python id": 602, "Ev Idx": 601}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/socket.py(376): __setattr__", "pid": 1304242, "tid": 1304242, "ts": 6595622703589.4, "dur": 4.38, "args": {"Python parent id": 596, "Python id": 603, "Ev Idx": 602}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(710): _update_handler", "pid": 1304242, "tid": 1304242, "ts": 6595622703595.718, "dur": 33.878, "args": {"Python parent id": 596, "Python id": 604, "Ev Idx": 603}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(49): _get_loop", "pid": 1304242, "tid": 1304242, "ts": 6595622703597.625, "dur": 8.214, "args": {"Python parent id": 604, "Python id": 605, "Ev Idx": 604}}, {"ph": "X", "cat": "python_function", "name": "zmq/asyncio.py(106): _default_loop", "pid": 1304242, "tid": 1304242, "ts": 6595622703600.198, "dur": 4.348, "args": {"Python parent id": 605, "Python id": 606, "Ev Idx": 605}}, {"ph": "X", "cat": "python_function", "name": "<built-in function get_running_loop>", "pid": 1304242, "tid": 1304242, "ts": 6595622703602.421, "dur": 1.88, "args": {"Python parent id": 606, "Python id": 607, "Ev Idx": 606}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(682): _schedule_remaining_events", "pid": 1304242, "tid": 1304242, "ts": 6595622703607.673, "dur": 21.404, "args": {"Python parent id": 604, "Python id": 608, "Ev Idx": 607}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622703610.903, "dur": 2.203, "args": {"Python parent id": 608, "Python id": 609, "Ev Idx": 608}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622703611.863, "dur": 0.962, "args": {"Python parent id": 609, "Python id": 610, "Ev Idx": 609}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595622703619.767, "dur": 8.546, "args": {"Python parent id": 608, "Python id": 611, "Ev Idx": 610}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622703620.549, "dur": 0.564, "args": {"Python parent id": 611, "Python id": 612, "Ev Idx": 611}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622703622.391, "dur": 2.193, "args": {"Python parent id": 611, "Python id": 613, "Ev Idx": 612}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622703623.347, "dur": 1.026, "args": {"Python parent id": 613, "Python id": 614, "Ev Idx": 613}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622703625.801, "dur": 2.246, "args": {"Python parent id": 611, "Python id": 615, "Ev Idx": 614}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622703626.671, "dur": 0.757, "args": {"Python parent id": 615, "Python id": 616, "Ev Idx": 615}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(337): _deserialize", "pid": 1304242, "tid": 1304242, "ts": 6595622703635.675, "dur": 8.638, "args": {"Python parent id": 563, "Python id": 617, "Ev Idx": 616}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add_done_callback of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622703641.401, "dur": 1.152, "args": {"Python parent id": 617, "Python id": 618, "Ev Idx": 617}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add_done_callback of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622703643.766, "dur": 0.218, "args": {"Python parent id": 617, "Python id": 619, "Ev Idx": 618}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(214): wait", "pid": 1304242, "tid": 1304242, "ts": 6595622703663.004, "dur": 9.803, "args": {"Python parent id": 38, "Python id": 620, "Ev Idx": 619}}, {"ph": "X", "cat": "python_function", "name": "<built-in method remove of collections.deque object at 0x7fa034862200>", "pid": 1304242, "tid": 1304242, "ts": 6595622703671.4, "dur": 0.877, "args": {"Python parent id": 620, "Python id": 621, "Ev Idx": 620}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(387): _release_waiter", "pid": 1304242, "tid": 1304242, "ts": 6595622703684.608, "dur": 5.425, "args": {"Python parent id": 38, "Python id": 622, "Ev Idx": 621}}, {"ph": "X", "cat": "python_function", "name": "<built-in method done of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622703686.399, "dur": 0.407, "args": {"Python parent id": 622, "Python id": 623, "Ev Idx": 622}}, {"ph": "X", "cat": "python_function", "name": "<built-in method set_result of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622703688.458, "dur": 1.38, "args": {"Python parent id": 622, "Python id": 624, "Ev Idx": 623}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(508): generate_request", "pid": 1304242, "tid": 1304242, "ts": 6595622703698.038, "dur": 62.302, "args": {"Python parent id": 38, "Python id": 625, "Ev Idx": 624}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(806): _wait_one_response", "pid": 1304242, "tid": 1304242, "ts": 6595622703700.609, "dur": 56.606, "args": {"Python parent id": 625, "Python id": 626, "Ev Idx": 625}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(432): wait_for", "pid": 1304242, "tid": 1304242, "ts": 6595622703702.691, "dur": 25.653, "args": {"Python parent id": 626, "Python id": 627, "Ev Idx": 626}}, {"ph": "X", "cat": "python_function", "name": "<built-in method done of _asyncio.Task object at 0x7fa02f684c70>", "pid": 1304242, "tid": 1304242, "ts": 6595622703710.136, "dur": 0.386, "args": {"Python parent id": 627, "Python id": 628, "Ev Idx": 627}}, {"ph": "X", "cat": "python_function", "name": "<built-in method result of _asyncio.Task object at 0x7fa02f684c70>", "pid": 1304242, "tid": 1304242, "ts": 6595622703714.654, "dur": 0.193, "args": {"Python parent id": 627, "Python id": 629, "Ev Idx": 628}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(39): _remove", "pid": 1304242, "tid": 1304242, "ts": 6595622703731.666, "dur": 9.396, "args": {"Python parent id": 626, "Python id": 630, "Ev Idx": 629}}, {"ph": "X", "cat": "python_function", "name": "<built-in method discard of set object at 0x7fa126a5e180>", "pid": 1304242, "tid": 1304242, "ts": 6595622703739.565, "dur": 1.226, "args": {"Python parent id": 630, "Python id": 631, "Ev Idx": 630}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595622703750.487, "dur": 0.385, "args": {"Python parent id": 626, "Python id": 632, "Ev Idx": 631}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622703752.306, "dur": 0.18, "args": {"Python parent id": 626, "Python id": 633, "Ev Idx": 632}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595622703753.879, "dur": 0.354, "args": {"Python parent id": 626, "Python id": 634, "Ev Idx": 633}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595622703756.095, "dur": 0.195, "args": {"Python parent id": 626, "Python id": 635, "Ev Idx": 634}}, {"ph": "X", "cat": "python_function", "name": "weakref.py(134): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595622703809.691, "dur": 3.253, "args": {"Python parent id": 38, "Python id": 636, "Ev Idx": 635}}, {"ph": "X", "cat": "python_function", "name": "socket.py(488): _decref_socketios", "pid": 1304242, "tid": 1304242, "ts": 6595622703820.292, "dur": 4.355, "args": {"Python parent id": 38, "Python id": 637, "Ev Idx": 636}}, {"ph": "X", "cat": "python_function", "name": "socket.py(498): close", "pid": 1304242, "tid": 1304242, "ts": 6595622703845.988, "dur": 32.947, "args": {"Python parent id": 38, "Python id": 638, "Ev Idx": 637}}, {"ph": "X", "cat": "python_function", "name": "socket.py(494): _real_close", "pid": 1304242, "tid": 1304242, "ts": 6595622703850.032, "dur": 28.27, "args": {"Python parent id": 638, "Python id": 639, "Ev Idx": 638}}, {"ph": "X", "cat": "python_function", "name": "<built-in method close of socket object at 0x7fa02f6c7880>", "pid": 1304242, "tid": 1304242, "ts": 6595622703857.607, "dur": 19.958, "args": {"Python parent id": 639, "Python id": 640, "Ev Idx": 639}}, {"ph": "X", "cat": "python_function", "name": "socket.py(498): close", "pid": 1304242, "tid": 1304242, "ts": 6595622703881.486, "dur": 16.141, "args": {"Python parent id": 38, "Python id": 641, "Ev Idx": 640}}, {"ph": "X", "cat": "python_function", "name": "socket.py(494): _real_close", "pid": 1304242, "tid": 1304242, "ts": 6595622703883.01, "dur": 14.199, "args": {"Python parent id": 641, "Python id": 642, "Ev Idx": 641}}, {"ph": "X", "cat": "python_function", "name": "<built-in method close of socket object at 0x7fa02f6c7880>", "pid": 1304242, "tid": 1304242, "ts": 6595622703883.967, "dur": 12.971, "args": {"Python parent id": 642, "Python id": 643, "Ev Idx": 642}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(39): _remove", "pid": 1304242, "tid": 1304242, "ts": 6595622703917.697, "dur": 3.082, "args": {"Python parent id": 38, "Python id": 644, "Ev Idx": 643}}, {"ph": "X", "cat": "python_function", "name": "<built-in method discard of set object at 0x7fa126a5e180>", "pid": 1304242, "tid": 1304242, "ts": 6595622703920.098, "dur": 0.436, "args": {"Python parent id": 644, "Python id": 645, "Ev Idx": 644}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(509): generate_request", "pid": 1304242, "tid": 1304242, "ts": 6595622703927.948, "dur": 98.086, "args": {"Python parent id": 9, "Python id": 646, "Ev Idx": 645}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(39): _remove", "pid": 1304242, "tid": 1304242, "ts": 6595622703930.43, "dur": 1.633, "args": {"Python parent id": 646, "Python id": 647, "Ev Idx": 646}}, {"ph": "X", "cat": "python_function", "name": "<built-in method discard of set object at 0x7fa126a5e180>", "pid": 1304242, "tid": 1304242, "ts": 6595622703931.731, "dur": 0.157, "args": {"Python parent id": 647, "Python id": 648, "Ev Idx": 647}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(116): discard", "pid": 1304242, "tid": 1304242, "ts": 6595622703937.216, "dur": 5.443, "args": {"Python parent id": 646, "Python id": 649, "Ev Idx": 648}}, {"ph": "X", "cat": "python_function", "name": "<built-in method discard of set object at 0x7fa126a5e180>", "pid": 1304242, "tid": 1304242, "ts": 6595622703941.363, "dur": 0.577, "args": {"Python parent id": 649, "Python id": 650, "Ev Idx": 649}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/aio_rwlock.py(87): __aexit__", "pid": 1304242, "tid": 1304242, "ts": 6595622703962.967, "dur": 60.699, "args": {"Python parent id": 646, "Python id": 651, "Ev Idx": 650}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/aio_rwlock.py(51): release_reader", "pid": 1304242, "tid": 1304242, "ts": 6595622703985.748, "dur": 37.462, "args": {"Python parent id": 651, "Python id": 652, "Ev Idx": 651}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(13): __aenter__", "pid": 1304242, "tid": 1304242, "ts": 6595622703989.763, "dur": 5.304, "args": {"Python parent id": 652, "Python id": 653, "Ev Idx": 652}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(93): acquire", "pid": 1304242, "tid": 1304242, "ts": 6595622703991.765, "dur": 2.393, "args": {"Python parent id": 653, "Python id": 654, "Ev Idx": 653}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(324): notify_all", "pid": 1304242, "tid": 1304242, "ts": 6595622704001.523, "dur": 13.31, "args": {"Python parent id": 652, "Python id": 655, "Ev Idx": 654}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595622704005.996, "dur": 0.561, "args": {"Python parent id": 655, "Python id": 656, "Ev Idx": 655}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(300): notify", "pid": 1304242, "tid": 1304242, "ts": 6595622704008.865, "dur": 5.413, "args": {"Python parent id": 655, "Python id": 657, "Ev Idx": 656}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(89): locked", "pid": 1304242, "tid": 1304242, "ts": 6595622704011.832, "dur": 0.642, "args": {"Python parent id": 657, "Python id": 658, "Ev Idx": 657}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(19): __aexit__", "pid": 1304242, "tid": 1304242, "ts": 6595622704017.44, "dur": 5.12, "args": {"Python parent id": 652, "Python id": 659, "Ev Idx": 658}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(125): release", "pid": 1304242, "tid": 1304242, "ts": 6595622704019.007, "dur": 3.235, "args": {"Python parent id": 659, "Python id": 660, "Ev Idx": 659}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(142): _wake_up_first", "pid": 1304242, "tid": 1304242, "ts": 6595622704020.887, "dur": 0.902, "args": {"Python parent id": 660, "Python id": 661, "Ev Idx": 660}}, {"ph": "X", "cat": "python_function", "name": "<built-in function time>", "pid": 1304242, "tid": 1304242, "ts": 6595622704035.379, "dur": 0.679, "args": {"Python parent id": 9, "Python id": 662, "Ev Idx": 661}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595622704097.724, "dur": 0.343, "args": {"Python parent id": 9, "Python id": 663, "Ev Idx": 662}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of list object at 0x7fa02f714280>", "pid": 1304242, "tid": 1304242, "ts": 6595622704101.525, "dur": 0.443, "args": {"Python parent id": 9, "Python id": 664, "Ev Idx": 663}}, {"ph": "X", "cat": "python_function", "name": "<built-in function print>", "pid": 1304242, "tid": 1304242, "ts": 6595622704114.924, "dur": 57.496, "args": {"Python parent id": 9, "Python id": 665, "Ev Idx": 664}}, {"ph": "X", "cat": "python_function", "name": "<built-in function print>", "pid": 1304242, "tid": 1304242, "ts": 6595622704179.52, "dur": 15.569, "args": {"Python parent id": 9, "Python id": 666, "Ev Idx": 665}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595622704200.003, "dur": 0.466, "args": {"Python parent id": 9, "Python id": 667, "Ev Idx": 666}}, {"ph": "X", "cat": "python_function", "name": "<built-in function print>", "pid": 1304242, "tid": 1304242, "ts": 6595622704204.634, "dur": 16.411, "args": {"Python parent id": 9, "Python id": 668, "Ev Idx": 667}}, {"ph": "X", "cat": "python_function", "name": "operator_profiler.py(28): set_step", "pid": 1304242, "tid": 1304242, "ts": 6595622704226.761, "dur": 1.993, "args": {"Python parent id": 9, "Python id": 669, "Ev Idx": 668}}, {"ph": "X", "cat": "python_function", "name": "<built-in function time>", "pid": 1304242, "tid": 1304242, "ts": 6595622704229.999, "dur": 0.559, "args": {"Python parent id": 9, "Python id": 670, "Ev Idx": 669}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/entrypoints/engine.py(140): generate", "pid": 1304242, "tid": 1304242, "ts": 6595622704234.697, "dur": 245260.857, "args": {"Python parent id": 9, "Python id": 671, "Ev Idx": 670}}, {"ph": "X", "cat": "python_function", "name": "<string>(2): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622704252.175, "dur": 10.21, "args": {"Python parent id": 671, "Python id": 672, "Ev Idx": 671}}, {"ph": "X", "cat": "python_function", "name": "<built-in function get_event_loop>", "pid": 1304242, "tid": 1304242, "ts": 6595622704266.913, "dur": 12.398, "args": {"Python parent id": 671, "Python id": 673, "Ev Idx": 672}}, {"ph": "X", "cat": "python_function", "name": "asyncio/events.py(736): get_event_loop_policy", "pid": 1304242, "tid": 1304242, "ts": 6595622704269.691, "dur": 1.125, "args": {"Python parent id": 673, "Python id": 674, "Ev Idx": 673}}, {"ph": "X", "cat": "python_function", "name": "asyncio/events.py(645): get_event_loop", "pid": 1304242, "tid": 1304242, "ts": 6595622704272.563, "dur": 6.519, "args": {"Python parent id": 673, "Python id": 675, "Ev Idx": 674}}, {"ph": "X", "cat": "python_function", "name": "asyncio/base_futures.py(14): isfuture", "pid": 1304242, "tid": 1304242, "ts": 6595622704288.003, "dur": 6.529, "args": {"Python parent id": 671, "Python id": 676, "Ev Idx": 675}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595622704289.681, "dur": 4.27, "args": {"Python parent id": 676, "Python id": 677, "Ev Idx": 676}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(610): ensure_future", "pid": 1304242, "tid": 1304242, "ts": 6595622704297.92, "dur": 36.072, "args": {"Python parent id": 671, "Python id": 678, "Ev Idx": 677}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(618): _ensure_future", "pid": 1304242, "tid": 1304242, "ts": 6595622704299.928, "dur": 33.391, "args": {"Python parent id": 678, "Python id": 679, "Ev Idx": 678}}, {"ph": "X", "cat": "python_function", "name": "asyncio/base_futures.py(14): isfuture", "pid": 1304242, "tid": 1304242, "ts": 6595622704302.433, "dur": 2.125, "args": {"Python parent id": 679, "Python id": 680, "Ev Idx": 679}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595622704303.079, "dur": 1.331, "args": {"Python parent id": 680, "Python id": 681, "Ev Idx": 680}}, {"ph": "X", "cat": "python_function", "name": "asyncio/coroutines.py(177): iscoroutine", "pid": 1304242, "tid": 1304242, "ts": 6595622704306.979, "dur": 2.503, "args": {"Python parent id": 679, "Python id": 682, "Ev Idx": 681}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(86): add", "pid": 1304242, "tid": 1304242, "ts": 6595622704326.35, "dur": 5.29, "args": {"Python parent id": 679, "Python id": 683, "Ev Idx": 682}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add of set object at 0x7fa126a5dd20>", "pid": 1304242, "tid": 1304242, "ts": 6595622704330.464, "dur": 0.712, "args": {"Python parent id": 683, "Python id": 684, "Ev Idx": 683}}, {"ph": "X", "cat": "python_function", "name": "socket.py(594): socketpair", "pid": 1304242, "tid": 1304242, "ts": 6595622704353.318, "dur": 66.731, "args": {"Python parent id": 671, "Python id": 685, "Ev Idx": 684}}, {"ph": "X", "cat": "python_function", "name": "<built-in function socketpair>", "pid": 1304242, "tid": 1304242, "ts": 6595622704357.127, "dur": 32.733, "args": {"Python parent id": 685, "Python id": 686, "Ev Idx": 685}}, {"ph": "X", "cat": "python_function", "name": "<built-in method detach of _socket.socket object at 0x7fa02f700930>", "pid": 1304242, "tid": 1304242, "ts": 6595622704391.977, "dur": 0.384, "args": {"Python parent id": 685, "Python id": 687, "Ev Idx": 686}}, {"ph": "X", "cat": "python_function", "name": "socket.py(220): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622704395.037, "dur": 16.221, "args": {"Python parent id": 685, "Python id": 688, "Ev Idx": 687}}, {"ph": "X", "cat": "python_function", "name": "<built-in method detach of _socket.socket object at 0x7fa02f700930>", "pid": 1304242, "tid": 1304242, "ts": 6595622704413.649, "dur": 0.18, "args": {"Python parent id": 685, "Python id": 689, "Ev Idx": 688}}, {"ph": "X", "cat": "python_function", "name": "socket.py(220): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622704414.774, "dur": 4.199, "args": {"Python parent id": 685, "Python id": 690, "Ev Idx": 689}}, {"ph": "X", "cat": "python_function", "name": "weakref.py(134): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595622704444.447, "dur": 2.776, "args": {"Python parent id": 671, "Python id": 691, "Ev Idx": 690}}, {"ph": "X", "cat": "python_function", "name": "asyncio/coroutines.py(177): iscoroutine", "pid": 1304242, "tid": 1304242, "ts": 6595622704488.28, "dur": 37.123, "args": {"Python parent id": 671, "Python id": 692, "Ev Idx": 691}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622704490.608, "dur": 30.641, "args": {"Python parent id": 692, "Python id": 693, "Ev Idx": 692}}, {"ph": "X", "cat": "python_function", "name": "abc.py(117): __instancecheck__", "pid": 1304242, "tid": 1304242, "ts": 6595622704495.651, "dur": 24.773, "args": {"Python parent id": 693, "Python id": 694, "Ev Idx": 693}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _abc_instancecheck>", "pid": 1304242, "tid": 1304242, "ts": 6595622704496.818, "dur": 23.468, "args": {"Python parent id": 694, "Python id": 695, "Ev Idx": 694}}, {"ph": "X", "cat": "python_function", "name": "abc.py(121): __subclasscheck__", "pid": 1304242, "tid": 1304242, "ts": 6595622704501.68, "dur": 18.092, "args": {"Python parent id": 695, "Python id": 696, "Ev Idx": 695}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _abc_subclasscheck>", "pid": 1304242, "tid": 1304242, "ts": 6595622704502.474, "dur": 17.071, "args": {"Python parent id": 696, "Python id": 697, "Ev Idx": 696}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(156): __subclasshook__", "pid": 1304242, "tid": 1304242, "ts": 6595622704504.919, "dur": 12.761, "args": {"Python parent id": 697, "Python id": 698, "Ev Idx": 697}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(78): _check_methods", "pid": 1304242, "tid": 1304242, "ts": 6595622704507.743, "dur": 9.428, "args": {"Python parent id": 698, "Python id": 699, "Ev Idx": 698}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595622704522.411, "dur": 0.454, "args": {"Python parent id": 692, "Python id": 700, "Ev Idx": 699}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add of set object at 0x7fa126a5dd20>", "pid": 1304242, "tid": 1304242, "ts": 6595622704524.714, "dur": 0.353, "args": {"Python parent id": 692, "Python id": 701, "Ev Idx": 700}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(86): add", "pid": 1304242, "tid": 1304242, "ts": 6595622704533.565, "dur": 2.82, "args": {"Python parent id": 671, "Python id": 702, "Ev Idx": 701}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add of set object at 0x7fa126a5dd20>", "pid": 1304242, "tid": 1304242, "ts": 6595622704535.7, "dur": 0.436, "args": {"Python parent id": 702, "Python id": 703, "Ev Idx": 702}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(482): generate_request", "pid": 1304242, "tid": 1304242, "ts": 6595622704542.867, "dur": 1114.575, "args": {"Python parent id": 671, "Python id": 704, "Ev Idx": 703}}, {"ph": "X", "cat": "python_function", "name": "<built-in function time>", "pid": 1304242, "tid": 1304242, "ts": 6595622704544.988, "dur": 0.59, "args": {"Python parent id": 704, "Python id": 705, "Ev Idx": 704}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(1426): auto_create_handle_loop", "pid": 1304242, "tid": 1304242, "ts": 6595622704547.558, "dur": 1.098, "args": {"Python parent id": 704, "Python id": 706, "Ev Idx": 705}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/io_struct.py(138): normalize_batch_and_arguments", "pid": 1304242, "tid": 1304242, "ts": 6595622704551.026, "dur": 74.272, "args": {"Python parent id": 704, "Python id": 707, "Ev Idx": 706}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/io_struct.py(160): _validate_inputs", "pid": 1304242, "tid": 1304242, "ts": 6595622704552.449, "dur": 2.322, "args": {"Python parent id": 707, "Python id": 708, "Ev Idx": 707}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/io_struct.py(173): _determine_batch_size", "pid": 1304242, "tid": 1304242, "ts": 6595622704556.109, "dur": 5.518, "args": {"Python parent id": 707, "Python id": 709, "Ev Idx": 708}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622704558.564, "dur": 0.354, "args": {"Python parent id": 709, "Python id": 710, "Ev Idx": 709}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/io_struct.py(201): _handle_parallel_sampling", "pid": 1304242, "tid": 1304242, "ts": 6595622704562.851, "dur": 5.306, "args": {"Python parent id": 707, "Python id": 711, "Ev Idx": 710}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622704564.601, "dur": 0.117, "args": {"Python parent id": 711, "Python id": 712, "Ev Idx": 711}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595622704565.749, "dur": 0.409, "args": {"Python parent id": 711, "Python id": 713, "Ev Idx": 712}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/io_struct.py(227): _normalize_single_inputs", "pid": 1304242, "tid": 1304242, "ts": 6595622704569.443, "dur": 55.148, "args": {"Python parent id": 707, "Python id": 714, "Ev Idx": 713}}, {"ph": "X", "cat": "python_function", "name": "uuid.py(718): uuid4", "pid": 1304242, "tid": 1304242, "ts": 6595622704572.487, "dur": 39.8, "args": {"Python parent id": 714, "Python id": 715, "Ev Idx": 714}}, {"ph": "X", "cat": "python_function", "name": "<built-in function urandom>", "pid": 1304242, "tid": 1304242, "ts": 6595622704575.085, "dur": 6.115, "args": {"Python parent id": 715, "Python id": 716, "Ev Idx": 715}}, {"ph": "X", "cat": "python_function", "name": "uuid.py(138): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622704585.916, "dur": 24.911, "args": {"Python parent id": 715, "Python id": 717, "Ev Idx": 716}}, {"ph": "X", "cat": "python_function", "name": "<built-in method count of list object at 0x7fa02f735100>", "pid": 1304242, "tid": 1304242, "ts": 6595622704587.743, "dur": 1.767, "args": {"Python parent id": 717, "Python id": 718, "Ev Idx": 717}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595622704591.61, "dur": 0.551, "args": {"Python parent id": 717, "Python id": 719, "Ev Idx": 718}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622704593.506, "dur": 0.226, "args": {"Python parent id": 717, "Python id": 720, "Ev Idx": 719}}, {"ph": "X", "cat": "python_function", "name": "<built-in method from_bytes of type object at 0x57ea91926320>", "pid": 1304242, "tid": 1304242, "ts": 6595622704596.421, "dur": 2.512, "args": {"Python parent id": 717, "Python id": 721, "Ev Idx": 720}}, {"ph": "X", "cat": "python_function", "name": "uuid.py(333): hex", "pid": 1304242, "tid": 1304242, "ts": 6595622704614.333, "dur": 6.34, "args": {"Python parent id": 714, "Python id": 722, "Ev Idx": 721}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(13): __aenter__", "pid": 1304242, "tid": 1304242, "ts": 6595622704629.763, "dur": 5.278, "args": {"Python parent id": 704, "Python id": 723, "Ev Idx": 722}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(93): acquire", "pid": 1304242, "tid": 1304242, "ts": 6595622704632.108, "dur": 1.957, "args": {"Python parent id": 723, "Python id": 724, "Ev Idx": 723}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(287): wait_for", "pid": 1304242, "tid": 1304242, "ts": 6595622704638.917, "dur": 3.041, "args": {"Python parent id": 704, "Python id": 725, "Ev Idx": 724}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(498): <lambda>", "pid": 1304242, "tid": 1304242, "ts": 6595622704639.876, "dur": 1.343, "args": {"Python parent id": 725, "Python id": 726, "Ev Idx": 725}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(19): __aexit__", "pid": 1304242, "tid": 1304242, "ts": 6595622704644.438, "dur": 4.656, "args": {"Python parent id": 704, "Python id": 727, "Ev Idx": 726}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(125): release", "pid": 1304242, "tid": 1304242, "ts": 6595622704645.663, "dur": 2.939, "args": {"Python parent id": 727, "Python id": 728, "Ev Idx": 727}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(142): _wake_up_first", "pid": 1304242, "tid": 1304242, "ts": 6595622704647.612, "dur": 0.587, "args": {"Python parent id": 728, "Python id": 729, "Ev Idx": 728}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/aio_rwlock.py(21): reader_lock", "pid": 1304242, "tid": 1304242, "ts": 6595622704651.792, "dur": 4.058, "args": {"Python parent id": 704, "Python id": 730, "Ev Idx": 729}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/aio_rwlock.py(80): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622704653.98, "dur": 1.329, "args": {"Python parent id": 730, "Python id": 731, "Ev Idx": 730}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/aio_rwlock.py(83): __aenter__", "pid": 1304242, "tid": 1304242, "ts": 6595622704657.323, "dur": 13.329, "args": {"Python parent id": 704, "Python id": 732, "Ev Idx": 731}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/aio_rwlock.py(43): acquire_reader", "pid": 1304242, "tid": 1304242, "ts": 6595622704659.167, "dur": 10.974, "args": {"Python parent id": 732, "Python id": 733, "Ev Idx": 732}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(13): __aenter__", "pid": 1304242, "tid": 1304242, "ts": 6595622704660.545, "dur": 2.366, "args": {"Python parent id": 733, "Python id": 734, "Ev Idx": 733}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(93): acquire", "pid": 1304242, "tid": 1304242, "ts": 6595622704661.644, "dur": 0.906, "args": {"Python parent id": 734, "Python id": 735, "Ev Idx": 734}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(19): __aexit__", "pid": 1304242, "tid": 1304242, "ts": 6595622704666.808, "dur": 2.869, "args": {"Python parent id": 733, "Python id": 736, "Ev Idx": 735}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(125): release", "pid": 1304242, "tid": 1304242, "ts": 6595622704667.825, "dur": 1.546, "args": {"Python parent id": 736, "Python id": 737, "Ev Idx": 736}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(142): _wake_up_first", "pid": 1304242, "tid": 1304242, "ts": 6595622704668.71, "dur": 0.351, "args": {"Python parent id": 737, "Python id": 738, "Ev Idx": 737}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(516): _tokenize_one_request", "pid": 1304242, "tid": 1304242, "ts": 6595622704674.761, "dur": 622.319, "args": {"Python parent id": 704, "Python id": 739, "Ev Idx": 738}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622704677.385, "dur": 0.702, "args": {"Python parent id": 739, "Python id": 740, "Ev Idx": 739}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(2827): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622704683.519, "dur": 513.624, "args": {"Python parent id": 739, "Python id": 741, "Ev Idx": 740}}, {"ph": "X", "cat": "python_function", "name": "<built-in method pop of dict object at 0x7fa02f6ad340>", "pid": 1304242, "tid": 1304242, "ts": 6595622704693.972, "dur": 0.341, "args": {"Python parent id": 741, "Python id": 742, "Ev Idx": 741}}, {"ph": "X", "cat": "python_function", "name": "<built-in method update of dict object at 0x7fa02f737540>", "pid": 1304242, "tid": 1304242, "ts": 6595622704697.841, "dur": 1.011, "args": {"Python parent id": 741, "Python id": 743, "Ev Idx": 742}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(3988): _switch_to_input_mode", "pid": 1304242, "tid": 1304242, "ts": 6595622704701.63, "dur": 0.73, "args": {"Python parent id": 741, "Python id": 744, "Ev Idx": 743}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(2925): _call_one", "pid": 1304242, "tid": 1304242, "ts": 6595622704711.809, "dur": 480.528, "args": {"Python parent id": 741, "Python id": 745, "Ev Idx": 744}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(2949): _is_valid_text_input", "pid": 1304242, "tid": 1304242, "ts": 6595622704714.032, "dur": 1.869, "args": {"Python parent id": 745, "Python id": 746, "Ev Idx": 745}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622704715.343, "dur": 0.132, "args": {"Python parent id": 746, "Python id": 747, "Ev Idx": 746}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622704717.854, "dur": 0.699, "args": {"Python parent id": 745, "Python id": 748, "Ev Idx": 747}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(3043): encode_plus", "pid": 1304242, "tid": 1304242, "ts": 6595622704729.797, "dur": 460.089, "args": {"Python parent id": 745, "Python id": 749, "Ev Idx": 748}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(2722): _get_padding_truncation_strategies", "pid": 1304242, "tid": 1304242, "ts": 6595622704734.979, "dur": 10.154, "args": {"Python parent id": 749, "Python id": 750, "Ev Idx": 749}}, {"ph": "X", "cat": "python_function", "name": "<built-in method pop of dict object at 0x7fa02f6ad340>", "pid": 1304242, "tid": 1304242, "ts": 6595622704755.887, "dur": 0.545, "args": {"Python parent id": 749, "Python id": 751, "Ev Idx": 750}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(603): _encode_plus", "pid": 1304242, "tid": 1304242, "ts": 6595622704760.118, "dur": 427.618, "args": {"Python parent id": 749, "Python id": 752, "Ev Idx": 751}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(512): _batch_encode_plus", "pid": 1304242, "tid": 1304242, "ts": 6595622704770.267, "dur": 320.467, "args": {"Python parent id": 752, "Python id": 753, "Ev Idx": 752}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622704771.929, "dur": 0.443, "args": {"Python parent id": 753, "Python id": 754, "Ev Idx": 753}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(437): set_truncation_and_padding", "pid": 1304242, "tid": 1304242, "ts": 6595622704775.45, "dur": 10.128, "args": {"Python parent id": 753, "Python id": 755, "Ev Idx": 754}}, {"ph": "X", "cat": "python_function", "name": "<built-in method encode_batch of tokenizers.Tokenizer object at 0x57eac2f350b0>", "pid": 1304242, "tid": 1304242, "ts": 6595622704790.208, "dur": 204.688, "args": {"Python parent id": 753, "Python id": 756, "Ev Idx": 755}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(565): <listcomp>", "pid": 1304242, "tid": 1304242, "ts": 6595622704999.192, "dur": 26.277, "args": {"Python parent id": 753, "Python id": 757, "Ev Idx": 756}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(307): _convert_encoding", "pid": 1304242, "tid": 1304242, "ts": 6595622705003.156, "dur": 20.769, "args": {"Python parent id": 757, "Python id": 758, "Ev Idx": 757}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of list object at 0x7fa02f714280>", "pid": 1304242, "tid": 1304242, "ts": 6595622705018.222, "dur": 0.514, "args": {"Python parent id": 758, "Python id": 759, "Ev Idx": 758}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of list object at 0x7fa02f714280>", "pid": 1304242, "tid": 1304242, "ts": 6595622705022.324, "dur": 0.158, "args": {"Python parent id": 758, "Python id": 760, "Ev Idx": 759}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(587): <listcomp>", "pid": 1304242, "tid": 1304242, "ts": 6595622705030.446, "dur": 2.055, "args": {"Python parent id": 753, "Python id": 761, "Ev Idx": 760}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(587): <listcomp>", "pid": 1304242, "tid": 1304242, "ts": 6595622705034.894, "dur": 0.973, "args": {"Python parent id": 753, "Python id": 762, "Ev Idx": 761}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(589): <listcomp>", "pid": 1304242, "tid": 1304242, "ts": 6595622705038.588, "dur": 0.814, "args": {"Python parent id": 753, "Python id": 763, "Ev Idx": 762}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(3968): _eventual_warn_about_too_long_sequence", "pid": 1304242, "tid": 1304242, "ts": 6595622705042.77, "dur": 2.849, "args": {"Python parent id": 753, "Python id": 764, "Ev Idx": 763}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595622705044.013, "dur": 0.434, "args": {"Python parent id": 764, "Python id": 765, "Ev Idx": 764}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(220): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622705051.04, "dur": 38.16, "args": {"Python parent id": 753, "Python id": 766, "Ev Idx": 765}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1091): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622705057.237, "dur": 21.435, "args": {"Python parent id": 766, "Python id": 767, "Ev Idx": 766}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(991): update", "pid": 1304242, "tid": 1304242, "ts": 6595622705060.663, "dur": 16.464, "args": {"Python parent id": 767, "Python id": 768, "Ev Idx": 767}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622705062.224, "dur": 5.422, "args": {"Python parent id": 768, "Python id": 769, "Ev Idx": 768}}, {"ph": "X", "cat": "python_function", "name": "abc.py(117): __instancecheck__", "pid": 1304242, "tid": 1304242, "ts": 6595622705063.677, "dur": 3.499, "args": {"Python parent id": 769, "Python id": 770, "Ev Idx": 769}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _abc_instancecheck>", "pid": 1304242, "tid": 1304242, "ts": 6595622705064.329, "dur": 2.515, "args": {"Python parent id": 770, "Python id": 771, "Ev Idx": 770}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1108): __setitem__", "pid": 1304242, "tid": 1304242, "ts": 6595622705070.235, "dur": 1.028, "args": {"Python parent id": 768, "Python id": 772, "Ev Idx": 771}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1108): __setitem__", "pid": 1304242, "tid": 1304242, "ts": 6595622705072.363, "dur": 0.664, "args": {"Python parent id": 768, "Python id": 773, "Ev Idx": 772}}, {"ph": "X", "cat": "python_function", "name": "<built-in method items of dict object at 0x7fa02f736d40>", "pid": 1304242, "tid": 1304242, "ts": 6595622705074.59, "dur": 0.705, "args": {"Python parent id": 768, "Python id": 774, "Ev Idx": 773}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622705080.396, "dur": 0.366, "args": {"Python parent id": 766, "Python id": 775, "Ev Idx": 774}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(689): convert_to_tensors", "pid": 1304242, "tid": 1304242, "ts": 6595622705086.702, "dur": 1.088, "args": {"Python parent id": 766, "Python id": 776, "Ev Idx": 775}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(840): items", "pid": 1304242, "tid": 1304242, "ts": 6595622705098.301, "dur": 4.126, "args": {"Python parent id": 752, "Python id": 777, "Ev Idx": 776}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(862): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622705100.575, "dur": 1.383, "args": {"Python parent id": 777, "Python id": 778, "Ev Idx": 777}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(653): <dictcomp>", "pid": 1304242, "tid": 1304242, "ts": 6595622705105.194, "dur": 23.149, "args": {"Python parent id": 752, "Python id": 779, "Ev Idx": 778}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(909): __iter__", "pid": 1304242, "tid": 1304242, "ts": 6595622705106.223, "dur": 10.355, "args": {"Python parent id": 779, "Python id": 780, "Ev Idx": 779}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1114): __iter__", "pid": 1304242, "tid": 1304242, "ts": 6595622705108.39, "dur": 2.22, "args": {"Python parent id": 780, "Python id": 781, "Ev Idx": 780}}, {"ph": "X", "cat": "python_function", "name": "<built-in function iter>", "pid": 1304242, "tid": 1304242, "ts": 6595622705109.771, "dur": 0.584, "args": {"Python parent id": 781, "Python id": 782, "Ev Idx": 781}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(259): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595622705113.36, "dur": 2.345, "args": {"Python parent id": 780, "Python id": 783, "Ev Idx": 782}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622705114.568, "dur": 0.19, "args": {"Python parent id": 783, "Python id": 784, "Ev Idx": 783}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595622705117.747, "dur": 0.217, "args": {"Python parent id": 779, "Python id": 785, "Ev Idx": 784}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622705119.552, "dur": 0.268, "args": {"Python parent id": 779, "Python id": 786, "Ev Idx": 785}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(911): __iter__", "pid": 1304242, "tid": 1304242, "ts": 6595622705121.413, "dur": 2.457, "args": {"Python parent id": 779, "Python id": 787, "Ev Idx": 786}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(259): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595622705122.512, "dur": 0.962, "args": {"Python parent id": 787, "Python id": 788, "Ev Idx": 787}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622705123.036, "dur": 0.107, "args": {"Python parent id": 788, "Python id": 789, "Ev Idx": 788}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595622705124.7, "dur": 0.184, "args": {"Python parent id": 779, "Python id": 790, "Ev Idx": 789}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622705125.365, "dur": 0.101, "args": {"Python parent id": 779, "Python id": 791, "Ev Idx": 790}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(911): __iter__", "pid": 1304242, "tid": 1304242, "ts": 6595622705126.376, "dur": 0.53, "args": {"Python parent id": 779, "Python id": 792, "Ev Idx": 791}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(301): encodings", "pid": 1304242, "tid": 1304242, "ts": 6595622705130.818, "dur": 26.774, "args": {"Python parent id": 752, "Python id": 793, "Ev Idx": 792}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(220): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622705159.875, "dur": 18.238, "args": {"Python parent id": 752, "Python id": 794, "Ev Idx": 793}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1091): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622705162.466, "dur": 10.688, "args": {"Python parent id": 794, "Python id": 795, "Ev Idx": 794}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(991): update", "pid": 1304242, "tid": 1304242, "ts": 6595622705163.85, "dur": 8.38, "args": {"Python parent id": 795, "Python id": 796, "Ev Idx": 795}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622705164.638, "dur": 2.469, "args": {"Python parent id": 796, "Python id": 797, "Ev Idx": 796}}, {"ph": "X", "cat": "python_function", "name": "abc.py(117): __instancecheck__", "pid": 1304242, "tid": 1304242, "ts": 6595622705165.383, "dur": 1.471, "args": {"Python parent id": 797, "Python id": 798, "Ev Idx": 797}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _abc_instancecheck>", "pid": 1304242, "tid": 1304242, "ts": 6595622705165.832, "dur": 0.808, "args": {"Python parent id": 798, "Python id": 799, "Ev Idx": 798}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1108): __setitem__", "pid": 1304242, "tid": 1304242, "ts": 6595622705168.307, "dur": 0.629, "args": {"Python parent id": 796, "Python id": 800, "Ev Idx": 799}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1108): __setitem__", "pid": 1304242, "tid": 1304242, "ts": 6595622705169.848, "dur": 0.489, "args": {"Python parent id": 796, "Python id": 801, "Ev Idx": 800}}, {"ph": "X", "cat": "python_function", "name": "<built-in method items of dict object at 0x7fa02f736d40>", "pid": 1304242, "tid": 1304242, "ts": 6595622705171.171, "dur": 0.323, "args": {"Python parent id": 796, "Python id": 802, "Ev Idx": 801}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622705173.988, "dur": 0.269, "args": {"Python parent id": 794, "Python id": 803, "Ev Idx": 802}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(689): convert_to_tensors", "pid": 1304242, "tid": 1304242, "ts": 6595622705176.996, "dur": 0.335, "args": {"Python parent id": 794, "Python id": 804, "Ev Idx": 803}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(259): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595622705182.471, "dur": 1.155, "args": {"Python parent id": 752, "Python id": 805, "Ev Idx": 804}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622705183.185, "dur": 0.117, "args": {"Python parent id": 805, "Python id": 806, "Ev Idx": 805}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(3968): _eventual_warn_about_too_long_sequence", "pid": 1304242, "tid": 1304242, "ts": 6595622705185.103, "dur": 1.538, "args": {"Python parent id": 752, "Python id": 807, "Ev Idx": 806}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595622705185.688, "dur": 0.271, "args": {"Python parent id": 807, "Python id": 808, "Ev Idx": 807}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(3988): _switch_to_input_mode", "pid": 1304242, "tid": 1304242, "ts": 6595622705196.204, "dur": 0.246, "args": {"Python parent id": 741, "Python id": 809, "Ev Idx": 808}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(259): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595622705199.982, "dur": 1.22, "args": {"Python parent id": 739, "Python id": 810, "Ev Idx": 809}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622705200.777, "dur": 0.115, "args": {"Python parent id": 810, "Python id": 811, "Ev Idx": 810}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(577): _validate_one_request", "pid": 1304242, "tid": 1304242, "ts": 6595622705204.283, "dur": 9.68, "args": {"Python parent id": 739, "Python id": 812, "Ev Idx": 811}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595622705205.996, "dur": 0.209, "args": {"Python parent id": 812, "Python id": 813, "Ev Idx": 812}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622705208.272, "dur": 0.286, "args": {"Python parent id": 812, "Python id": 814, "Ev Idx": 813}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595622705209.733, "dur": 0.481, "args": {"Python parent id": 812, "Python id": 815, "Ev Idx": 814}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622705211.918, "dur": 0.23, "args": {"Python parent id": 812, "Python id": 816, "Ev Idx": 815}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(658): _create_tokenized_object", "pid": 1304242, "tid": 1304242, "ts": 6595622705216.589, "dur": 79.216, "args": {"Python parent id": 739, "Python id": 817, "Ev Idx": 816}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(31): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622705223.134, "dur": 18.48, "args": {"Python parent id": 817, "Python id": 818, "Ev Idx": 817}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(149): normalize", "pid": 1304242, "tid": 1304242, "ts": 6595622705244.587, "dur": 2.517, "args": {"Python parent id": 817, "Python id": 819, "Ev Idx": 818}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(92): verify", "pid": 1304242, "tid": 1304242, "ts": 6595622705250.189, "dur": 19.408, "args": {"Python parent id": 817, "Python id": 820, "Ev Idx": 819}}, {"ph": "X", "cat": "python_function", "name": "<built-in function sum>", "pid": 1304242, "tid": 1304242, "ts": 6595622705260.541, "dur": 8.307, "args": {"Python parent id": 820, "Python id": 821, "Ev Idx": 820}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(146): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595622705262.698, "dur": 0.885, "args": {"Python parent id": 821, "Python id": 822, "Ev Idx": 821}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(146): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595622705264.855, "dur": 0.63, "args": {"Python parent id": 821, "Python id": 823, "Ev Idx": 822}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(146): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595622705266.422, "dur": 0.438, "args": {"Python parent id": 821, "Python id": 824, "Ev Idx": 823}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(146): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595622705267.696, "dur": 0.533, "args": {"Python parent id": 821, "Python id": 825, "Ev Idx": 824}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622705271.066, "dur": 0.161, "args": {"Python parent id": 817, "Python id": 826, "Ev Idx": 825}}, {"ph": "X", "cat": "python_function", "name": "<string>(2): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622705282.783, "dur": 10.857, "args": {"Python parent id": 817, "Python id": 827, "Ev Idx": 826}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(762): _send_one_request", "pid": 1304242, "tid": 1304242, "ts": 6595622705305.191, "dur": 244.569, "args": {"Python parent id": 704, "Python id": 828, "Ev Idx": 827}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/socket.py(933): send_pyobj", "pid": 1304242, "tid": 1304242, "ts": 6595622705308.902, "dur": 193.395, "args": {"Python parent id": 828, "Python id": 829, "Ev Idx": 828}}, {"ph": "X", "cat": "python_function", "name": "<built-in function dumps>", "pid": 1304242, "tid": 1304242, "ts": 6595622705311.734, "dur": 78.268, "args": {"Python parent id": 829, "Python id": 830, "Ev Idx": 829}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(317): send", "pid": 1304242, "tid": 1304242, "ts": 6595622705394.17, "dur": 106.669, "args": {"Python parent id": 829, "Python id": 831, "Ev Idx": 830}}, {"ph": "X", "cat": "python_function", "name": "<built-in method update of dict object at 0x7fa02f737540>", "pid": 1304242, "tid": 1304242, "ts": 6595622705398.935, "dur": 1.555, "args": {"Python parent id": 831, "Python id": 832, "Ev Idx": 831}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(525): _add_send_event", "pid": 1304242, "tid": 1304242, "ts": 6595622705403.835, "dur": 95.486, "args": {"Python parent id": 831, "Python id": 833, "Ev Idx": 832}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595622705413.831, "dur": 0.321, "args": {"Python parent id": 833, "Python id": 834, "Ev Idx": 833}}, {"ph": "X", "cat": "python_function", "name": "<built-in method copy of dict object at 0x7fa02f714bc0>", "pid": 1304242, "tid": 1304242, "ts": 6595622705415.297, "dur": 1.08, "args": {"Python parent id": 833, "Python id": 835, "Ev Idx": 834}}, {"ph": "X", "cat": "python_function", "name": "enum.py(980): __or__", "pid": 1304242, "tid": 1304242, "ts": 6595622705420.735, "dur": 20.867, "args": {"Python parent id": 833, "Python id": 836, "Ev Idx": 835}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622705422.757, "dur": 1.632, "args": {"Python parent id": 836, "Python id": 837, "Ev Idx": 836}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622705429.112, "dur": 6.653, "args": {"Python parent id": 836, "Python id": 838, "Ev Idx": 837}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622705432.065, "dur": 3.199, "args": {"Python parent id": 838, "Python id": 839, "Ev Idx": 838}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622705438.529, "dur": 2.624, "args": {"Python parent id": 836, "Python id": 840, "Ev Idx": 839}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622705439.645, "dur": 1.247, "args": {"Python parent id": 840, "Python id": 841, "Ev Idx": 840}}, {"ph": "X", "cat": "python_function", "name": "<built-in function getattr>", "pid": 1304242, "tid": 1304242, "ts": 6595622705443.679, "dur": 3.39, "args": {"Python parent id": 833, "Python id": 842, "Ev Idx": 841}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/socket.py(623): send", "pid": 1304242, "tid": 1304242, "ts": 6595622705450.19, "dur": 42.38, "args": {"Python parent id": 833, "Python id": 843, "Ev Idx": 842}}, {"ph": "X", "cat": "python_function", "name": "<built-in method set_result of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622705496.913, "dur": 0.486, "args": {"Python parent id": 833, "Python id": 844, "Ev Idx": 843}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(167): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622705509.428, "dur": 11.985, "args": {"Python parent id": 828, "Python id": 845, "Ev Idx": 844}}, {"ph": "X", "cat": "python_function", "name": "asyncio/mixins.py(15): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622705514.228, "dur": 1.867, "args": {"Python parent id": 845, "Python id": 846, "Ev Idx": 845}}, {"ph": "X", "cat": "python_function", "name": "<string>(2): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622705526.984, "dur": 18.278, "args": {"Python parent id": 828, "Python id": 847, "Ev Idx": 846}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(86): add", "pid": 1304242, "tid": 1304242, "ts": 6595622705558.02, "dur": 5.992, "args": {"Python parent id": 704, "Python id": 848, "Ev Idx": 847}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add of set object at 0x7fa126a5dd20>", "pid": 1304242, "tid": 1304242, "ts": 6595622705562.741, "dur": 0.752, "args": {"Python parent id": 848, "Python id": 849, "Ev Idx": 848}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(797): _wait_one_response", "pid": 1304242, "tid": 1304242, "ts": 6595622705566.956, "dur": 89.466, "args": {"Python parent id": 704, "Python id": 850, "Ev Idx": 849}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(392): wait_for", "pid": 1304242, "tid": 1304242, "ts": 6595622705572.274, "dur": 83.632, "args": {"Python parent id": 850, "Python id": 851, "Ev Idx": 850}}, {"ph": "X", "cat": "python_function", "name": "<built-in function get_running_loop>", "pid": 1304242, "tid": 1304242, "ts": 6595622705574.883, "dur": 3.007, "args": {"Python parent id": 851, "Python id": 852, "Ev Idx": 851}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(610): ensure_future", "pid": 1304242, "tid": 1304242, "ts": 6595622705613.852, "dur": 38.128, "args": {"Python parent id": 851, "Python id": 853, "Ev Idx": 852}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(618): _ensure_future", "pid": 1304242, "tid": 1304242, "ts": 6595622705615.576, "dur": 35.988, "args": {"Python parent id": 853, "Python id": 854, "Ev Idx": 853}}, {"ph": "X", "cat": "python_function", "name": "asyncio/base_futures.py(14): isfuture", "pid": 1304242, "tid": 1304242, "ts": 6595622705617.727, "dur": 8.861, "args": {"Python parent id": 854, "Python id": 855, "Ev Idx": 854}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595622705620.29, "dur": 5.776, "args": {"Python parent id": 855, "Python id": 856, "Ev Idx": 855}}, {"ph": "X", "cat": "python_function", "name": "asyncio/coroutines.py(177): iscoroutine", "pid": 1304242, "tid": 1304242, "ts": 6595622705629.449, "dur": 1.977, "args": {"Python parent id": 854, "Python id": 857, "Ev Idx": 856}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(86): add", "pid": 1304242, "tid": 1304242, "ts": 6595622705646.449, "dur": 3.832, "args": {"Python parent id": 854, "Python id": 858, "Ev Idx": 857}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add of set object at 0x7fa126a5dd20>", "pid": 1304242, "tid": 1304242, "ts": 6595622705648.664, "dur": 1.263, "args": {"Python parent id": 858, "Python id": 859, "Ev Idx": 858}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add_done_callback of _asyncio.Task object at 0x7fa02f684c70>", "pid": 1304242, "tid": 1304242, "ts": 6595622705653.503, "dur": 0.687, "args": {"Python parent id": 851, "Python id": 860, "Ev Idx": 859}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(862): _wait_one_response", "pid": 1304242, "tid": 1304242, "ts": 6595622705698.398, "dur": 1.022, "args": {"Python parent id": 671, "Python id": 861, "Ev Idx": 860}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(39): _remove", "pid": 1304242, "tid": 1304242, "ts": 6595622705710.025, "dur": 3.913, "args": {"Python parent id": 671, "Python id": 862, "Ev Idx": 861}}, {"ph": "X", "cat": "python_function", "name": "<built-in method discard of set object at 0x7fa126a5e180>", "pid": 1304242, "tid": 1304242, "ts": 6595622705713.027, "dur": 0.684, "args": {"Python parent id": 862, "Python id": 863, "Ev Idx": 862}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(201): wait", "pid": 1304242, "tid": 1304242, "ts": 6595622705718.186, "dur": 17.349, "args": {"Python parent id": 671, "Python id": 864, "Ev Idx": 863}}, {"ph": "X", "cat": "python_function", "name": "asyncio/mixins.py(22): _get_loop", "pid": 1304242, "tid": 1304242, "ts": 6595622705720.044, "dur": 10.504, "args": {"Python parent id": 864, "Python id": 865, "Ev Idx": 864}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _get_running_loop>", "pid": 1304242, "tid": 1304242, "ts": 6595622705721.527, "dur": 1.616, "args": {"Python parent id": 865, "Python id": 866, "Ev Idx": 865}}, {"ph": "X", "cat": "python_function", "name": "<built-in method __exit__ of _thread.lock object at 0x7fa1262bc680>", "pid": 1304242, "tid": 1304242, "ts": 6595622705728.529, "dur": 0.883, "args": {"Python parent id": 865, "Python id": 867, "Ev Idx": 866}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of collections.deque object at 0x7fa02f673580>", "pid": 1304242, "tid": 1304242, "ts": 6595622705734.292, "dur": 0.203, "args": {"Python parent id": 864, "Python id": 868, "Ev Idx": 867}}, {"ph": "X", "cat": "python_function", "name": "zmq/asyncio.py(151): <lambda>", "pid": 1304242, "tid": 1304242, "ts": 6595622948490.833, "dur": 261.228, "args": {"Python parent id": 671, "Python id": 869, "Ev Idx": 868}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(670): _handle_events", "pid": 1304242, "tid": 1304242, "ts": 6595622948502.1, "dur": 249.23, "args": {"Python parent id": 869, "Python id": 870, "Ev Idx": 869}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622948520.441, "dur": 14.474, "args": {"Python parent id": 870, "Python id": 871, "Ev Idx": 870}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622948526.681, "dur": 6.885, "args": {"Python parent id": 871, "Python id": 872, "Ev Idx": 871}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595622948572.072, "dur": 22.791, "args": {"Python parent id": 870, "Python id": 873, "Ev Idx": 872}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622948577.075, "dur": 4.628, "args": {"Python parent id": 873, "Python id": 874, "Ev Idx": 873}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622948586.226, "dur": 3.734, "args": {"Python parent id": 873, "Python id": 875, "Ev Idx": 874}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622948588.061, "dur": 1.403, "args": {"Python parent id": 875, "Python id": 876, "Ev Idx": 875}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622948592.443, "dur": 1.947, "args": {"Python parent id": 873, "Python id": 877, "Ev Idx": 876}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622948593.403, "dur": 0.779, "args": {"Python parent id": 877, "Python id": 878, "Ev Idx": 877}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(586): _handle_recv", "pid": 1304242, "tid": 1304242, "ts": 6595622948597.548, "dur": 138.222, "args": {"Python parent id": 870, "Python id": 879, "Ev Idx": 878}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622948600.232, "dur": 2.272, "args": {"Python parent id": 879, "Python id": 880, "Ev Idx": 879}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622948601.281, "dur": 0.965, "args": {"Python parent id": 880, "Python id": 881, "Ev Idx": 880}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595622948609.466, "dur": 8.34, "args": {"Python parent id": 879, "Python id": 882, "Ev Idx": 881}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622948610.282, "dur": 0.658, "args": {"Python parent id": 882, "Python id": 883, "Ev Idx": 882}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622948612.421, "dur": 1.951, "args": {"Python parent id": 882, "Python id": 884, "Ev Idx": 883}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622948613.398, "dur": 0.774, "args": {"Python parent id": 884, "Python id": 885, "Ev Idx": 884}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622948615.655, "dur": 1.76, "args": {"Python parent id": 882, "Python id": 886, "Ev Idx": 885}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622948616.488, "dur": 0.727, "args": {"Python parent id": 886, "Python id": 887, "Ev Idx": 886}}, {"ph": "X", "cat": "python_function", "name": "<built-in method popleft of collections.deque object at 0x7fa02f673580>", "pid": 1304242, "tid": 1304242, "ts": 6595622948621.901, "dur": 0.814, "args": {"Python parent id": 879, "Python id": 888, "Ev Idx": 887}}, {"ph": "X", "cat": "python_function", "name": "<built-in method done of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622948628.047, "dur": 0.773, "args": {"Python parent id": 879, "Python id": 889, "Ev Idx": 888}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(704): _drop_io_state", "pid": 1304242, "tid": 1304242, "ts": 6595622948631.733, "dur": 46.942, "args": {"Python parent id": 879, "Python id": 890, "Ev Idx": 889}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595622948634.636, "dur": 8.146, "args": {"Python parent id": 890, "Python id": 891, "Ev Idx": 890}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622948635.496, "dur": 0.263, "args": {"Python parent id": 891, "Python id": 892, "Ev Idx": 891}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622948637.4, "dur": 1.974, "args": {"Python parent id": 891, "Python id": 893, "Ev Idx": 892}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622948638.465, "dur": 0.663, "args": {"Python parent id": 893, "Python id": 894, "Ev Idx": 893}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622948640.634, "dur": 1.835, "args": {"Python parent id": 891, "Python id": 895, "Ev Idx": 894}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622948641.467, "dur": 0.799, "args": {"Python parent id": 895, "Python id": 896, "Ev Idx": 895}}, {"ph": "X", "cat": "python_function", "name": "enum.py(1000): __invert__", "pid": 1304242, "tid": 1304242, "ts": 6595622948646.158, "dur": 4.803, "args": {"Python parent id": 890, "Python id": 897, "Ev Idx": 896}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622948648.292, "dur": 2.228, "args": {"Python parent id": 897, "Python id": 898, "Ev Idx": 897}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622948649.291, "dur": 0.937, "args": {"Python parent id": 898, "Python id": 899, "Ev Idx": 898}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595622948651.926, "dur": 8.876, "args": {"Python parent id": 890, "Python id": 900, "Ev Idx": 899}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622948652.513, "dur": 0.238, "args": {"Python parent id": 900, "Python id": 901, "Ev Idx": 900}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622948654.026, "dur": 1.534, "args": {"Python parent id": 900, "Python id": 902, "Ev Idx": 901}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622948654.996, "dur": 0.367, "args": {"Python parent id": 902, "Python id": 903, "Ev Idx": 902}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622948658.6, "dur": 1.889, "args": {"Python parent id": 900, "Python id": 904, "Ev Idx": 903}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622948659.487, "dur": 0.79, "args": {"Python parent id": 904, "Python id": 905, "Ev Idx": 904}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/socket.py(376): __setattr__", "pid": 1304242, "tid": 1304242, "ts": 6595622948663.047, "dur": 9.05, "args": {"Python parent id": 890, "Python id": 906, "Ev Idx": 905}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(710): _update_handler", "pid": 1304242, "tid": 1304242, "ts": 6595622948674.044, "dur": 4.222, "args": {"Python parent id": 890, "Python id": 907, "Ev Idx": 906}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(682): _schedule_remaining_events", "pid": 1304242, "tid": 1304242, "ts": 6595622948676.104, "dur": 1.498, "args": {"Python parent id": 907, "Python id": 908, "Ev Idx": 907}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(193): cancel", "pid": 1304242, "tid": 1304242, "ts": 6595622948682.018, "dur": 0.493, "args": {"Python parent id": 879, "Python id": 909, "Ev Idx": 908}}, {"ph": "X", "cat": "python_function", "name": "enum.py(980): __or__", "pid": 1304242, "tid": 1304242, "ts": 6595622948690.512, "dur": 12.232, "args": {"Python parent id": 879, "Python id": 910, "Ev Idx": 909}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622948692.379, "dur": 0.736, "args": {"Python parent id": 910, "Python id": 911, "Ev Idx": 910}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622948695.733, "dur": 2.498, "args": {"Python parent id": 910, "Python id": 912, "Ev Idx": 911}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622948696.675, "dur": 1.025, "args": {"Python parent id": 912, "Python id": 913, "Ev Idx": 912}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622948700.427, "dur": 1.827, "args": {"Python parent id": 910, "Python id": 914, "Ev Idx": 913}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622948701.269, "dur": 0.787, "args": {"Python parent id": 914, "Python id": 915, "Ev Idx": 914}}, {"ph": "X", "cat": "python_function", "name": "<built-in method set_result of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622948719.514, "dur": 15.587, "args": {"Python parent id": 879, "Python id": 916, "Ev Idx": 915}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595622948738.843, "dur": 9.322, "args": {"Python parent id": 870, "Python id": 917, "Ev Idx": 916}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622948739.865, "dur": 0.703, "args": {"Python parent id": 917, "Python id": 918, "Ev Idx": 917}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622948741.914, "dur": 2.374, "args": {"Python parent id": 917, "Python id": 919, "Ev Idx": 918}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622948742.962, "dur": 0.973, "args": {"Python parent id": 919, "Python id": 920, "Ev Idx": 919}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622948745.941, "dur": 1.878, "args": {"Python parent id": 917, "Python id": 921, "Ev Idx": 920}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622948746.823, "dur": 0.793, "args": {"Python parent id": 921, "Python id": 922, "Ev Idx": 921}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(682): _schedule_remaining_events", "pid": 1304242, "tid": 1304242, "ts": 6595622948749.973, "dur": 0.687, "args": {"Python parent id": 870, "Python id": 923, "Ev Idx": 922}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(452): _remove_finished_future", "pid": 1304242, "tid": 1304242, "ts": 6595622948765.718, "dur": 0.739, "args": {"Python parent id": 671, "Python id": 924, "Ev Idx": 923}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(341): _chain", "pid": 1304242, "tid": 1304242, "ts": 6595622948774.912, "dur": 118.08, "args": {"Python parent id": 671, "Python id": 925, "Ev Idx": 924}}, {"ph": "X", "cat": "python_function", "name": "<built-in method done of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622948776.148, "dur": 0.428, "args": {"Python parent id": 925, "Python id": 926, "Ev Idx": 925}}, {"ph": "X", "cat": "python_function", "name": "<built-in method exception of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622948777.792, "dur": 0.421, "args": {"Python parent id": 925, "Python id": 927, "Ev Idx": 926}}, {"ph": "X", "cat": "python_function", "name": "<built-in method result of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622948779.328, "dur": 0.235, "args": {"Python parent id": 925, "Python id": 928, "Ev Idx": 927}}, {"ph": "X", "cat": "python_function", "name": "<built-in function loads>", "pid": 1304242, "tid": 1304242, "ts": 6595622948780.674, "dur": 102.792, "args": {"Python parent id": 925, "Python id": 929, "Ev Idx": 928}}, {"ph": "X", "cat": "python_function", "name": "<built-in method set_result of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622948885.948, "dur": 6.322, "args": {"Python parent id": 925, "Python id": 930, "Ev Idx": 929}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(368): _chain_cancel", "pid": 1304242, "tid": 1304242, "ts": 6595622948912.357, "dur": 2.784, "args": {"Python parent id": 671, "Python id": 931, "Ev Idx": 930}}, {"ph": "X", "cat": "python_function", "name": "<built-in method done of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622948913.857, "dur": 0.456, "args": {"Python parent id": 931, "Python id": 932, "Ev Idx": 931}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(2071): print_exception_wrapper", "pid": 1304242, "tid": 1304242, "ts": 6595622948925.835, "dur": 354.213, "args": {"Python parent id": 671, "Python id": 933, "Ev Idx": 932}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(1581): handle_loop", "pid": 1304242, "tid": 1304242, "ts": 6595622948927.539, "dur": 352.168, "args": {"Python parent id": 933, "Python id": 934, "Ev Idx": 933}}, {"ph": "X", "cat": "python_function", "name": "sglang/utils.py(475): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622948935.629, "dur": 75.66, "args": {"Python parent id": 934, "Python id": 935, "Ev Idx": 934}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622948939.971, "dur": 0.243, "args": {"Python parent id": 935, "Python id": 936, "Ev Idx": 935}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(1585): _handle_batch_output", "pid": 1304242, "tid": 1304242, "ts": 6595622948942.048, "dur": 68.221, "args": {"Python parent id": 935, "Python id": 937, "Ev Idx": 936}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595622948948.03, "dur": 1.295, "args": {"Python parent id": 937, "Python id": 938, "Ev Idx": 937}}, {"ph": "X", "cat": "python_function", "name": "<built-in function getattr>", "pid": 1304242, "tid": 1304242, "ts": 6595622948958.306, "dur": 1.214, "args": {"Python parent id": 937, "Python id": 939, "Ev Idx": 938}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622948960.565, "dur": 0.966, "args": {"Python parent id": 937, "Python id": 940, "Ev Idx": 939}}, {"ph": "X", "cat": "python_function", "name": "<built-in method update of dict object at 0x7fa02f737540>", "pid": 1304242, "tid": 1304242, "ts": 6595622948964.586, "dur": 2.248, "args": {"Python parent id": 937, "Python id": 941, "Ev Idx": 940}}, {"ph": "X", "cat": "python_function", "name": "<built-in function getattr>", "pid": 1304242, "tid": 1304242, "ts": 6595622948968.481, "dur": 0.426, "args": {"Python parent id": 937, "Python id": 942, "Ev Idx": 941}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622948970.142, "dur": 0.21, "args": {"Python parent id": 937, "Python id": 943, "Ev Idx": 942}}, {"ph": "X", "cat": "python_function", "name": "<built-in method extend of list object at 0x7fa03508f040>", "pid": 1304242, "tid": 1304242, "ts": 6595622948976.016, "dur": 0.894, "args": {"Python parent id": 937, "Python id": 944, "Ev Idx": 943}}, {"ph": "X", "cat": "python_function", "name": "<built-in method copy of list object at 0x7fa03508f040>", "pid": 1304242, "tid": 1304242, "ts": 6595622948977.883, "dur": 0.942, "args": {"Python parent id": 937, "Python id": 945, "Ev Idx": 944}}, {"ph": "X", "cat": "python_function", "name": "<built-in function time>", "pid": 1304242, "tid": 1304242, "ts": 6595622948985.344, "dur": 1.049, "args": {"Python parent id": 937, "Python id": 946, "Ev Idx": 945}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of list object at 0x7fa02f714280>", "pid": 1304242, "tid": 1304242, "ts": 6595622948993.082, "dur": 0.508, "args": {"Python parent id": 937, "Python id": 947, "Ev Idx": 946}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(183): set", "pid": 1304242, "tid": 1304242, "ts": 6595622948997.181, "dur": 8.239, "args": {"Python parent id": 937, "Python id": 948, "Ev Idx": 947}}, {"ph": "X", "cat": "python_function", "name": "<built-in method done of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622949000.807, "dur": 0.24, "args": {"Python parent id": 948, "Python id": 949, "Ev Idx": 948}}, {"ph": "X", "cat": "python_function", "name": "<built-in method set_result of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622949001.875, "dur": 2.067, "args": {"Python parent id": 948, "Python id": 950, "Ev Idx": 949}}, {"ph": "X", "cat": "python_function", "name": "<built-in function time>", "pid": 1304242, "tid": 1304242, "ts": 6595622949012.966, "dur": 0.411, "args": {"Python parent id": 934, "Python id": 951, "Ev Idx": 950}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/socket.py(961): recv_pyobj", "pid": 1304242, "tid": 1304242, "ts": 6595622949017.366, "dur": 259.941, "args": {"Python parent id": 934, "Python id": 952, "Ev Idx": 951}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(281): recv", "pid": 1304242, "tid": 1304242, "ts": 6595622949019.768, "dur": 243.26, "args": {"Python parent id": 952, "Python id": 953, "Ev Idx": 952}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(470): _add_recv_event", "pid": 1304242, "tid": 1304242, "ts": 6595622949024.864, "dur": 237.219, "args": {"Python parent id": 953, "Python id": 954, "Ev Idx": 953}}, {"ph": "X", "cat": "python_function", "name": "<built-in method startswith of str object at 0x7fa2cbeb2070>", "pid": 1304242, "tid": 1304242, "ts": 6595622949034.807, "dur": 2.526, "args": {"Python parent id": 954, "Python id": 955, "Ev Idx": 954}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595622949038.432, "dur": 0.545, "args": {"Python parent id": 954, "Python id": 956, "Ev Idx": 955}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595622949041.21, "dur": 16.371, "args": {"Python parent id": 954, "Python id": 957, "Ev Idx": 956}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622949042.749, "dur": 1.521, "args": {"Python parent id": 957, "Python id": 958, "Ev Idx": 957}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622949048.783, "dur": 4.195, "args": {"Python parent id": 957, "Python id": 959, "Ev Idx": 958}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622949050.888, "dur": 1.732, "args": {"Python parent id": 959, "Python id": 960, "Ev Idx": 959}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622949055.169, "dur": 2.059, "args": {"Python parent id": 957, "Python id": 961, "Ev Idx": 960}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622949056.258, "dur": 0.767, "args": {"Python parent id": 961, "Python id": 962, "Ev Idx": 961}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595622949059.251, "dur": 1.503, "args": {"Python parent id": 954, "Python id": 963, "Ev Idx": 962}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/attrsettr.py(43): __getattr__", "pid": 1304242, "tid": 1304242, "ts": 6595622949072.176, "dur": 35.22, "args": {"Python parent id": 954, "Python id": 964, "Ev Idx": 963}}, {"ph": "X", "cat": "python_function", "name": "<built-in method upper of str object at 0x7fa1224219f0>", "pid": 1304242, "tid": 1304242, "ts": 6595622949073.87, "dur": 1.476, "args": {"Python parent id": 964, "Python id": 965, "Ev Idx": 964}}, {"ph": "X", "cat": "python_function", "name": "<built-in function getattr>", "pid": 1304242, "tid": 1304242, "ts": 6595622949076.907, "dur": 2.073, "args": {"Python parent id": 964, "Python id": 966, "Ev Idx": 965}}, {"ph": "X", "cat": "python_function", "name": "<frozen importlib._bootstrap>(1053): _handle_fromlist", "pid": 1304242, "tid": 1304242, "ts": 6595622949085.581, "dur": 5.708, "args": {"Python parent id": 964, "Python id": 967, "Ev Idx": 966}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622949087.59, "dur": 0.276, "args": {"Python parent id": 967, "Python id": 968, "Ev Idx": 967}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595622949089.585, "dur": 0.736, "args": {"Python parent id": 967, "Python id": 969, "Ev Idx": 968}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/attrsettr.py(66): _get_attr_opt", "pid": 1304242, "tid": 1304242, "ts": 6595622949094.579, "dur": 11.951, "args": {"Python parent id": 964, "Python id": 970, "Ev Idx": 969}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622949098.521, "dur": 2.806, "args": {"Python parent id": 970, "Python id": 971, "Ev Idx": 970}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622949099.734, "dur": 1.25, "args": {"Python parent id": 971, "Python id": 972, "Ev Idx": 971}}, {"ph": "X", "cat": "python_function", "name": "<string>(1): <lambda>", "pid": 1304242, "tid": 1304242, "ts": 6595622949114.516, "dur": 3.494, "args": {"Python parent id": 954, "Python id": 973, "Ev Idx": 972}}, {"ph": "X", "cat": "python_function", "name": "<built-in method __new__ of type object at 0x57ea91926320>", "pid": 1304242, "tid": 1304242, "ts": 6595622949116.176, "dur": 1.398, "args": {"Python parent id": 973, "Python id": 974, "Ev Idx": 973}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of collections.deque object at 0x7fa02f673580>", "pid": 1304242, "tid": 1304242, "ts": 6595622949121.145, "dur": 0.293, "args": {"Python parent id": 954, "Python id": 975, "Ev Idx": 974}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622949124.891, "dur": 2.601, "args": {"Python parent id": 954, "Python id": 976, "Ev Idx": 975}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622949126.224, "dur": 1.056, "args": {"Python parent id": 976, "Python id": 977, "Ev Idx": 976}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595622949168.492, "dur": 10.018, "args": {"Python parent id": 954, "Python id": 978, "Ev Idx": 977}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622949169.777, "dur": 0.84, "args": {"Python parent id": 978, "Python id": 979, "Ev Idx": 978}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622949172.519, "dur": 2.408, "args": {"Python parent id": 978, "Python id": 980, "Ev Idx": 979}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622949173.762, "dur": 0.927, "args": {"Python parent id": 980, "Python id": 981, "Ev Idx": 980}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622949176.385, "dur": 1.804, "args": {"Python parent id": 978, "Python id": 982, "Ev Idx": 981}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622949177.247, "dur": 0.742, "args": {"Python parent id": 982, "Python id": 983, "Ev Idx": 982}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add_done_callback of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622949185.211, "dur": 1.191, "args": {"Python parent id": 954, "Python id": 984, "Ev Idx": 983}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(698): _add_io_state", "pid": 1304242, "tid": 1304242, "ts": 6595622949188.451, "dur": 73.066, "args": {"Python parent id": 954, "Python id": 985, "Ev Idx": 984}}, {"ph": "X", "cat": "python_function", "name": "enum.py(980): __or__", "pid": 1304242, "tid": 1304242, "ts": 6595622949191.085, "dur": 10.317, "args": {"Python parent id": 985, "Python id": 986, "Ev Idx": 985}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622949192.654, "dur": 0.185, "args": {"Python parent id": 986, "Python id": 987, "Ev Idx": 986}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622949194.722, "dur": 1.748, "args": {"Python parent id": 986, "Python id": 988, "Ev Idx": 987}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622949195.639, "dur": 0.544, "args": {"Python parent id": 988, "Python id": 989, "Ev Idx": 988}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622949198.598, "dur": 2.285, "args": {"Python parent id": 986, "Python id": 990, "Ev Idx": 989}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622949199.666, "dur": 0.962, "args": {"Python parent id": 990, "Python id": 991, "Ev Idx": 990}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/socket.py(376): __setattr__", "pid": 1304242, "tid": 1304242, "ts": 6595622949203.119, "dur": 4.898, "args": {"Python parent id": 985, "Python id": 992, "Ev Idx": 991}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(710): _update_handler", "pid": 1304242, "tid": 1304242, "ts": 6595622949209.882, "dur": 51.003, "args": {"Python parent id": 985, "Python id": 993, "Ev Idx": 992}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(49): _get_loop", "pid": 1304242, "tid": 1304242, "ts": 6595622949211.205, "dur": 11.498, "args": {"Python parent id": 993, "Python id": 994, "Ev Idx": 993}}, {"ph": "X", "cat": "python_function", "name": "zmq/asyncio.py(106): _default_loop", "pid": 1304242, "tid": 1304242, "ts": 6595622949213.807, "dur": 6.713, "args": {"Python parent id": 994, "Python id": 995, "Ev Idx": 994}}, {"ph": "X", "cat": "python_function", "name": "<built-in function get_running_loop>", "pid": 1304242, "tid": 1304242, "ts": 6595622949217.072, "dur": 2.896, "args": {"Python parent id": 995, "Python id": 996, "Ev Idx": 995}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(682): _schedule_remaining_events", "pid": 1304242, "tid": 1304242, "ts": 6595622949225.552, "dur": 34.589, "args": {"Python parent id": 993, "Python id": 997, "Ev Idx": 996}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622949230.807, "dur": 4.051, "args": {"Python parent id": 997, "Python id": 998, "Ev Idx": 997}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622949232.649, "dur": 1.747, "args": {"Python parent id": 998, "Python id": 999, "Ev Idx": 998}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595622949247.416, "dur": 11.836, "args": {"Python parent id": 997, "Python id": 1000, "Ev Idx": 999}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622949248.897, "dur": 1.821, "args": {"Python parent id": 1000, "Python id": 1001, "Ev Idx": 1000}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622949253.315, "dur": 2.24, "args": {"Python parent id": 1000, "Python id": 1002, "Ev Idx": 1001}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622949254.383, "dur": 0.974, "args": {"Python parent id": 1002, "Python id": 1003, "Ev Idx": 1002}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622949257.089, "dur": 1.785, "args": {"Python parent id": 1000, "Python id": 1004, "Ev Idx": 1003}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622949257.929, "dur": 0.736, "args": {"Python parent id": 1004, "Python id": 1005, "Ev Idx": 1004}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(337): _deserialize", "pid": 1304242, "tid": 1304242, "ts": 6595622949266.484, "dur": 10.152, "args": {"Python parent id": 952, "Python id": 1006, "Ev Idx": 1005}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add_done_callback of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622949273.558, "dur": 1.133, "args": {"Python parent id": 1006, "Python id": 1007, "Ev Idx": 1006}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add_done_callback of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622949276.029, "dur": 0.22, "args": {"Python parent id": 1006, "Python id": 1008, "Ev Idx": 1007}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(214): wait", "pid": 1304242, "tid": 1304242, "ts": 6595622949296.211, "dur": 3.481, "args": {"Python parent id": 671, "Python id": 1009, "Ev Idx": 1008}}, {"ph": "X", "cat": "python_function", "name": "<built-in method remove of collections.deque object at 0x7fa034862200>", "pid": 1304242, "tid": 1304242, "ts": 6595622949298.487, "dur": 0.794, "args": {"Python parent id": 1009, "Python id": 1010, "Ev Idx": 1009}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(387): _release_waiter", "pid": 1304242, "tid": 1304242, "ts": 6595622949309.574, "dur": 4.39, "args": {"Python parent id": 671, "Python id": 1011, "Ev Idx": 1010}}, {"ph": "X", "cat": "python_function", "name": "<built-in method done of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622949310.783, "dur": 0.234, "args": {"Python parent id": 1011, "Python id": 1012, "Ev Idx": 1011}}, {"ph": "X", "cat": "python_function", "name": "<built-in method set_result of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622949312.144, "dur": 1.541, "args": {"Python parent id": 1011, "Python id": 1013, "Ev Idx": 1012}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(508): generate_request", "pid": 1304242, "tid": 1304242, "ts": 6595622949321.815, "dur": 37.987, "args": {"Python parent id": 671, "Python id": 1014, "Ev Idx": 1013}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(806): _wait_one_response", "pid": 1304242, "tid": 1304242, "ts": 6595622949323.747, "dur": 32.599, "args": {"Python parent id": 1014, "Python id": 1015, "Ev Idx": 1014}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(432): wait_for", "pid": 1304242, "tid": 1304242, "ts": 6595622949325.506, "dur": 8.703, "args": {"Python parent id": 1015, "Python id": 1016, "Ev Idx": 1015}}, {"ph": "X", "cat": "python_function", "name": "<built-in method done of _asyncio.Task object at 0x7fa02f684c70>", "pid": 1304242, "tid": 1304242, "ts": 6595622949327.58, "dur": 0.317, "args": {"Python parent id": 1016, "Python id": 1017, "Ev Idx": 1016}}, {"ph": "X", "cat": "python_function", "name": "<built-in method result of _asyncio.Task object at 0x7fa02f684c70>", "pid": 1304242, "tid": 1304242, "ts": 6595622949328.628, "dur": 0.271, "args": {"Python parent id": 1016, "Python id": 1018, "Ev Idx": 1017}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(39): _remove", "pid": 1304242, "tid": 1304242, "ts": 6595622949337.093, "dur": 6.488, "args": {"Python parent id": 1015, "Python id": 1019, "Ev Idx": 1018}}, {"ph": "X", "cat": "python_function", "name": "<built-in method discard of set object at 0x7fa126a5e180>", "pid": 1304242, "tid": 1304242, "ts": 6595622949341.885, "dur": 1.383, "args": {"Python parent id": 1019, "Python id": 1020, "Ev Idx": 1019}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595622949351.204, "dur": 0.582, "args": {"Python parent id": 1015, "Python id": 1021, "Ev Idx": 1020}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622949352.57, "dur": 0.23, "args": {"Python parent id": 1015, "Python id": 1022, "Ev Idx": 1021}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595622949353.69, "dur": 0.456, "args": {"Python parent id": 1015, "Python id": 1023, "Ev Idx": 1022}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595622949355.256, "dur": 0.25, "args": {"Python parent id": 1015, "Python id": 1024, "Ev Idx": 1023}}, {"ph": "X", "cat": "python_function", "name": "weakref.py(134): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595622949400.536, "dur": 3.696, "args": {"Python parent id": 671, "Python id": 1025, "Ev Idx": 1024}}, {"ph": "X", "cat": "python_function", "name": "socket.py(488): _decref_socketios", "pid": 1304242, "tid": 1304242, "ts": 6595622949410.192, "dur": 3.663, "args": {"Python parent id": 671, "Python id": 1026, "Ev Idx": 1025}}, {"ph": "X", "cat": "python_function", "name": "socket.py(498): close", "pid": 1304242, "tid": 1304242, "ts": 6595622949433.093, "dur": 23.49, "args": {"Python parent id": 671, "Python id": 1027, "Ev Idx": 1026}}, {"ph": "X", "cat": "python_function", "name": "socket.py(494): _real_close", "pid": 1304242, "tid": 1304242, "ts": 6595622949436.145, "dur": 19.847, "args": {"Python parent id": 1027, "Python id": 1028, "Ev Idx": 1027}}, {"ph": "X", "cat": "python_function", "name": "<built-in method close of socket object at 0x7fa02f6c7880>", "pid": 1304242, "tid": 1304242, "ts": 6595622949437.909, "dur": 17.319, "args": {"Python parent id": 1028, "Python id": 1029, "Ev Idx": 1028}}, {"ph": "X", "cat": "python_function", "name": "socket.py(498): close", "pid": 1304242, "tid": 1304242, "ts": 6595622949458.601, "dur": 13.249, "args": {"Python parent id": 671, "Python id": 1030, "Ev Idx": 1029}}, {"ph": "X", "cat": "python_function", "name": "socket.py(494): _real_close", "pid": 1304242, "tid": 1304242, "ts": 6595622949459.506, "dur": 11.928, "args": {"Python parent id": 1030, "Python id": 1031, "Ev Idx": 1030}}, {"ph": "X", "cat": "python_function", "name": "<built-in method close of socket object at 0x7fa02f6c7880>", "pid": 1304242, "tid": 1304242, "ts": 6595622949460.32, "dur": 10.831, "args": {"Python parent id": 1031, "Python id": 1032, "Ev Idx": 1031}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(39): _remove", "pid": 1304242, "tid": 1304242, "ts": 6595622949489.749, "dur": 3.102, "args": {"Python parent id": 671, "Python id": 1033, "Ev Idx": 1032}}, {"ph": "X", "cat": "python_function", "name": "<built-in method discard of set object at 0x7fa126a5e180>", "pid": 1304242, "tid": 1304242, "ts": 6595622949492.009, "dur": 0.544, "args": {"Python parent id": 1033, "Python id": 1034, "Ev Idx": 1033}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(509): generate_request", "pid": 1304242, "tid": 1304242, "ts": 6595622949498.786, "dur": 75.206, "args": {"Python parent id": 9, "Python id": 1035, "Ev Idx": 1034}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(39): _remove", "pid": 1304242, "tid": 1304242, "ts": 6595622949501.666, "dur": 6.41, "args": {"Python parent id": 1035, "Python id": 1036, "Ev Idx": 1035}}, {"ph": "X", "cat": "python_function", "name": "<built-in method discard of set object at 0x7fa126a5e180>", "pid": 1304242, "tid": 1304242, "ts": 6595622949502.978, "dur": 4.865, "args": {"Python parent id": 1036, "Python id": 1037, "Ev Idx": 1036}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(116): discard", "pid": 1304242, "tid": 1304242, "ts": 6595622949511.398, "dur": 5.203, "args": {"Python parent id": 1035, "Python id": 1038, "Ev Idx": 1037}}, {"ph": "X", "cat": "python_function", "name": "<built-in method discard of set object at 0x7fa126a5e180>", "pid": 1304242, "tid": 1304242, "ts": 6595622949515.313, "dur": 0.709, "args": {"Python parent id": 1038, "Python id": 1039, "Ev Idx": 1038}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/aio_rwlock.py(87): __aexit__", "pid": 1304242, "tid": 1304242, "ts": 6595622949536.52, "dur": 35.189, "args": {"Python parent id": 1035, "Python id": 1040, "Ev Idx": 1039}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/aio_rwlock.py(51): release_reader", "pid": 1304242, "tid": 1304242, "ts": 6595622949539.883, "dur": 31.402, "args": {"Python parent id": 1040, "Python id": 1041, "Ev Idx": 1040}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(13): __aenter__", "pid": 1304242, "tid": 1304242, "ts": 6595622949545.467, "dur": 5.241, "args": {"Python parent id": 1041, "Python id": 1042, "Ev Idx": 1041}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(93): acquire", "pid": 1304242, "tid": 1304242, "ts": 6595622949547.188, "dur": 2.638, "args": {"Python parent id": 1042, "Python id": 1043, "Ev Idx": 1042}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(324): notify_all", "pid": 1304242, "tid": 1304242, "ts": 6595622949554.453, "dur": 9.146, "args": {"Python parent id": 1041, "Python id": 1044, "Ev Idx": 1043}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595622949556.776, "dur": 0.654, "args": {"Python parent id": 1044, "Python id": 1045, "Ev Idx": 1044}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(300): notify", "pid": 1304242, "tid": 1304242, "ts": 6595622949558.142, "dur": 5.079, "args": {"Python parent id": 1044, "Python id": 1046, "Ev Idx": 1045}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(89): locked", "pid": 1304242, "tid": 1304242, "ts": 6595622949560.545, "dur": 0.513, "args": {"Python parent id": 1046, "Python id": 1047, "Ev Idx": 1046}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(19): __aexit__", "pid": 1304242, "tid": 1304242, "ts": 6595622949565.82, "dur": 4.786, "args": {"Python parent id": 1041, "Python id": 1048, "Ev Idx": 1047}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(125): release", "pid": 1304242, "tid": 1304242, "ts": 6595622949567.067, "dur": 3.11, "args": {"Python parent id": 1048, "Python id": 1049, "Ev Idx": 1048}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(142): _wake_up_first", "pid": 1304242, "tid": 1304242, "ts": 6595622949569.034, "dur": 0.756, "args": {"Python parent id": 1049, "Python id": 1050, "Ev Idx": 1049}}, {"ph": "X", "cat": "python_function", "name": "<built-in function time>", "pid": 1304242, "tid": 1304242, "ts": 6595622949586.902, "dur": 0.607, "args": {"Python parent id": 9, "Python id": 1051, "Ev Idx": 1050}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595622949661.852, "dur": 0.391, "args": {"Python parent id": 9, "Python id": 1052, "Ev Idx": 1051}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of list object at 0x7fa02f714280>", "pid": 1304242, "tid": 1304242, "ts": 6595622949666.19, "dur": 0.389, "args": {"Python parent id": 9, "Python id": 1053, "Ev Idx": 1052}}, {"ph": "X", "cat": "python_function", "name": "<built-in function print>", "pid": 1304242, "tid": 1304242, "ts": 6595622949676.947, "dur": 37.171, "args": {"Python parent id": 9, "Python id": 1054, "Ev Idx": 1053}}, {"ph": "X", "cat": "python_function", "name": "<built-in function print>", "pid": 1304242, "tid": 1304242, "ts": 6595622949720.528, "dur": 15.65, "args": {"Python parent id": 9, "Python id": 1055, "Ev Idx": 1054}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595622949740.706, "dur": 0.533, "args": {"Python parent id": 9, "Python id": 1056, "Ev Idx": 1055}}, {"ph": "X", "cat": "python_function", "name": "<built-in function print>", "pid": 1304242, "tid": 1304242, "ts": 6595622949744.911, "dur": 18.501, "args": {"Python parent id": 9, "Python id": 1057, "Ev Idx": 1056}}, {"ph": "X", "cat": "python_function", "name": "operator_profiler.py(28): set_step", "pid": 1304242, "tid": 1304242, "ts": 6595622949769.008, "dur": 1.777, "args": {"Python parent id": 9, "Python id": 1058, "Ev Idx": 1057}}, {"ph": "X", "cat": "python_function", "name": "<built-in function time>", "pid": 1304242, "tid": 1304242, "ts": 6595622949772.286, "dur": 0.484, "args": {"Python parent id": 9, "Python id": 1059, "Ev Idx": 1058}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/entrypoints/engine.py(140): generate", "pid": 1304242, "tid": 1304242, "ts": 6595622949776.977, "dur": 149707.738, "args": {"Python parent id": 9, "Python id": 1060, "Ev Idx": 1059}}, {"ph": "X", "cat": "python_function", "name": "<string>(2): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622949793.303, "dur": 10.192, "args": {"Python parent id": 1060, "Python id": 1061, "Ev Idx": 1060}}, {"ph": "X", "cat": "python_function", "name": "<built-in function get_event_loop>", "pid": 1304242, "tid": 1304242, "ts": 6595622949807.035, "dur": 12.082, "args": {"Python parent id": 1060, "Python id": 1062, "Ev Idx": 1061}}, {"ph": "X", "cat": "python_function", "name": "asyncio/events.py(736): get_event_loop_policy", "pid": 1304242, "tid": 1304242, "ts": 6595622949810.082, "dur": 1.454, "args": {"Python parent id": 1062, "Python id": 1063, "Ev Idx": 1062}}, {"ph": "X", "cat": "python_function", "name": "asyncio/events.py(645): get_event_loop", "pid": 1304242, "tid": 1304242, "ts": 6595622949813.418, "dur": 5.456, "args": {"Python parent id": 1062, "Python id": 1064, "Ev Idx": 1063}}, {"ph": "X", "cat": "python_function", "name": "asyncio/base_futures.py(14): isfuture", "pid": 1304242, "tid": 1304242, "ts": 6595622949827.99, "dur": 7.168, "args": {"Python parent id": 1060, "Python id": 1065, "Ev Idx": 1064}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595622949829.943, "dur": 4.718, "args": {"Python parent id": 1065, "Python id": 1066, "Ev Idx": 1065}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(610): ensure_future", "pid": 1304242, "tid": 1304242, "ts": 6595622949838.372, "dur": 38.304, "args": {"Python parent id": 1060, "Python id": 1067, "Ev Idx": 1066}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(618): _ensure_future", "pid": 1304242, "tid": 1304242, "ts": 6595622949840.302, "dur": 35.754, "args": {"Python parent id": 1067, "Python id": 1068, "Ev Idx": 1067}}, {"ph": "X", "cat": "python_function", "name": "asyncio/base_futures.py(14): isfuture", "pid": 1304242, "tid": 1304242, "ts": 6595622949843.231, "dur": 2.412, "args": {"Python parent id": 1068, "Python id": 1069, "Ev Idx": 1068}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595622949844.003, "dur": 1.494, "args": {"Python parent id": 1069, "Python id": 1070, "Ev Idx": 1069}}, {"ph": "X", "cat": "python_function", "name": "asyncio/coroutines.py(177): iscoroutine", "pid": 1304242, "tid": 1304242, "ts": 6595622949848.048, "dur": 2.303, "args": {"Python parent id": 1068, "Python id": 1071, "Ev Idx": 1070}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(86): add", "pid": 1304242, "tid": 1304242, "ts": 6595622949869.503, "dur": 5.19, "args": {"Python parent id": 1068, "Python id": 1072, "Ev Idx": 1071}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add of set object at 0x7fa126a5dd20>", "pid": 1304242, "tid": 1304242, "ts": 6595622949873.24, "dur": 1.01, "args": {"Python parent id": 1072, "Python id": 1073, "Ev Idx": 1072}}, {"ph": "X", "cat": "python_function", "name": "socket.py(594): socketpair", "pid": 1304242, "tid": 1304242, "ts": 6595622949895.828, "dur": 63.751, "args": {"Python parent id": 1060, "Python id": 1074, "Ev Idx": 1073}}, {"ph": "X", "cat": "python_function", "name": "<built-in function socketpair>", "pid": 1304242, "tid": 1304242, "ts": 6595622949899.577, "dur": 31.54, "args": {"Python parent id": 1074, "Python id": 1075, "Ev Idx": 1074}}, {"ph": "X", "cat": "python_function", "name": "<built-in method detach of _socket.socket object at 0x7fa02f700930>", "pid": 1304242, "tid": 1304242, "ts": 6595622949933.204, "dur": 0.351, "args": {"Python parent id": 1074, "Python id": 1076, "Ev Idx": 1075}}, {"ph": "X", "cat": "python_function", "name": "socket.py(220): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622949936.377, "dur": 14.699, "args": {"Python parent id": 1074, "Python id": 1077, "Ev Idx": 1076}}, {"ph": "X", "cat": "python_function", "name": "<built-in method detach of _socket.socket object at 0x7fa02f700930>", "pid": 1304242, "tid": 1304242, "ts": 6595622949953.221, "dur": 0.108, "args": {"Python parent id": 1074, "Python id": 1078, "Ev Idx": 1077}}, {"ph": "X", "cat": "python_function", "name": "socket.py(220): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622949954.424, "dur": 4.092, "args": {"Python parent id": 1074, "Python id": 1079, "Ev Idx": 1078}}, {"ph": "X", "cat": "python_function", "name": "weakref.py(134): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595622949984.228, "dur": 2.746, "args": {"Python parent id": 1060, "Python id": 1080, "Ev Idx": 1079}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(86): add", "pid": 1304242, "tid": 1304242, "ts": 6595622950038.881, "dur": 3.419, "args": {"Python parent id": 1060, "Python id": 1081, "Ev Idx": 1080}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add of set object at 0x7fa126a5dd20>", "pid": 1304242, "tid": 1304242, "ts": 6595622950041.515, "dur": 0.396, "args": {"Python parent id": 1081, "Python id": 1082, "Ev Idx": 1081}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(482): generate_request", "pid": 1304242, "tid": 1304242, "ts": 6595622950048.856, "dur": 1152.642, "args": {"Python parent id": 1060, "Python id": 1083, "Ev Idx": 1082}}, {"ph": "X", "cat": "python_function", "name": "<built-in function time>", "pid": 1304242, "tid": 1304242, "ts": 6595622950050.952, "dur": 0.642, "args": {"Python parent id": 1083, "Python id": 1084, "Ev Idx": 1083}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(1426): auto_create_handle_loop", "pid": 1304242, "tid": 1304242, "ts": 6595622950053.561, "dur": 1.434, "args": {"Python parent id": 1083, "Python id": 1085, "Ev Idx": 1084}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/io_struct.py(138): normalize_batch_and_arguments", "pid": 1304242, "tid": 1304242, "ts": 6595622950057.123, "dur": 103.99, "args": {"Python parent id": 1083, "Python id": 1086, "Ev Idx": 1085}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/io_struct.py(160): _validate_inputs", "pid": 1304242, "tid": 1304242, "ts": 6595622950059.513, "dur": 2.0, "args": {"Python parent id": 1086, "Python id": 1087, "Ev Idx": 1086}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/io_struct.py(173): _determine_batch_size", "pid": 1304242, "tid": 1304242, "ts": 6595622950062.894, "dur": 4.949, "args": {"Python parent id": 1086, "Python id": 1088, "Ev Idx": 1087}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950065.086, "dur": 0.338, "args": {"Python parent id": 1088, "Python id": 1089, "Ev Idx": 1088}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/io_struct.py(201): _handle_parallel_sampling", "pid": 1304242, "tid": 1304242, "ts": 6595622950069.066, "dur": 5.861, "args": {"Python parent id": 1086, "Python id": 1090, "Ev Idx": 1089}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950070.925, "dur": 0.166, "args": {"Python parent id": 1090, "Python id": 1091, "Ev Idx": 1090}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595622950072.038, "dur": 0.666, "args": {"Python parent id": 1090, "Python id": 1092, "Ev Idx": 1091}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/io_struct.py(227): _normalize_single_inputs", "pid": 1304242, "tid": 1304242, "ts": 6595622950076.473, "dur": 83.695, "args": {"Python parent id": 1086, "Python id": 1093, "Ev Idx": 1092}}, {"ph": "X", "cat": "python_function", "name": "uuid.py(718): uuid4", "pid": 1304242, "tid": 1304242, "ts": 6595622950080.17, "dur": 40.275, "args": {"Python parent id": 1093, "Python id": 1094, "Ev Idx": 1093}}, {"ph": "X", "cat": "python_function", "name": "<built-in function urandom>", "pid": 1304242, "tid": 1304242, "ts": 6595622950083.077, "dur": 5.865, "args": {"Python parent id": 1094, "Python id": 1095, "Ev Idx": 1094}}, {"ph": "X", "cat": "python_function", "name": "uuid.py(138): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622950093.192, "dur": 25.858, "args": {"Python parent id": 1094, "Python id": 1096, "Ev Idx": 1095}}, {"ph": "X", "cat": "python_function", "name": "<built-in method count of list object at 0x7fa02f735100>", "pid": 1304242, "tid": 1304242, "ts": 6595622950095.062, "dur": 1.887, "args": {"Python parent id": 1096, "Python id": 1097, "Ev Idx": 1096}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595622950099.049, "dur": 0.547, "args": {"Python parent id": 1096, "Python id": 1098, "Ev Idx": 1097}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950100.893, "dur": 0.181, "args": {"Python parent id": 1096, "Python id": 1099, "Ev Idx": 1098}}, {"ph": "X", "cat": "python_function", "name": "<built-in method from_bytes of type object at 0x57ea91926320>", "pid": 1304242, "tid": 1304242, "ts": 6595622950103.835, "dur": 2.056, "args": {"Python parent id": 1096, "Python id": 1100, "Ev Idx": 1099}}, {"ph": "X", "cat": "python_function", "name": "uuid.py(333): hex", "pid": 1304242, "tid": 1304242, "ts": 6595622950122.241, "dur": 6.195, "args": {"Python parent id": 1093, "Python id": 1101, "Ev Idx": 1100}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(13): __aenter__", "pid": 1304242, "tid": 1304242, "ts": 6595622950165.86, "dur": 6.016, "args": {"Python parent id": 1083, "Python id": 1102, "Ev Idx": 1101}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(93): acquire", "pid": 1304242, "tid": 1304242, "ts": 6595622950168.656, "dur": 2.334, "args": {"Python parent id": 1102, "Python id": 1103, "Ev Idx": 1102}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(287): wait_for", "pid": 1304242, "tid": 1304242, "ts": 6595622950176.768, "dur": 2.829, "args": {"Python parent id": 1083, "Python id": 1104, "Ev Idx": 1103}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(498): <lambda>", "pid": 1304242, "tid": 1304242, "ts": 6595622950177.789, "dur": 0.979, "args": {"Python parent id": 1104, "Python id": 1105, "Ev Idx": 1104}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(19): __aexit__", "pid": 1304242, "tid": 1304242, "ts": 6595622950182.366, "dur": 4.762, "args": {"Python parent id": 1083, "Python id": 1106, "Ev Idx": 1105}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(125): release", "pid": 1304242, "tid": 1304242, "ts": 6595622950183.672, "dur": 3.002, "args": {"Python parent id": 1106, "Python id": 1107, "Ev Idx": 1106}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(142): _wake_up_first", "pid": 1304242, "tid": 1304242, "ts": 6595622950185.388, "dur": 0.785, "args": {"Python parent id": 1107, "Python id": 1108, "Ev Idx": 1107}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/aio_rwlock.py(21): reader_lock", "pid": 1304242, "tid": 1304242, "ts": 6595622950189.558, "dur": 3.82, "args": {"Python parent id": 1083, "Python id": 1109, "Ev Idx": 1108}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/aio_rwlock.py(80): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622950191.779, "dur": 1.125, "args": {"Python parent id": 1109, "Python id": 1110, "Ev Idx": 1109}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/aio_rwlock.py(83): __aenter__", "pid": 1304242, "tid": 1304242, "ts": 6595622950194.901, "dur": 14.061, "args": {"Python parent id": 1083, "Python id": 1111, "Ev Idx": 1110}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/aio_rwlock.py(43): acquire_reader", "pid": 1304242, "tid": 1304242, "ts": 6595622950196.768, "dur": 11.688, "args": {"Python parent id": 1111, "Python id": 1112, "Ev Idx": 1111}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(13): __aenter__", "pid": 1304242, "tid": 1304242, "ts": 6595622950198.335, "dur": 2.343, "args": {"Python parent id": 1112, "Python id": 1113, "Ev Idx": 1112}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(93): acquire", "pid": 1304242, "tid": 1304242, "ts": 6595622950199.524, "dur": 0.835, "args": {"Python parent id": 1113, "Python id": 1114, "Ev Idx": 1113}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(19): __aexit__", "pid": 1304242, "tid": 1304242, "ts": 6595622950204.882, "dur": 3.073, "args": {"Python parent id": 1112, "Python id": 1115, "Ev Idx": 1114}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(125): release", "pid": 1304242, "tid": 1304242, "ts": 6595622950205.91, "dur": 1.693, "args": {"Python parent id": 1115, "Python id": 1116, "Ev Idx": 1115}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(142): _wake_up_first", "pid": 1304242, "tid": 1304242, "ts": 6595622950206.781, "dur": 0.481, "args": {"Python parent id": 1116, "Python id": 1117, "Ev Idx": 1116}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(516): _tokenize_one_request", "pid": 1304242, "tid": 1304242, "ts": 6595622950213.076, "dur": 619.87, "args": {"Python parent id": 1083, "Python id": 1118, "Ev Idx": 1117}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950215.522, "dur": 0.822, "args": {"Python parent id": 1118, "Python id": 1119, "Ev Idx": 1118}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(2827): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622950221.995, "dur": 518.526, "args": {"Python parent id": 1118, "Python id": 1120, "Ev Idx": 1119}}, {"ph": "X", "cat": "python_function", "name": "<built-in method pop of dict object at 0x7fa02f6ad340>", "pid": 1304242, "tid": 1304242, "ts": 6595622950232.61, "dur": 0.29, "args": {"Python parent id": 1120, "Python id": 1121, "Ev Idx": 1120}}, {"ph": "X", "cat": "python_function", "name": "<built-in method update of dict object at 0x7fa02f737540>", "pid": 1304242, "tid": 1304242, "ts": 6595622950236.291, "dur": 0.933, "args": {"Python parent id": 1120, "Python id": 1122, "Ev Idx": 1121}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(3988): _switch_to_input_mode", "pid": 1304242, "tid": 1304242, "ts": 6595622950240.441, "dur": 0.621, "args": {"Python parent id": 1120, "Python id": 1123, "Ev Idx": 1122}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(2925): _call_one", "pid": 1304242, "tid": 1304242, "ts": 6595622950251.057, "dur": 485.18, "args": {"Python parent id": 1120, "Python id": 1124, "Ev Idx": 1123}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(2949): _is_valid_text_input", "pid": 1304242, "tid": 1304242, "ts": 6595622950253.031, "dur": 1.829, "args": {"Python parent id": 1124, "Python id": 1125, "Ev Idx": 1124}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950254.304, "dur": 0.146, "args": {"Python parent id": 1125, "Python id": 1126, "Ev Idx": 1125}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950257.487, "dur": 0.576, "args": {"Python parent id": 1124, "Python id": 1127, "Ev Idx": 1126}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(3043): encode_plus", "pid": 1304242, "tid": 1304242, "ts": 6595622950268.034, "dur": 465.65, "args": {"Python parent id": 1124, "Python id": 1128, "Ev Idx": 1127}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(2722): _get_padding_truncation_strategies", "pid": 1304242, "tid": 1304242, "ts": 6595622950273.201, "dur": 10.605, "args": {"Python parent id": 1128, "Python id": 1129, "Ev Idx": 1128}}, {"ph": "X", "cat": "python_function", "name": "<built-in method pop of dict object at 0x7fa02f6ad340>", "pid": 1304242, "tid": 1304242, "ts": 6595622950292.264, "dur": 0.661, "args": {"Python parent id": 1128, "Python id": 1130, "Ev Idx": 1129}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(603): _encode_plus", "pid": 1304242, "tid": 1304242, "ts": 6595622950296.851, "dur": 434.492, "args": {"Python parent id": 1128, "Python id": 1131, "Ev Idx": 1130}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(512): _batch_encode_plus", "pid": 1304242, "tid": 1304242, "ts": 6595622950307.823, "dur": 349.518, "args": {"Python parent id": 1131, "Python id": 1132, "Ev Idx": 1131}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950309.527, "dur": 0.521, "args": {"Python parent id": 1132, "Python id": 1133, "Ev Idx": 1132}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(437): set_truncation_and_padding", "pid": 1304242, "tid": 1304242, "ts": 6595622950313.01, "dur": 9.533, "args": {"Python parent id": 1132, "Python id": 1134, "Ev Idx": 1133}}, {"ph": "X", "cat": "python_function", "name": "<built-in method encode_batch of tokenizers.Tokenizer object at 0x57eac2f350b0>", "pid": 1304242, "tid": 1304242, "ts": 6595622950327.415, "dur": 233.885, "args": {"Python parent id": 1132, "Python id": 1135, "Ev Idx": 1134}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(565): <listcomp>", "pid": 1304242, "tid": 1304242, "ts": 6595622950565.37, "dur": 26.596, "args": {"Python parent id": 1132, "Python id": 1136, "Ev Idx": 1135}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(307): _convert_encoding", "pid": 1304242, "tid": 1304242, "ts": 6595622950568.896, "dur": 21.333, "args": {"Python parent id": 1136, "Python id": 1137, "Ev Idx": 1136}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of list object at 0x7fa02f714280>", "pid": 1304242, "tid": 1304242, "ts": 6595622950583.854, "dur": 0.581, "args": {"Python parent id": 1137, "Python id": 1138, "Ev Idx": 1137}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of list object at 0x7fa02f714280>", "pid": 1304242, "tid": 1304242, "ts": 6595622950588.601, "dur": 0.135, "args": {"Python parent id": 1137, "Python id": 1139, "Ev Idx": 1138}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(587): <listcomp>", "pid": 1304242, "tid": 1304242, "ts": 6595622950597.085, "dur": 1.898, "args": {"Python parent id": 1132, "Python id": 1140, "Ev Idx": 1139}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(587): <listcomp>", "pid": 1304242, "tid": 1304242, "ts": 6595622950601.292, "dur": 0.71, "args": {"Python parent id": 1132, "Python id": 1141, "Ev Idx": 1140}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(589): <listcomp>", "pid": 1304242, "tid": 1304242, "ts": 6595622950604.443, "dur": 0.815, "args": {"Python parent id": 1132, "Python id": 1142, "Ev Idx": 1141}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(3968): _eventual_warn_about_too_long_sequence", "pid": 1304242, "tid": 1304242, "ts": 6595622950608.094, "dur": 2.984, "args": {"Python parent id": 1132, "Python id": 1143, "Ev Idx": 1142}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595622950609.294, "dur": 0.452, "args": {"Python parent id": 1143, "Python id": 1144, "Ev Idx": 1143}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(220): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622950616.482, "dur": 39.273, "args": {"Python parent id": 1132, "Python id": 1145, "Ev Idx": 1144}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1091): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622950623.305, "dur": 23.14, "args": {"Python parent id": 1145, "Python id": 1146, "Ev Idx": 1145}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(991): update", "pid": 1304242, "tid": 1304242, "ts": 6595622950626.505, "dur": 18.516, "args": {"Python parent id": 1146, "Python id": 1147, "Ev Idx": 1146}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950628.155, "dur": 7.452, "args": {"Python parent id": 1147, "Python id": 1148, "Ev Idx": 1147}}, {"ph": "X", "cat": "python_function", "name": "abc.py(117): __instancecheck__", "pid": 1304242, "tid": 1304242, "ts": 6595622950630.464, "dur": 4.576, "args": {"Python parent id": 1148, "Python id": 1149, "Ev Idx": 1148}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _abc_instancecheck>", "pid": 1304242, "tid": 1304242, "ts": 6595622950631.587, "dur": 3.107, "args": {"Python parent id": 1149, "Python id": 1150, "Ev Idx": 1149}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1108): __setitem__", "pid": 1304242, "tid": 1304242, "ts": 6595622950638.178, "dur": 1.107, "args": {"Python parent id": 1147, "Python id": 1151, "Ev Idx": 1150}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1108): __setitem__", "pid": 1304242, "tid": 1304242, "ts": 6595622950640.501, "dur": 0.634, "args": {"Python parent id": 1147, "Python id": 1152, "Ev Idx": 1151}}, {"ph": "X", "cat": "python_function", "name": "<built-in method items of dict object at 0x7fa02f736d40>", "pid": 1304242, "tid": 1304242, "ts": 6595622950642.529, "dur": 0.485, "args": {"Python parent id": 1147, "Python id": 1153, "Ev Idx": 1152}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950647.745, "dur": 0.394, "args": {"Python parent id": 1145, "Python id": 1154, "Ev Idx": 1153}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(689): convert_to_tensors", "pid": 1304242, "tid": 1304242, "ts": 6595622950653.639, "dur": 0.925, "args": {"Python parent id": 1145, "Python id": 1155, "Ev Idx": 1154}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(840): items", "pid": 1304242, "tid": 1304242, "ts": 6595622950666.094, "dur": 4.172, "args": {"Python parent id": 1131, "Python id": 1156, "Ev Idx": 1155}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(862): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622950668.427, "dur": 1.384, "args": {"Python parent id": 1156, "Python id": 1157, "Ev Idx": 1156}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_fast.py(653): <dictcomp>", "pid": 1304242, "tid": 1304242, "ts": 6595622950673.594, "dur": 23.805, "args": {"Python parent id": 1131, "Python id": 1158, "Ev Idx": 1157}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(909): __iter__", "pid": 1304242, "tid": 1304242, "ts": 6595622950674.577, "dur": 10.673, "args": {"Python parent id": 1158, "Python id": 1159, "Ev Idx": 1158}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1114): __iter__", "pid": 1304242, "tid": 1304242, "ts": 6595622950677.017, "dur": 2.098, "args": {"Python parent id": 1159, "Python id": 1160, "Ev Idx": 1159}}, {"ph": "X", "cat": "python_function", "name": "<built-in function iter>", "pid": 1304242, "tid": 1304242, "ts": 6595622950678.38, "dur": 0.467, "args": {"Python parent id": 1160, "Python id": 1161, "Ev Idx": 1160}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(259): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595622950682.074, "dur": 2.215, "args": {"Python parent id": 1159, "Python id": 1162, "Ev Idx": 1161}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950683.325, "dur": 0.193, "args": {"Python parent id": 1162, "Python id": 1163, "Ev Idx": 1162}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595622950686.58, "dur": 0.208, "args": {"Python parent id": 1158, "Python id": 1164, "Ev Idx": 1163}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950688.515, "dur": 0.135, "args": {"Python parent id": 1158, "Python id": 1165, "Ev Idx": 1164}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(911): __iter__", "pid": 1304242, "tid": 1304242, "ts": 6595622950690.173, "dur": 2.699, "args": {"Python parent id": 1158, "Python id": 1166, "Ev Idx": 1165}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(259): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595622950691.449, "dur": 1.022, "args": {"Python parent id": 1166, "Python id": 1167, "Ev Idx": 1166}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950692.03, "dur": 0.103, "args": {"Python parent id": 1167, "Python id": 1168, "Ev Idx": 1167}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595622950693.722, "dur": 0.183, "args": {"Python parent id": 1158, "Python id": 1169, "Ev Idx": 1168}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950694.547, "dur": 0.097, "args": {"Python parent id": 1158, "Python id": 1170, "Ev Idx": 1169}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(911): __iter__", "pid": 1304242, "tid": 1304242, "ts": 6595622950695.494, "dur": 0.413, "args": {"Python parent id": 1158, "Python id": 1171, "Ev Idx": 1170}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(301): encodings", "pid": 1304242, "tid": 1304242, "ts": 6595622950699.957, "dur": 0.769, "args": {"Python parent id": 1131, "Python id": 1172, "Ev Idx": 1171}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(220): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622950702.741, "dur": 18.744, "args": {"Python parent id": 1131, "Python id": 1173, "Ev Idx": 1172}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1091): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622950704.916, "dur": 11.039, "args": {"Python parent id": 1173, "Python id": 1174, "Ev Idx": 1173}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(991): update", "pid": 1304242, "tid": 1304242, "ts": 6595622950706.312, "dur": 8.623, "args": {"Python parent id": 1174, "Python id": 1175, "Ev Idx": 1174}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950706.959, "dur": 3.036, "args": {"Python parent id": 1175, "Python id": 1176, "Ev Idx": 1175}}, {"ph": "X", "cat": "python_function", "name": "abc.py(117): __instancecheck__", "pid": 1304242, "tid": 1304242, "ts": 6595622950707.77, "dur": 1.927, "args": {"Python parent id": 1176, "Python id": 1177, "Ev Idx": 1176}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _abc_instancecheck>", "pid": 1304242, "tid": 1304242, "ts": 6595622950708.342, "dur": 1.145, "args": {"Python parent id": 1177, "Python id": 1178, "Ev Idx": 1177}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1108): __setitem__", "pid": 1304242, "tid": 1304242, "ts": 6595622950711.329, "dur": 0.548, "args": {"Python parent id": 1175, "Python id": 1179, "Ev Idx": 1178}}, {"ph": "X", "cat": "python_function", "name": "collections/__init__.py(1108): __setitem__", "pid": 1304242, "tid": 1304242, "ts": 6595622950712.807, "dur": 0.544, "args": {"Python parent id": 1175, "Python id": 1180, "Ev Idx": 1179}}, {"ph": "X", "cat": "python_function", "name": "<built-in method items of dict object at 0x7fa02f736d40>", "pid": 1304242, "tid": 1304242, "ts": 6595622950714.065, "dur": 0.274, "args": {"Python parent id": 1175, "Python id": 1181, "Ev Idx": 1180}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950717.022, "dur": 0.316, "args": {"Python parent id": 1173, "Python id": 1182, "Ev Idx": 1181}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(689): convert_to_tensors", "pid": 1304242, "tid": 1304242, "ts": 6595622950720.328, "dur": 0.43, "args": {"Python parent id": 1173, "Python id": 1183, "Ev Idx": 1182}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(259): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595622950726.108, "dur": 1.302, "args": {"Python parent id": 1131, "Python id": 1184, "Ev Idx": 1183}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950726.858, "dur": 0.12, "args": {"Python parent id": 1184, "Python id": 1185, "Ev Idx": 1184}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(3968): _eventual_warn_about_too_long_sequence", "pid": 1304242, "tid": 1304242, "ts": 6595622950728.727, "dur": 1.587, "args": {"Python parent id": 1131, "Python id": 1186, "Ev Idx": 1185}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595622950729.418, "dur": 0.197, "args": {"Python parent id": 1186, "Python id": 1187, "Ev Idx": 1186}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(3988): _switch_to_input_mode", "pid": 1304242, "tid": 1304242, "ts": 6595622950739.59, "dur": 0.273, "args": {"Python parent id": 1120, "Python id": 1188, "Ev Idx": 1187}}, {"ph": "X", "cat": "python_function", "name": "transformers/tokenization_utils_base.py(259): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595622950743.792, "dur": 1.136, "args": {"Python parent id": 1118, "Python id": 1189, "Ev Idx": 1188}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950744.479, "dur": 0.113, "args": {"Python parent id": 1189, "Python id": 1190, "Ev Idx": 1189}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(577): _validate_one_request", "pid": 1304242, "tid": 1304242, "ts": 6595622950748.179, "dur": 9.863, "args": {"Python parent id": 1118, "Python id": 1191, "Ev Idx": 1190}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595622950749.883, "dur": 0.202, "args": {"Python parent id": 1191, "Python id": 1192, "Ev Idx": 1191}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950752.057, "dur": 0.332, "args": {"Python parent id": 1191, "Python id": 1193, "Ev Idx": 1192}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595622950753.586, "dur": 0.421, "args": {"Python parent id": 1191, "Python id": 1194, "Ev Idx": 1193}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950756.015, "dur": 0.272, "args": {"Python parent id": 1191, "Python id": 1195, "Ev Idx": 1194}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(658): _create_tokenized_object", "pid": 1304242, "tid": 1304242, "ts": 6595622950760.703, "dur": 71.038, "args": {"Python parent id": 1118, "Python id": 1196, "Ev Idx": 1195}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(31): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622950766.076, "dur": 14.784, "args": {"Python parent id": 1196, "Python id": 1197, "Ev Idx": 1196}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(149): normalize", "pid": 1304242, "tid": 1304242, "ts": 6595622950783.547, "dur": 1.812, "args": {"Python parent id": 1196, "Python id": 1198, "Ev Idx": 1197}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(92): verify", "pid": 1304242, "tid": 1304242, "ts": 6595622950787.63, "dur": 19.721, "args": {"Python parent id": 1196, "Python id": 1199, "Ev Idx": 1198}}, {"ph": "X", "cat": "python_function", "name": "<built-in function sum>", "pid": 1304242, "tid": 1304242, "ts": 6595622950797.653, "dur": 8.818, "args": {"Python parent id": 1199, "Python id": 1200, "Ev Idx": 1199}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(146): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595622950799.945, "dur": 1.196, "args": {"Python parent id": 1200, "Python id": 1201, "Ev Idx": 1200}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(146): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595622950802.58, "dur": 0.586, "args": {"Python parent id": 1200, "Python id": 1202, "Ev Idx": 1201}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(146): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595622950804.206, "dur": 0.301, "args": {"Python parent id": 1200, "Python id": 1203, "Ev Idx": 1202}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/sampling/sampling_params.py(146): <genexpr>", "pid": 1304242, "tid": 1304242, "ts": 6595622950805.354, "dur": 0.499, "args": {"Python parent id": 1200, "Python id": 1204, "Ev Idx": 1203}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950808.614, "dur": 0.175, "args": {"Python parent id": 1196, "Python id": 1205, "Ev Idx": 1204}}, {"ph": "X", "cat": "python_function", "name": "<string>(2): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622950820.825, "dur": 8.891, "args": {"Python parent id": 1196, "Python id": 1206, "Ev Idx": 1205}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(762): _send_one_request", "pid": 1304242, "tid": 1304242, "ts": 6595622950841.359, "dur": 240.92, "args": {"Python parent id": 1083, "Python id": 1207, "Ev Idx": 1206}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/socket.py(933): send_pyobj", "pid": 1304242, "tid": 1304242, "ts": 6595622950845.005, "dur": 192.082, "args": {"Python parent id": 1207, "Python id": 1208, "Ev Idx": 1207}}, {"ph": "X", "cat": "python_function", "name": "<built-in function dumps>", "pid": 1304242, "tid": 1304242, "ts": 6595622950847.801, "dur": 78.339, "args": {"Python parent id": 1208, "Python id": 1209, "Ev Idx": 1208}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(317): send", "pid": 1304242, "tid": 1304242, "ts": 6595622950930.274, "dur": 105.332, "args": {"Python parent id": 1208, "Python id": 1210, "Ev Idx": 1209}}, {"ph": "X", "cat": "python_function", "name": "<built-in method update of dict object at 0x7fa02f737540>", "pid": 1304242, "tid": 1304242, "ts": 6595622950934.444, "dur": 1.479, "args": {"Python parent id": 1210, "Python id": 1211, "Ev Idx": 1210}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(525): _add_send_event", "pid": 1304242, "tid": 1304242, "ts": 6595622950938.573, "dur": 95.643, "args": {"Python parent id": 1210, "Python id": 1212, "Ev Idx": 1211}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595622950947.949, "dur": 0.424, "args": {"Python parent id": 1212, "Python id": 1213, "Ev Idx": 1212}}, {"ph": "X", "cat": "python_function", "name": "<built-in method copy of dict object at 0x7fa02f714bc0>", "pid": 1304242, "tid": 1304242, "ts": 6595622950949.436, "dur": 0.913, "args": {"Python parent id": 1212, "Python id": 1214, "Ev Idx": 1213}}, {"ph": "X", "cat": "python_function", "name": "enum.py(980): __or__", "pid": 1304242, "tid": 1304242, "ts": 6595622950954.256, "dur": 20.96, "args": {"Python parent id": 1212, "Python id": 1215, "Ev Idx": 1214}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595622950956.211, "dur": 1.776, "args": {"Python parent id": 1215, "Python id": 1216, "Ev Idx": 1215}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622950963.39, "dur": 6.448, "args": {"Python parent id": 1215, "Python id": 1217, "Ev Idx": 1216}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622950966.29, "dur": 3.001, "args": {"Python parent id": 1217, "Python id": 1218, "Ev Idx": 1217}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595622950972.387, "dur": 2.295, "args": {"Python parent id": 1215, "Python id": 1219, "Ev Idx": 1218}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595622950973.356, "dur": 1.062, "args": {"Python parent id": 1219, "Python id": 1220, "Ev Idx": 1219}}, {"ph": "X", "cat": "python_function", "name": "<built-in function getattr>", "pid": 1304242, "tid": 1304242, "ts": 6595622950977.182, "dur": 3.424, "args": {"Python parent id": 1212, "Python id": 1221, "Ev Idx": 1220}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/socket.py(623): send", "pid": 1304242, "tid": 1304242, "ts": 6595622950983.796, "dur": 43.568, "args": {"Python parent id": 1212, "Python id": 1222, "Ev Idx": 1221}}, {"ph": "X", "cat": "python_function", "name": "<built-in method set_result of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595622951031.965, "dur": 0.459, "args": {"Python parent id": 1212, "Python id": 1223, "Ev Idx": 1222}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(167): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622951045.036, "dur": 12.138, "args": {"Python parent id": 1207, "Python id": 1224, "Ev Idx": 1223}}, {"ph": "X", "cat": "python_function", "name": "asyncio/mixins.py(15): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622951050.211, "dur": 1.784, "args": {"Python parent id": 1224, "Python id": 1225, "Ev Idx": 1224}}, {"ph": "X", "cat": "python_function", "name": "<string>(2): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595622951062.302, "dur": 15.661, "args": {"Python parent id": 1207, "Python id": 1226, "Ev Idx": 1225}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(86): add", "pid": 1304242, "tid": 1304242, "ts": 6595622951089.872, "dur": 6.185, "args": {"Python parent id": 1083, "Python id": 1227, "Ev Idx": 1226}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add of set object at 0x7fa126a5dd20>", "pid": 1304242, "tid": 1304242, "ts": 6595622951094.478, "dur": 1.024, "args": {"Python parent id": 1227, "Python id": 1228, "Ev Idx": 1227}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(797): _wait_one_response", "pid": 1304242, "tid": 1304242, "ts": 6595622951099.242, "dur": 101.362, "args": {"Python parent id": 1083, "Python id": 1229, "Ev Idx": 1228}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(392): wait_for", "pid": 1304242, "tid": 1304242, "ts": 6595622951104.707, "dur": 95.351, "args": {"Python parent id": 1229, "Python id": 1230, "Ev Idx": 1229}}, {"ph": "X", "cat": "python_function", "name": "<built-in function get_running_loop>", "pid": 1304242, "tid": 1304242, "ts": 6595622951107.622, "dur": 2.52, "args": {"Python parent id": 1230, "Python id": 1231, "Ev Idx": 1230}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(610): ensure_future", "pid": 1304242, "tid": 1304242, "ts": 6595622951158.294, "dur": 37.671, "args": {"Python parent id": 1230, "Python id": 1232, "Ev Idx": 1231}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(618): _ensure_future", "pid": 1304242, "tid": 1304242, "ts": 6595622951160.722, "dur": 34.753, "args": {"Python parent id": 1232, "Python id": 1233, "Ev Idx": 1232}}, {"ph": "X", "cat": "python_function", "name": "asyncio/base_futures.py(14): isfuture", "pid": 1304242, "tid": 1304242, "ts": 6595622951163.944, "dur": 7.411, "args": {"Python parent id": 1233, "Python id": 1234, "Ev Idx": 1233}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595622951166.246, "dur": 4.631, "args": {"Python parent id": 1234, "Python id": 1235, "Ev Idx": 1234}}, {"ph": "X", "cat": "python_function", "name": "asyncio/coroutines.py(177): iscoroutine", "pid": 1304242, "tid": 1304242, "ts": 6595622951173.733, "dur": 2.102, "args": {"Python parent id": 1233, "Python id": 1236, "Ev Idx": 1235}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(86): add", "pid": 1304242, "tid": 1304242, "ts": 6595622951190.879, "dur": 3.207, "args": {"Python parent id": 1233, "Python id": 1237, "Ev Idx": 1236}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add of set object at 0x7fa126a5dd20>", "pid": 1304242, "tid": 1304242, "ts": 6595622951193.256, "dur": 0.461, "args": {"Python parent id": 1237, "Python id": 1238, "Ev Idx": 1237}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add_done_callback of _asyncio.Task object at 0x7fa02f684c70>", "pid": 1304242, "tid": 1304242, "ts": 6595622951197.738, "dur": 0.601, "args": {"Python parent id": 1230, "Python id": 1239, "Ev Idx": 1238}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(862): _wait_one_response", "pid": 1304242, "tid": 1304242, "ts": 6595622951242.022, "dur": 1.04, "args": {"Python parent id": 1060, "Python id": 1240, "Ev Idx": 1239}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(39): _remove", "pid": 1304242, "tid": 1304242, "ts": 6595622951254.291, "dur": 4.511, "args": {"Python parent id": 1060, "Python id": 1241, "Ev Idx": 1240}}, {"ph": "X", "cat": "python_function", "name": "<built-in method discard of set object at 0x7fa126a5e180>", "pid": 1304242, "tid": 1304242, "ts": 6595622951257.585, "dur": 0.977, "args": {"Python parent id": 1241, "Python id": 1242, "Ev Idx": 1241}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(201): wait", "pid": 1304242, "tid": 1304242, "ts": 6595622951263.225, "dur": 18.531, "args": {"Python parent id": 1060, "Python id": 1243, "Ev Idx": 1242}}, {"ph": "X", "cat": "python_function", "name": "asyncio/mixins.py(22): _get_loop", "pid": 1304242, "tid": 1304242, "ts": 6595622951265.567, "dur": 11.488, "args": {"Python parent id": 1243, "Python id": 1244, "Ev Idx": 1243}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _get_running_loop>", "pid": 1304242, "tid": 1304242, "ts": 6595622951266.907, "dur": 1.668, "args": {"Python parent id": 1244, "Python id": 1245, "Ev Idx": 1244}}, {"ph": "X", "cat": "python_function", "name": "<built-in method __exit__ of _thread.lock object at 0x7fa1262bc680>", "pid": 1304242, "tid": 1304242, "ts": 6595622951274.99, "dur": 0.875, "args": {"Python parent id": 1244, "Python id": 1246, "Ev Idx": 1245}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of collections.deque object at 0x7fa02f673580>", "pid": 1304242, "tid": 1304242, "ts": 6595622951280.423, "dur": 0.223, "args": {"Python parent id": 1243, "Python id": 1247, "Ev Idx": 1246}}, {"ph": "X", "cat": "python_function", "name": "zmq/asyncio.py(151): <lambda>", "pid": 1304242, "tid": 1304242, "ts": 6595623098510.806, "dur": 256.542, "args": {"Python parent id": 1060, "Python id": 1248, "Ev Idx": 1247}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(670): _handle_events", "pid": 1304242, "tid": 1304242, "ts": 6595623098520.08, "dur": 246.515, "args": {"Python parent id": 1248, "Python id": 1249, "Ev Idx": 1248}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623098538.205, "dur": 12.886, "args": {"Python parent id": 1249, "Python id": 1250, "Ev Idx": 1249}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623098543.28, "dur": 6.693, "args": {"Python parent id": 1250, "Python id": 1251, "Ev Idx": 1250}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595623098588.599, "dur": 22.524, "args": {"Python parent id": 1249, "Python id": 1252, "Ev Idx": 1251}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623098593.508, "dur": 4.571, "args": {"Python parent id": 1252, "Python id": 1253, "Ev Idx": 1252}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623098602.703, "dur": 3.689, "args": {"Python parent id": 1252, "Python id": 1254, "Ev Idx": 1253}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623098604.327, "dur": 1.639, "args": {"Python parent id": 1254, "Python id": 1255, "Ev Idx": 1254}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623098608.739, "dur": 1.998, "args": {"Python parent id": 1252, "Python id": 1256, "Ev Idx": 1255}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623098609.697, "dur": 0.745, "args": {"Python parent id": 1256, "Python id": 1257, "Ev Idx": 1256}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(586): _handle_recv", "pid": 1304242, "tid": 1304242, "ts": 6595623098614.017, "dur": 137.299, "args": {"Python parent id": 1249, "Python id": 1258, "Ev Idx": 1257}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623098616.516, "dur": 2.029, "args": {"Python parent id": 1258, "Python id": 1259, "Ev Idx": 1258}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623098617.473, "dur": 0.825, "args": {"Python parent id": 1259, "Python id": 1260, "Ev Idx": 1259}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595623098625.602, "dur": 8.405, "args": {"Python parent id": 1258, "Python id": 1261, "Ev Idx": 1260}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623098626.744, "dur": 0.584, "args": {"Python parent id": 1261, "Python id": 1262, "Ev Idx": 1261}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623098628.809, "dur": 1.945, "args": {"Python parent id": 1261, "Python id": 1263, "Ev Idx": 1262}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623098629.694, "dur": 0.855, "args": {"Python parent id": 1263, "Python id": 1264, "Ev Idx": 1263}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623098631.979, "dur": 1.762, "args": {"Python parent id": 1261, "Python id": 1265, "Ev Idx": 1264}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623098632.804, "dur": 0.722, "args": {"Python parent id": 1265, "Python id": 1266, "Ev Idx": 1265}}, {"ph": "X", "cat": "python_function", "name": "<built-in method popleft of collections.deque object at 0x7fa02f673580>", "pid": 1304242, "tid": 1304242, "ts": 6595623098638.191, "dur": 0.776, "args": {"Python parent id": 1258, "Python id": 1267, "Ev Idx": 1266}}, {"ph": "X", "cat": "python_function", "name": "<built-in method done of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595623098643.366, "dur": 0.765, "args": {"Python parent id": 1258, "Python id": 1268, "Ev Idx": 1267}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(704): _drop_io_state", "pid": 1304242, "tid": 1304242, "ts": 6595623098646.774, "dur": 45.483, "args": {"Python parent id": 1258, "Python id": 1269, "Ev Idx": 1268}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595623098650.015, "dur": 7.836, "args": {"Python parent id": 1269, "Python id": 1270, "Ev Idx": 1269}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623098650.852, "dur": 0.183, "args": {"Python parent id": 1270, "Python id": 1271, "Ev Idx": 1270}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623098652.606, "dur": 1.857, "args": {"Python parent id": 1270, "Python id": 1272, "Ev Idx": 1271}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623098653.498, "dur": 0.647, "args": {"Python parent id": 1272, "Python id": 1273, "Ev Idx": 1272}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623098655.784, "dur": 1.793, "args": {"Python parent id": 1270, "Python id": 1274, "Ev Idx": 1273}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623098656.598, "dur": 0.795, "args": {"Python parent id": 1274, "Python id": 1275, "Ev Idx": 1274}}, {"ph": "X", "cat": "python_function", "name": "enum.py(1000): __invert__", "pid": 1304242, "tid": 1304242, "ts": 6595623098661.197, "dur": 4.931, "args": {"Python parent id": 1269, "Python id": 1276, "Ev Idx": 1275}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623098663.268, "dur": 2.499, "args": {"Python parent id": 1276, "Python id": 1277, "Ev Idx": 1276}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623098664.316, "dur": 1.01, "args": {"Python parent id": 1277, "Python id": 1278, "Ev Idx": 1277}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595623098667.346, "dur": 8.31, "args": {"Python parent id": 1269, "Python id": 1279, "Ev Idx": 1278}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623098668.133, "dur": 0.187, "args": {"Python parent id": 1279, "Python id": 1280, "Ev Idx": 1279}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623098669.385, "dur": 1.484, "args": {"Python parent id": 1279, "Python id": 1281, "Ev Idx": 1280}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623098670.338, "dur": 0.341, "args": {"Python parent id": 1281, "Python id": 1282, "Ev Idx": 1281}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623098673.486, "dur": 1.899, "args": {"Python parent id": 1279, "Python id": 1283, "Ev Idx": 1282}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623098674.305, "dur": 0.897, "args": {"Python parent id": 1283, "Python id": 1284, "Ev Idx": 1283}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/socket.py(376): __setattr__", "pid": 1304242, "tid": 1304242, "ts": 6595623098677.931, "dur": 7.646, "args": {"Python parent id": 1269, "Python id": 1285, "Ev Idx": 1284}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(710): _update_handler", "pid": 1304242, "tid": 1304242, "ts": 6595623098687.674, "dur": 4.225, "args": {"Python parent id": 1269, "Python id": 1286, "Ev Idx": 1285}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(682): _schedule_remaining_events", "pid": 1304242, "tid": 1304242, "ts": 6595623098689.839, "dur": 1.614, "args": {"Python parent id": 1286, "Python id": 1287, "Ev Idx": 1286}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(193): cancel", "pid": 1304242, "tid": 1304242, "ts": 6595623098695.407, "dur": 0.537, "args": {"Python parent id": 1258, "Python id": 1288, "Ev Idx": 1287}}, {"ph": "X", "cat": "python_function", "name": "enum.py(980): __or__", "pid": 1304242, "tid": 1304242, "ts": 6595623098704.241, "dur": 12.78, "args": {"Python parent id": 1258, "Python id": 1289, "Ev Idx": 1288}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623098706.309, "dur": 0.663, "args": {"Python parent id": 1289, "Python id": 1290, "Ev Idx": 1289}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623098709.536, "dur": 2.784, "args": {"Python parent id": 1289, "Python id": 1291, "Ev Idx": 1290}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623098710.797, "dur": 1.222, "args": {"Python parent id": 1291, "Python id": 1292, "Ev Idx": 1291}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623098714.586, "dur": 1.962, "args": {"Python parent id": 1289, "Python id": 1293, "Ev Idx": 1292}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623098715.502, "dur": 0.84, "args": {"Python parent id": 1293, "Python id": 1294, "Ev Idx": 1293}}, {"ph": "X", "cat": "python_function", "name": "<built-in method set_result of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595623098734.167, "dur": 16.571, "args": {"Python parent id": 1258, "Python id": 1295, "Ev Idx": 1294}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595623098754.083, "dur": 9.287, "args": {"Python parent id": 1249, "Python id": 1296, "Ev Idx": 1295}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623098755.259, "dur": 0.468, "args": {"Python parent id": 1296, "Python id": 1297, "Ev Idx": 1296}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623098757.261, "dur": 2.288, "args": {"Python parent id": 1296, "Python id": 1298, "Ev Idx": 1297}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623098758.381, "dur": 0.853, "args": {"Python parent id": 1298, "Python id": 1299, "Ev Idx": 1298}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623098761.15, "dur": 1.908, "args": {"Python parent id": 1296, "Python id": 1300, "Ev Idx": 1299}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623098762.067, "dur": 0.793, "args": {"Python parent id": 1300, "Python id": 1301, "Ev Idx": 1300}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(682): _schedule_remaining_events", "pid": 1304242, "tid": 1304242, "ts": 6595623098765.292, "dur": 0.702, "args": {"Python parent id": 1249, "Python id": 1302, "Ev Idx": 1301}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(452): _remove_finished_future", "pid": 1304242, "tid": 1304242, "ts": 6595623098780.185, "dur": 0.724, "args": {"Python parent id": 1060, "Python id": 1303, "Ev Idx": 1302}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(341): _chain", "pid": 1304242, "tid": 1304242, "ts": 6595623098788.054, "dur": 101.38, "args": {"Python parent id": 1060, "Python id": 1304, "Ev Idx": 1303}}, {"ph": "X", "cat": "python_function", "name": "<built-in method done of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595623098789.389, "dur": 0.447, "args": {"Python parent id": 1304, "Python id": 1305, "Ev Idx": 1304}}, {"ph": "X", "cat": "python_function", "name": "<built-in method exception of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595623098790.592, "dur": 0.261, "args": {"Python parent id": 1304, "Python id": 1306, "Ev Idx": 1305}}, {"ph": "X", "cat": "python_function", "name": "<built-in method result of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595623098791.918, "dur": 0.175, "args": {"Python parent id": 1304, "Python id": 1307, "Ev Idx": 1306}}, {"ph": "X", "cat": "python_function", "name": "<built-in function loads>", "pid": 1304242, "tid": 1304242, "ts": 6595623098793.055, "dur": 89.755, "args": {"Python parent id": 1304, "Python id": 1308, "Ev Idx": 1307}}, {"ph": "X", "cat": "python_function", "name": "<built-in method set_result of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595623098884.625, "dur": 4.368, "args": {"Python parent id": 1304, "Python id": 1309, "Ev Idx": 1308}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(368): _chain_cancel", "pid": 1304242, "tid": 1304242, "ts": 6595623098902.367, "dur": 2.002, "args": {"Python parent id": 1060, "Python id": 1310, "Ev Idx": 1309}}, {"ph": "X", "cat": "python_function", "name": "<built-in method done of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595623098903.608, "dur": 0.292, "args": {"Python parent id": 1310, "Python id": 1311, "Ev Idx": 1310}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(2071): print_exception_wrapper", "pid": 1304242, "tid": 1304242, "ts": 6595623098913.042, "dur": 342.356, "args": {"Python parent id": 1060, "Python id": 1312, "Ev Idx": 1311}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(1581): handle_loop", "pid": 1304242, "tid": 1304242, "ts": 6595623098914.997, "dur": 339.897, "args": {"Python parent id": 1312, "Python id": 1313, "Ev Idx": 1312}}, {"ph": "X", "cat": "python_function", "name": "sglang/utils.py(475): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623098923.229, "dur": 73.391, "args": {"Python parent id": 1313, "Python id": 1314, "Ev Idx": 1313}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623098926.768, "dur": 0.28, "args": {"Python parent id": 1314, "Python id": 1315, "Ev Idx": 1314}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(1585): _handle_batch_output", "pid": 1304242, "tid": 1304242, "ts": 6595623098929.256, "dur": 66.381, "args": {"Python parent id": 1314, "Python id": 1316, "Ev Idx": 1315}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595623098934.934, "dur": 1.314, "args": {"Python parent id": 1316, "Python id": 1317, "Ev Idx": 1316}}, {"ph": "X", "cat": "python_function", "name": "<built-in function getattr>", "pid": 1304242, "tid": 1304242, "ts": 6595623098944.825, "dur": 1.08, "args": {"Python parent id": 1316, "Python id": 1318, "Ev Idx": 1317}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623098947.199, "dur": 0.917, "args": {"Python parent id": 1316, "Python id": 1319, "Ev Idx": 1318}}, {"ph": "X", "cat": "python_function", "name": "<built-in method update of dict object at 0x7fa02f737540>", "pid": 1304242, "tid": 1304242, "ts": 6595623098950.922, "dur": 2.014, "args": {"Python parent id": 1316, "Python id": 1320, "Ev Idx": 1319}}, {"ph": "X", "cat": "python_function", "name": "<built-in function getattr>", "pid": 1304242, "tid": 1304242, "ts": 6595623098954.689, "dur": 0.526, "args": {"Python parent id": 1316, "Python id": 1321, "Ev Idx": 1320}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623098956.481, "dur": 0.164, "args": {"Python parent id": 1316, "Python id": 1322, "Ev Idx": 1321}}, {"ph": "X", "cat": "python_function", "name": "<built-in method extend of list object at 0x7fa03508f040>", "pid": 1304242, "tid": 1304242, "ts": 6595623098961.837, "dur": 0.933, "args": {"Python parent id": 1316, "Python id": 1323, "Ev Idx": 1322}}, {"ph": "X", "cat": "python_function", "name": "<built-in method copy of list object at 0x7fa03508f040>", "pid": 1304242, "tid": 1304242, "ts": 6595623098964.003, "dur": 0.89, "args": {"Python parent id": 1316, "Python id": 1324, "Ev Idx": 1323}}, {"ph": "X", "cat": "python_function", "name": "<built-in function time>", "pid": 1304242, "tid": 1304242, "ts": 6595623098971.844, "dur": 0.959, "args": {"Python parent id": 1316, "Python id": 1325, "Ev Idx": 1324}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of list object at 0x7fa02f714280>", "pid": 1304242, "tid": 1304242, "ts": 6595623098978.685, "dur": 0.504, "args": {"Python parent id": 1316, "Python id": 1326, "Ev Idx": 1325}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(183): set", "pid": 1304242, "tid": 1304242, "ts": 6595623098983.131, "dur": 8.224, "args": {"Python parent id": 1316, "Python id": 1327, "Ev Idx": 1326}}, {"ph": "X", "cat": "python_function", "name": "<built-in method done of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595623098987.017, "dur": 0.401, "args": {"Python parent id": 1327, "Python id": 1328, "Ev Idx": 1327}}, {"ph": "X", "cat": "python_function", "name": "<built-in method set_result of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595623098988.265, "dur": 1.765, "args": {"Python parent id": 1327, "Python id": 1329, "Ev Idx": 1328}}, {"ph": "X", "cat": "python_function", "name": "<built-in function time>", "pid": 1304242, "tid": 1304242, "ts": 6595623098998.257, "dur": 0.323, "args": {"Python parent id": 1313, "Python id": 1330, "Ev Idx": 1329}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/socket.py(961): recv_pyobj", "pid": 1304242, "tid": 1304242, "ts": 6595623099002.287, "dur": 249.903, "args": {"Python parent id": 1313, "Python id": 1331, "Ev Idx": 1330}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(281): recv", "pid": 1304242, "tid": 1304242, "ts": 6595623099004.464, "dur": 228.561, "args": {"Python parent id": 1331, "Python id": 1332, "Ev Idx": 1331}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(470): _add_recv_event", "pid": 1304242, "tid": 1304242, "ts": 6595623099009.215, "dur": 223.085, "args": {"Python parent id": 1332, "Python id": 1333, "Ev Idx": 1332}}, {"ph": "X", "cat": "python_function", "name": "<built-in method startswith of str object at 0x7fa2cbeb2070>", "pid": 1304242, "tid": 1304242, "ts": 6595623099017.675, "dur": 2.391, "args": {"Python parent id": 1333, "Python id": 1334, "Ev Idx": 1333}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595623099021.078, "dur": 0.617, "args": {"Python parent id": 1333, "Python id": 1335, "Ev Idx": 1334}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595623099023.932, "dur": 13.745, "args": {"Python parent id": 1333, "Python id": 1336, "Ev Idx": 1335}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623099025.095, "dur": 1.211, "args": {"Python parent id": 1336, "Python id": 1337, "Ev Idx": 1336}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623099030.23, "dur": 3.102, "args": {"Python parent id": 1336, "Python id": 1338, "Ev Idx": 1337}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623099031.774, "dur": 1.323, "args": {"Python parent id": 1338, "Python id": 1339, "Ev Idx": 1338}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623099035.448, "dur": 1.891, "args": {"Python parent id": 1336, "Python id": 1340, "Ev Idx": 1339}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623099036.357, "dur": 0.779, "args": {"Python parent id": 1340, "Python id": 1341, "Ev Idx": 1340}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595623099039.95, "dur": 1.561, "args": {"Python parent id": 1333, "Python id": 1342, "Ev Idx": 1341}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/attrsettr.py(43): __getattr__", "pid": 1304242, "tid": 1304242, "ts": 6595623099051.746, "dur": 33.97, "args": {"Python parent id": 1333, "Python id": 1343, "Ev Idx": 1342}}, {"ph": "X", "cat": "python_function", "name": "<built-in method upper of str object at 0x7fa1224219f0>", "pid": 1304242, "tid": 1304242, "ts": 6595623099053.25, "dur": 1.372, "args": {"Python parent id": 1343, "Python id": 1344, "Ev Idx": 1343}}, {"ph": "X", "cat": "python_function", "name": "<built-in function getattr>", "pid": 1304242, "tid": 1304242, "ts": 6595623099056.261, "dur": 1.692, "args": {"Python parent id": 1343, "Python id": 1345, "Ev Idx": 1344}}, {"ph": "X", "cat": "python_function", "name": "<frozen importlib._bootstrap>(1053): _handle_fromlist", "pid": 1304242, "tid": 1304242, "ts": 6595623099064.498, "dur": 5.306, "args": {"Python parent id": 1343, "Python id": 1346, "Ev Idx": 1345}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623099066.31, "dur": 0.183, "args": {"Python parent id": 1346, "Python id": 1347, "Ev Idx": 1346}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595623099067.888, "dur": 0.838, "args": {"Python parent id": 1346, "Python id": 1348, "Ev Idx": 1347}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/attrsettr.py(66): _get_attr_opt", "pid": 1304242, "tid": 1304242, "ts": 6595623099073.051, "dur": 11.758, "args": {"Python parent id": 1343, "Python id": 1349, "Ev Idx": 1348}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623099076.525, "dur": 3.036, "args": {"Python parent id": 1349, "Python id": 1350, "Ev Idx": 1349}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623099077.689, "dur": 1.562, "args": {"Python parent id": 1350, "Python id": 1351, "Ev Idx": 1350}}, {"ph": "X", "cat": "python_function", "name": "<string>(1): <lambda>", "pid": 1304242, "tid": 1304242, "ts": 6595623099093.045, "dur": 3.692, "args": {"Python parent id": 1333, "Python id": 1352, "Ev Idx": 1351}}, {"ph": "X", "cat": "python_function", "name": "<built-in method __new__ of type object at 0x57ea91926320>", "pid": 1304242, "tid": 1304242, "ts": 6595623099094.866, "dur": 1.403, "args": {"Python parent id": 1352, "Python id": 1353, "Ev Idx": 1352}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of collections.deque object at 0x7fa02f673580>", "pid": 1304242, "tid": 1304242, "ts": 6595623099099.589, "dur": 0.26, "args": {"Python parent id": 1333, "Python id": 1354, "Ev Idx": 1353}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623099102.579, "dur": 2.735, "args": {"Python parent id": 1333, "Python id": 1355, "Ev Idx": 1354}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623099103.947, "dur": 1.095, "args": {"Python parent id": 1355, "Python id": 1356, "Ev Idx": 1355}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595623099114.627, "dur": 9.176, "args": {"Python parent id": 1333, "Python id": 1357, "Ev Idx": 1356}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623099115.619, "dur": 0.847, "args": {"Python parent id": 1357, "Python id": 1358, "Ev Idx": 1357}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623099117.812, "dur": 2.257, "args": {"Python parent id": 1357, "Python id": 1359, "Ev Idx": 1358}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623099118.866, "dur": 0.916, "args": {"Python parent id": 1359, "Python id": 1360, "Ev Idx": 1359}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623099121.684, "dur": 1.776, "args": {"Python parent id": 1357, "Python id": 1361, "Ev Idx": 1360}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623099122.546, "dur": 0.704, "args": {"Python parent id": 1361, "Python id": 1362, "Ev Idx": 1361}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add_done_callback of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595623099130.384, "dur": 41.346, "args": {"Python parent id": 1333, "Python id": 1363, "Ev Idx": 1362}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(698): _add_io_state", "pid": 1304242, "tid": 1304242, "ts": 6595623099174.661, "dur": 57.038, "args": {"Python parent id": 1333, "Python id": 1364, "Ev Idx": 1363}}, {"ph": "X", "cat": "python_function", "name": "enum.py(980): __or__", "pid": 1304242, "tid": 1304242, "ts": 6595623099177.241, "dur": 10.875, "args": {"Python parent id": 1364, "Python id": 1365, "Ev Idx": 1364}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623099178.911, "dur": 0.314, "args": {"Python parent id": 1365, "Python id": 1366, "Ev Idx": 1365}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623099181.145, "dur": 1.829, "args": {"Python parent id": 1365, "Python id": 1367, "Ev Idx": 1366}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623099182.099, "dur": 0.477, "args": {"Python parent id": 1367, "Python id": 1368, "Ev Idx": 1367}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623099185.332, "dur": 2.336, "args": {"Python parent id": 1365, "Python id": 1369, "Ev Idx": 1368}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623099186.209, "dur": 1.256, "args": {"Python parent id": 1369, "Python id": 1370, "Ev Idx": 1369}}, {"ph": "X", "cat": "python_function", "name": "zmq/sugar/socket.py(376): __setattr__", "pid": 1304242, "tid": 1304242, "ts": 6595623099189.79, "dur": 4.07, "args": {"Python parent id": 1364, "Python id": 1371, "Ev Idx": 1370}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(710): _update_handler", "pid": 1304242, "tid": 1304242, "ts": 6595623099195.702, "dur": 35.413, "args": {"Python parent id": 1364, "Python id": 1372, "Ev Idx": 1371}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(49): _get_loop", "pid": 1304242, "tid": 1304242, "ts": 6595623099197.172, "dur": 8.05, "args": {"Python parent id": 1372, "Python id": 1373, "Ev Idx": 1372}}, {"ph": "X", "cat": "python_function", "name": "zmq/asyncio.py(106): _default_loop", "pid": 1304242, "tid": 1304242, "ts": 6595623099199.345, "dur": 4.636, "args": {"Python parent id": 1373, "Python id": 1374, "Ev Idx": 1373}}, {"ph": "X", "cat": "python_function", "name": "<built-in function get_running_loop>", "pid": 1304242, "tid": 1304242, "ts": 6595623099201.765, "dur": 1.945, "args": {"Python parent id": 1374, "Python id": 1375, "Ev Idx": 1374}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(682): _schedule_remaining_events", "pid": 1304242, "tid": 1304242, "ts": 6595623099207.038, "dur": 23.511, "args": {"Python parent id": 1372, "Python id": 1376, "Ev Idx": 1375}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623099210.526, "dur": 2.422, "args": {"Python parent id": 1376, "Python id": 1377, "Ev Idx": 1376}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623099211.508, "dur": 1.143, "args": {"Python parent id": 1377, "Python id": 1378, "Ev Idx": 1377}}, {"ph": "X", "cat": "python_function", "name": "enum.py(986): __and__", "pid": 1304242, "tid": 1304242, "ts": 6595623099220.427, "dur": 9.476, "args": {"Python parent id": 1376, "Python id": 1379, "Ev Idx": 1378}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623099221.272, "dur": 0.807, "args": {"Python parent id": 1379, "Python id": 1380, "Ev Idx": 1379}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623099223.287, "dur": 2.611, "args": {"Python parent id": 1379, "Python id": 1381, "Ev Idx": 1380}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623099224.415, "dur": 1.28, "args": {"Python parent id": 1381, "Python id": 1382, "Ev Idx": 1381}}, {"ph": "X", "cat": "python_function", "name": "enum.py(359): __call__", "pid": 1304242, "tid": 1304242, "ts": 6595623099227.489, "dur": 2.053, "args": {"Python parent id": 1379, "Python id": 1383, "Ev Idx": 1382}}, {"ph": "X", "cat": "python_function", "name": "enum.py(678): __new__", "pid": 1304242, "tid": 1304242, "ts": 6595623099228.522, "dur": 0.814, "args": {"Python parent id": 1383, "Python id": 1384, "Ev Idx": 1383}}, {"ph": "X", "cat": "python_function", "name": "zmq/_future.py(337): _deserialize", "pid": 1304242, "tid": 1304242, "ts": 6595623099238.057, "dur": 13.016, "args": {"Python parent id": 1331, "Python id": 1385, "Ev Idx": 1384}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add_done_callback of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595623099246.565, "dur": 1.452, "args": {"Python parent id": 1385, "Python id": 1386, "Ev Idx": 1385}}, {"ph": "X", "cat": "python_function", "name": "<built-in method add_done_callback of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595623099250.093, "dur": 0.341, "args": {"Python parent id": 1385, "Python id": 1387, "Ev Idx": 1386}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(214): wait", "pid": 1304242, "tid": 1304242, "ts": 6595623099272.807, "dur": 4.794, "args": {"Python parent id": 1060, "Python id": 1388, "Ev Idx": 1387}}, {"ph": "X", "cat": "python_function", "name": "<built-in method remove of collections.deque object at 0x7fa034862200>", "pid": 1304242, "tid": 1304242, "ts": 6595623099275.829, "dur": 1.11, "args": {"Python parent id": 1388, "Python id": 1389, "Ev Idx": 1388}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(387): _release_waiter", "pid": 1304242, "tid": 1304242, "ts": 6595623099292.709, "dur": 4.897, "args": {"Python parent id": 1060, "Python id": 1390, "Ev Idx": 1389}}, {"ph": "X", "cat": "python_function", "name": "<built-in method done of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595623099294.172, "dur": 0.373, "args": {"Python parent id": 1390, "Python id": 1391, "Ev Idx": 1390}}, {"ph": "X", "cat": "python_function", "name": "<built-in method set_result of _asyncio.Future object at 0x7fa02efc4670>", "pid": 1304242, "tid": 1304242, "ts": 6595623099295.799, "dur": 1.48, "args": {"Python parent id": 1390, "Python id": 1392, "Ev Idx": 1391}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(508): generate_request", "pid": 1304242, "tid": 1304242, "ts": 6595623099306.487, "dur": 38.002, "args": {"Python parent id": 1060, "Python id": 1393, "Ev Idx": 1392}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(806): _wait_one_response", "pid": 1304242, "tid": 1304242, "ts": 6595623099308.002, "dur": 32.895, "args": {"Python parent id": 1393, "Python id": 1394, "Ev Idx": 1393}}, {"ph": "X", "cat": "python_function", "name": "asyncio/tasks.py(432): wait_for", "pid": 1304242, "tid": 1304242, "ts": 6595623099309.764, "dur": 8.961, "args": {"Python parent id": 1394, "Python id": 1395, "Ev Idx": 1394}}, {"ph": "X", "cat": "python_function", "name": "<built-in method done of _asyncio.Task object at 0x7fa02f684c70>", "pid": 1304242, "tid": 1304242, "ts": 6595623099311.883, "dur": 0.364, "args": {"Python parent id": 1395, "Python id": 1396, "Ev Idx": 1395}}, {"ph": "X", "cat": "python_function", "name": "<built-in method result of _asyncio.Task object at 0x7fa02f684c70>", "pid": 1304242, "tid": 1304242, "ts": 6595623099312.926, "dur": 0.259, "args": {"Python parent id": 1395, "Python id": 1397, "Ev Idx": 1396}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(39): _remove", "pid": 1304242, "tid": 1304242, "ts": 6595623099321.156, "dur": 6.077, "args": {"Python parent id": 1394, "Python id": 1398, "Ev Idx": 1397}}, {"ph": "X", "cat": "python_function", "name": "<built-in method discard of set object at 0x7fa126a5e180>", "pid": 1304242, "tid": 1304242, "ts": 6595623099325.632, "dur": 1.275, "args": {"Python parent id": 1398, "Python id": 1399, "Ev Idx": 1398}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595623099335.164, "dur": 0.576, "args": {"Python parent id": 1394, "Python id": 1400, "Ev Idx": 1399}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623099336.667, "dur": 0.234, "args": {"Python parent id": 1394, "Python id": 1401, "Ev Idx": 1400}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595623099337.981, "dur": 0.508, "args": {"Python parent id": 1394, "Python id": 1402, "Ev Idx": 1401}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595623099339.451, "dur": 0.504, "args": {"Python parent id": 1394, "Python id": 1403, "Ev Idx": 1402}}, {"ph": "X", "cat": "python_function", "name": "weakref.py(134): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595623099385.099, "dur": 3.785, "args": {"Python parent id": 1060, "Python id": 1404, "Ev Idx": 1403}}, {"ph": "X", "cat": "python_function", "name": "socket.py(488): _decref_socketios", "pid": 1304242, "tid": 1304242, "ts": 6595623099394.297, "dur": 4.154, "args": {"Python parent id": 1060, "Python id": 1405, "Ev Idx": 1404}}, {"ph": "X", "cat": "python_function", "name": "socket.py(498): close", "pid": 1304242, "tid": 1304242, "ts": 6595623099418.873, "dur": 24.081, "args": {"Python parent id": 1060, "Python id": 1406, "Ev Idx": 1405}}, {"ph": "X", "cat": "python_function", "name": "socket.py(494): _real_close", "pid": 1304242, "tid": 1304242, "ts": 6595623099421.539, "dur": 20.678, "args": {"Python parent id": 1406, "Python id": 1407, "Ev Idx": 1406}}, {"ph": "X", "cat": "python_function", "name": "<built-in method close of socket object at 0x7fa02f6c7880>", "pid": 1304242, "tid": 1304242, "ts": 6595623099423.205, "dur": 18.368, "args": {"Python parent id": 1407, "Python id": 1408, "Ev Idx": 1407}}, {"ph": "X", "cat": "python_function", "name": "socket.py(498): close", "pid": 1304242, "tid": 1304242, "ts": 6595623099445.037, "dur": 16.4, "args": {"Python parent id": 1060, "Python id": 1409, "Ev Idx": 1408}}, {"ph": "X", "cat": "python_function", "name": "socket.py(494): _real_close", "pid": 1304242, "tid": 1304242, "ts": 6595623099446.124, "dur": 14.895, "args": {"Python parent id": 1409, "Python id": 1410, "Ev Idx": 1409}}, {"ph": "X", "cat": "python_function", "name": "<built-in method close of socket object at 0x7fa02f6c7880>", "pid": 1304242, "tid": 1304242, "ts": 6595623099446.725, "dur": 14.019, "args": {"Python parent id": 1410, "Python id": 1411, "Ev Idx": 1410}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(39): _remove", "pid": 1304242, "tid": 1304242, "ts": 6595623099478.368, "dur": 3.734, "args": {"Python parent id": 1060, "Python id": 1412, "Ev Idx": 1411}}, {"ph": "X", "cat": "python_function", "name": "<built-in method discard of set object at 0x7fa126a5e180>", "pid": 1304242, "tid": 1304242, "ts": 6595623099480.899, "dur": 0.977, "args": {"Python parent id": 1412, "Python id": 1413, "Ev Idx": 1412}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/managers/tokenizer_manager.py(509): generate_request", "pid": 1304242, "tid": 1304242, "ts": 6595623099487.999, "dur": 70.751, "args": {"Python parent id": 9, "Python id": 1414, "Ev Idx": 1413}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(39): _remove", "pid": 1304242, "tid": 1304242, "ts": 6595623099491.026, "dur": 1.828, "args": {"Python parent id": 1414, "Python id": 1415, "Ev Idx": 1414}}, {"ph": "X", "cat": "python_function", "name": "<built-in method discard of set object at 0x7fa126a5e180>", "pid": 1304242, "tid": 1304242, "ts": 6595623099492.409, "dur": 0.158, "args": {"Python parent id": 1415, "Python id": 1416, "Ev Idx": 1415}}, {"ph": "X", "cat": "python_function", "name": "_weakrefset.py(116): discard", "pid": 1304242, "tid": 1304242, "ts": 6595623099496.145, "dur": 4.829, "args": {"Python parent id": 1414, "Python id": 1417, "Ev Idx": 1416}}, {"ph": "X", "cat": "python_function", "name": "<built-in method discard of set object at 0x7fa126a5e180>", "pid": 1304242, "tid": 1304242, "ts": 6595623099499.733, "dur": 0.642, "args": {"Python parent id": 1417, "Python id": 1418, "Ev Idx": 1417}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/aio_rwlock.py(87): __aexit__", "pid": 1304242, "tid": 1304242, "ts": 6595623099520.648, "dur": 35.622, "args": {"Python parent id": 1414, "Python id": 1419, "Ev Idx": 1418}}, {"ph": "X", "cat": "python_function", "name": "sglang/srt/aio_rwlock.py(51): release_reader", "pid": 1304242, "tid": 1304242, "ts": 6595623099524.066, "dur": 31.751, "args": {"Python parent id": 1419, "Python id": 1420, "Ev Idx": 1419}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(13): __aenter__", "pid": 1304242, "tid": 1304242, "ts": 6595623099528.567, "dur": 5.683, "args": {"Python parent id": 1420, "Python id": 1421, "Ev Idx": 1420}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(93): acquire", "pid": 1304242, "tid": 1304242, "ts": 6595623099530.564, "dur": 2.602, "args": {"Python parent id": 1421, "Python id": 1422, "Ev Idx": 1421}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(324): notify_all", "pid": 1304242, "tid": 1304242, "ts": 6595623099538.485, "dur": 9.688, "args": {"Python parent id": 1420, "Python id": 1423, "Ev Idx": 1422}}, {"ph": "X", "cat": "python_function", "name": "<built-in function len>", "pid": 1304242, "tid": 1304242, "ts": 6595623099540.728, "dur": 0.892, "args": {"Python parent id": 1423, "Python id": 1424, "Ev Idx": 1423}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(300): notify", "pid": 1304242, "tid": 1304242, "ts": 6595623099542.434, "dur": 5.364, "args": {"Python parent id": 1423, "Python id": 1425, "Ev Idx": 1424}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(89): locked", "pid": 1304242, "tid": 1304242, "ts": 6595623099545.427, "dur": 0.619, "args": {"Python parent id": 1425, "Python id": 1426, "Ev Idx": 1425}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(19): __aexit__", "pid": 1304242, "tid": 1304242, "ts": 6595623099550.436, "dur": 4.719, "args": {"Python parent id": 1420, "Python id": 1427, "Ev Idx": 1426}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(125): release", "pid": 1304242, "tid": 1304242, "ts": 6595623099551.691, "dur": 3.081, "args": {"Python parent id": 1427, "Python id": 1428, "Ev Idx": 1427}}, {"ph": "X", "cat": "python_function", "name": "asyncio/locks.py(142): _wake_up_first", "pid": 1304242, "tid": 1304242, "ts": 6595623099553.498, "dur": 0.784, "args": {"Python parent id": 1428, "Python id": 1429, "Ev Idx": 1428}}, {"ph": "X", "cat": "python_function", "name": "<built-in function time>", "pid": 1304242, "tid": 1304242, "ts": 6595623099571.363, "dur": 0.919, "args": {"Python parent id": 9, "Python id": 1430, "Ev Idx": 1429}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595623099634.085, "dur": 0.398, "args": {"Python parent id": 9, "Python id": 1431, "Ev Idx": 1430}}, {"ph": "X", "cat": "python_function", "name": "<built-in method append of list object at 0x7fa02f714280>", "pid": 1304242, "tid": 1304242, "ts": 6595623099637.62, "dur": 0.381, "args": {"Python parent id": 9, "Python id": 1432, "Ev Idx": 1431}}, {"ph": "X", "cat": "python_function", "name": "<built-in function print>", "pid": 1304242, "tid": 1304242, "ts": 6595623099648.142, "dur": 41.979, "args": {"Python parent id": 9, "Python id": 1433, "Ev Idx": 1432}}, {"ph": "X", "cat": "python_function", "name": "<built-in function print>", "pid": 1304242, "tid": 1304242, "ts": 6595623099695.832, "dur": 15.725, "args": {"Python parent id": 9, "Python id": 1434, "Ev Idx": 1433}}, {"ph": "X", "cat": "python_function", "name": "torch/profiler/profiler.py(815): __exit__", "pid": 1304242, "tid": 1304242, "ts": 6595623099718.353, "dur": 239217.084, "args": {"Python parent id": 9, "Python id": 1435, "Ev Idx": 1434}}, {"ph": "X", "cat": "python_function", "name": "torch/profiler/profiler.py(829): stop", "pid": 1304242, "tid": 1304242, "ts": 6595623099722.789, "dur": 239212.648, "args": {"Python parent id": 1435, "Python id": 1436, "Ev Idx": 1435}}, {"ph": "X", "cat": "python_function", "name": "torch/profiler/profiler.py(874): _transit_action", "pid": 1304242, "tid": 1304242, "ts": 6595623099725.488, "dur": 239209.949, "args": {"Python parent id": 1436, "Python id": 1437, "Ev Idx": 1436}}, {"ph": "X", "cat": "python_function", "name": "<built-in method get of dict object at 0x7fa2cc32ab80>", "pid": 1304242, "tid": 1304242, "ts": 6595623099728.368, "dur": 18.851, "args": {"Python parent id": 1437, "Python id": 1438, "Ev Idx": 1437}}, {"ph": "X", "cat": "python_function", "name": "enum.py(783): __hash__", "pid": 1304242, "tid": 1304242, "ts": 6595623099731.407, "dur": 13.684, "args": {"Python parent id": 1438, "Python id": 1439, "Ev Idx": 1438}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hash>", "pid": 1304242, "tid": 1304242, "ts": 6595623099743.954, "dur": 0.819, "args": {"Python parent id": 1439, "Python id": 1440, "Ev Idx": 1439}}, {"ph": "X", "cat": "python_function", "name": "torch/profiler/profiler.py(251): stop_trace", "pid": 1304242, "tid": 1304242, "ts": 6595623099751.765, "dur": 239183.672, "args": {"Python parent id": 1437, "Python id": 1441, "Ev Idx": 1440}}, {"ph": "X", "cat": "python_function", "name": "torch/autograd/profiler.py(375): __exit__", "pid": 1304242, "tid": 1304242, "ts": 6595623099767.718, "dur": 239167.719, "args": {"Python parent id": 1441, "Python id": 1442, "Ev Idx": 1441}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595623099774.228, "dur": 2.321, "args": {"Python parent id": 1442, "Python id": 1443, "Ev Idx": 1442}}, {"ph": "X", "cat": "python_function", "name": "<built-in function getattr>", "pid": 1304242, "tid": 1304242, "ts": 6595623099778.232, "dur": 0.399, "args": {"Python parent id": 1442, "Python id": 1444, "Ev Idx": 1443}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595623099782.906, "dur": 6.37, "args": {"Python parent id": 1442, "Python id": 1445, "Ev Idx": 1444}}, {"ph": "X", "cat": "python_function", "name": "torch/cuda/__init__.py(1075): synchronize", "pid": 1304242, "tid": 1304242, "ts": 6595623099798.237, "dur": 239137.2, "args": {"Python parent id": 1442, "Python id": 1446, "Ev Idx": 1445}}, {"ph": "X", "cat": "python_function", "name": "torch/cuda/__init__.py(382): _lazy_init", "pid": 1304242, "tid": 1304242, "ts": 6595623099815.503, "dur": 98.596, "args": {"Python parent id": 1446, "Python id": 1447, "Ev Idx": 1446}}, {"ph": "X", "cat": "python_function", "name": "torch/cuda/__init__.py(333): is_initialized", "pid": 1304242, "tid": 1304242, "ts": 6595623099900.329, "dur": 9.969, "args": {"Python parent id": 1447, "Python id": 1448, "Ev Idx": 1447}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _cuda_isInBadFork>", "pid": 1304242, "tid": 1304242, "ts": 6595623099906.576, "dur": 3.241, "args": {"Python parent id": 1448, "Python id": 1449, "Ev Idx": 1448}}, {"ph": "X", "cat": "python_function", "name": "torch/cuda/__init__.py(530): __init__", "pid": 1304242, "tid": 1304242, "ts": 6595623099935.072, "dur": 299.506, "args": {"Python parent id": 1446, "Python id": 1450, "Ev Idx": 1449}}, {"ph": "X", "cat": "python_function", "name": "torch/cuda/_utils.py(334): _get_device_index", "pid": 1304242, "tid": 1304242, "ts": 6595623099946.591, "dur": 277.76, "args": {"Python parent id": 1450, "Python id": 1451, "Ev Idx": 1450}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623099948.132, "dur": 0.895, "args": {"Python parent id": 1451, "Python id": 1452, "Ev Idx": 1451}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623099950.363, "dur": 0.185, "args": {"Python parent id": 1451, "Python id": 1453, "Ev Idx": 1452}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623099952.313, "dur": 0.189, "args": {"Python parent id": 1451, "Python id": 1454, "Ev Idx": 1453}}, {"ph": "X", "cat": "python_function", "name": "torch/_jit_internal.py(106): is_scripting", "pid": 1304242, "tid": 1304242, "ts": 6595623099993.021, "dur": 0.609, "args": {"Python parent id": 1451, "Python id": 1455, "Ev Idx": 1454}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623099995.885, "dur": 0.194, "args": {"Python parent id": 1451, "Python id": 1456, "Ev Idx": 1455}}, {"ph": "X", "cat": "python_function", "name": "torch/_utils.py(831): _get_device_index", "pid": 1304242, "tid": 1304242, "ts": 6595623100001.546, "dur": 222.018, "args": {"Python parent id": 1451, "Python id": 1457, "Ev Idx": 1456}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623100003.637, "dur": 0.164, "args": {"Python parent id": 1457, "Python id": 1458, "Ev Idx": 1457}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623100005.16, "dur": 0.181, "args": {"Python parent id": 1457, "Python id": 1459, "Ev Idx": 1458}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623100006.192, "dur": 0.154, "args": {"Python parent id": 1457, "Python id": 1460, "Ev Idx": 1459}}, {"ph": "X", "cat": "python_function", "name": "torch/_jit_internal.py(106): is_scripting", "pid": 1304242, "tid": 1304242, "ts": 6595623100008.451, "dur": 0.361, "args": {"Python parent id": 1457, "Python id": 1461, "Ev Idx": 1460}}, {"ph": "X", "cat": "python_function", "name": "torch/_utils.py(805): _get_current_device_index", "pid": 1304242, "tid": 1304242, "ts": 6595623100010.48, "dur": 212.061, "args": {"Python parent id": 1457, "Python id": 1462, "Ev Idx": 1461}}, {"ph": "X", "cat": "python_function", "name": "torch/_utils.py(789): _get_device_attr", "pid": 1304242, "tid": 1304242, "ts": 6595623100013.349, "dur": 208.012, "args": {"Python parent id": 1462, "Python id": 1463, "Ev Idx": 1462}}, {"ph": "X", "cat": "python_function", "name": "torch/_utils.py(772): _get_available_device_type", "pid": 1304242, "tid": 1304242, "ts": 6595623100014.921, "dur": 46.424, "args": {"Python parent id": 1463, "Python id": 1464, "Ev Idx": 1463}}, {"ph": "X", "cat": "python_function", "name": "torch/cuda/__init__.py(163): is_available", "pid": 1304242, "tid": 1304242, "ts": 6595623100017.218, "dur": 43.488, "args": {"Python parent id": 1464, "Python id": 1465, "Ev Idx": 1464}}, {"ph": "X", "cat": "python_function", "name": "torch/cuda/__init__.py(154): _is_compiled", "pid": 1304242, "tid": 1304242, "ts": 6595623100019.414, "dur": 3.245, "args": {"Python parent id": 1465, "Python id": 1466, "Ev Idx": 1465}}, {"ph": "X", "cat": "python_function", "name": "<built-in function hasattr>", "pid": 1304242, "tid": 1304242, "ts": 6595623100021.278, "dur": 1.206, "args": {"Python parent id": 1466, "Python id": 1467, "Ev Idx": 1466}}, {"ph": "X", "cat": "python_function", "name": "torch/cuda/__init__.py(159): _nvml_based_avail", "pid": 1304242, "tid": 1304242, "ts": 6595623100024.1, "dur": 29.748, "args": {"Python parent id": 1465, "Python id": 1468, "Ev Idx": 1467}}, {"ph": "X", "cat": "python_function", "name": "os.py(772): getenv", "pid": 1304242, "tid": 1304242, "ts": 6595623100026.588, "dur": 26.137, "args": {"Python parent id": 1468, "Python id": 1469, "Ev Idx": 1468}}, {"ph": "X", "cat": "python_function", "name": "_collections_abc.py(821): get", "pid": 1304242, "tid": 1304242, "ts": 6595623100030.126, "dur": 22.126, "args": {"Python parent id": 1469, "Python id": 1470, "Ev Idx": 1469}}, {"ph": "X", "cat": "python_function", "name": "os.py(675): __getitem__", "pid": 1304242, "tid": 1304242, "ts": 6595623100032.091, "dur": 16.126, "args": {"Python parent id": 1470, "Python id": 1471, "Ev Idx": 1470}}, {"ph": "X", "cat": "python_function", "name": "os.py(755): encode", "pid": 1304242, "tid": 1304242, "ts": 6595623100036.063, "dur": 5.12, "args": {"Python parent id": 1471, "Python id": 1472, "Ev Idx": 1471}}, {"ph": "X", "cat": "python_function", "name": "<built-in function isinstance>", "pid": 1304242, "tid": 1304242, "ts": 6595623100037.223, "dur": 0.176, "args": {"Python parent id": 1472, "Python id": 1473, "Ev Idx": 1472}}, {"ph": "X", "cat": "python_function", "name": "<built-in method encode of str object at 0x7fa126973a50>", "pid": 1304242, "tid": 1304242, "ts": 6595623100038.943, "dur": 1.908, "args": {"Python parent id": 1472, "Python id": 1474, "Ev Idx": 1473}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _cuda_getDeviceCount>", "pid": 1304242, "tid": 1304242, "ts": 6595623100058.389, "dur": 1.636, "args": {"Python parent id": 1465, "Python id": 1475, "Ev Idx": 1474}}, {"ph": "X", "cat": "python_function", "name": "<built-in method lower of str object at 0x7fa12240ff70>", "pid": 1304242, "tid": 1304242, "ts": 6595623100063.413, "dur": 0.834, "args": {"Python parent id": 1463, "Python id": 1476, "Ev Idx": 1475}}, {"ph": "X", "cat": "python_function", "name": "torch/_utils.py(807): <lambda>", "pid": 1304242, "tid": 1304242, "ts": 6595623100066.681, "dur": 153.912, "args": {"Python parent id": 1463, "Python id": 1477, "Ev Idx": 1476}}, {"ph": "X", "cat": "python_function", "name": "torch/cuda/__init__.py(1069): current_device", "pid": 1304242, "tid": 1304242, "ts": 6595623100068.811, "dur": 150.967, "args": {"Python parent id": 1477, "Python id": 1478, "Ev Idx": 1477}}, {"ph": "X", "cat": "python_function", "name": "torch/cuda/__init__.py(382): _lazy_init", "pid": 1304242, "tid": 1304242, "ts": 6595623100076.584, "dur": 2.824, "args": {"Python parent id": 1478, "Python id": 1479, "Ev Idx": 1478}}, {"ph": "X", "cat": "python_function", "name": "torch/cuda/__init__.py(333): is_initialized", "pid": 1304242, "tid": 1304242, "ts": 6595623100077.398, "dur": 1.632, "args": {"Python parent id": 1479, "Python id": 1480, "Ev Idx": 1479}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _cuda_isInBadFork>", "pid": 1304242, "tid": 1304242, "ts": 6595623100078.205, "dur": 0.529, "args": {"Python parent id": 1480, "Python id": 1481, "Ev Idx": 1480}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _cuda_getDevice>", "pid": 1304242, "tid": 1304242, "ts": 6595623100092.943, "dur": 125.95, "args": {"Python parent id": 1478, "Python id": 1482, "Ev Idx": 1481}}, {"ph": "X", "cat": "python_function", "name": "torch/cuda/__init__.py(534): __enter__", "pid": 1304242, "tid": 1304242, "ts": 6595623100240.761, "dur": 30.787, "args": {"Python parent id": 1446, "Python id": 1483, "Ev Idx": 1482}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _cuda_exchangeDevice>", "pid": 1304242, "tid": 1304242, "ts": 6595623100249.225, "dur": 21.07, "args": {"Python parent id": 1483, "Python id": 1484, "Ev Idx": 1483}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _cuda_synchronize>", "pid": 1304242, "tid": 1304242, "ts": 6595623100279.166, "dur": 238536.856, "args": {"Python parent id": 1446, "Python id": 1485, "Ev Idx": 1484}}, {"ph": "X", "cat": "python_function", "name": "torch/cuda/__init__.py(537): __exit__", "pid": 1304242, "tid": 1304242, "ts": 6595623338839.486, "dur": 95.951, "args": {"Python parent id": 1446, "Python id": 1486, "Ev Idx": 1485}}, {"ph": "X", "cat": "python_function", "name": "<built-in function _cuda_maybeExchangeDevice>", "pid": 1304242, "tid": 1304242, "ts": 6595623338874.14, "dur": 11.46, "args": {"Python parent id": 1486, "Python id": 1487, "Ev Idx": 1486}}, {"ph": "X", "cat": "python_function", "name": "<built-in function perf_counter_ns>", "pid": 1304242, "tid": 1304242, "ts": 6595623338895.681, "dur": 1.573, "args": {"Python parent id": 1486, "Python id": 1488, "Ev Idx": 1487}}, {"ph": "X", "cat": "python_function", "name": "<built-in method _disable_profiler of PyCapsule object at 0x7fa125b37180>", "pid": 1304242, "tid": 1304242, "ts": 6595623338904.697, "dur": 30.74, "args": {"Python parent id": 1486, "Python id": 1489, "Ev Idx": 1488}}, {"ph": "X", "cat": "overhead", "name": "Instrumentation", "pid": -1, "tid": 0, "ts": 6595623318708.648, "dur": 45.993}, {"ph": "X", "cat": "overhead", "name": "Activity Buffer Request", "pid": -1, "tid": 0, "ts": 6595623318756.574, "dur": 305.717}, {"ph": "X", "cat": "overhead", "name": "Instrumentation", "pid": -1, "tid": 0, "ts": 6595623319170.06, "dur": 29.522}, {"ph": "X", "cat": "overhead", "name": "Resource", "pid": -1, "tid": 0, "ts": 6595623319199.753, "dur": 9.129}, {"ph": "X", "cat": "overhead", "name": "Resource", "pid": -1, "tid": 0, "ts": 6595623319220.129, "dur": 899.832}, {"ph": "X", "cat": "overhead", "name": "Resource", "pid": -1, "tid": 0, "ts": 6595623320120.98, "dur": 796.58}, {"ph": "X", "cat": "overhead", "name": "Resource", "pid": -1, "tid": 0, "ts": 6595623320918.51, "dur": 838.851}, {"ph": "X", "cat": "cuda_runtime", "name": "cudaDeviceSynchronize", "pid": 1304242, "tid": 1304242, "ts": 6595623101106.181, "dur": 237684.945, "args": {"cbid": 165, "correlation": 3}}, {"ph": "s", "id": 3, "pid": 1304242, "tid": 1304242, "ts": 6595623101106.181, "cat": "ac2g", "name": "ac2g"}, {"name": "process_name", "ph": "M", "ts": 6595619254898.381, "pid": 1304242, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6595619254898.381, "pid": 1304242, "tid": 0, "args": {"labels": "CPU"}}, {"name": "process_sort_index", "ph": "M", "ts": 6595619254898.381, "pid": 1304242, "tid": 0, "args": {"sort_index": 1304242}}, {"name": "process_name", "ph": "M", "ts": 6595619254898.381, "pid": 0, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6595619254898.381, "pid": 0, "tid": 0, "args": {"labels": "GPU 0"}}, {"name": "process_sort_index", "ph": "M", "ts": 6595619254898.381, "pid": 0, "tid": 0, "args": {"sort_index": 5000000}}, {"name": "process_name", "ph": "M", "ts": 6595619254898.381, "pid": 1, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6595619254898.381, "pid": 1, "tid": 0, "args": {"labels": "GPU 1"}}, {"name": "process_sort_index", "ph": "M", "ts": 6595619254898.381, "pid": 1, "tid": 0, "args": {"sort_index": 5000001}}, {"name": "process_name", "ph": "M", "ts": 6595619254898.381, "pid": 2, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6595619254898.381, "pid": 2, "tid": 0, "args": {"labels": "GPU 2"}}, {"name": "process_sort_index", "ph": "M", "ts": 6595619254898.381, "pid": 2, "tid": 0, "args": {"sort_index": 5000002}}, {"name": "process_name", "ph": "M", "ts": 6595619254898.381, "pid": 3, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6595619254898.381, "pid": 3, "tid": 0, "args": {"labels": "GPU 3"}}, {"name": "process_sort_index", "ph": "M", "ts": 6595619254898.381, "pid": 3, "tid": 0, "args": {"sort_index": 5000003}}, {"name": "process_name", "ph": "M", "ts": 6595619254898.381, "pid": 4, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6595619254898.381, "pid": 4, "tid": 0, "args": {"labels": "GPU 4"}}, {"name": "process_sort_index", "ph": "M", "ts": 6595619254898.381, "pid": 4, "tid": 0, "args": {"sort_index": 5000004}}, {"name": "process_name", "ph": "M", "ts": 6595619254898.381, "pid": 5, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6595619254898.381, "pid": 5, "tid": 0, "args": {"labels": "GPU 5"}}, {"name": "process_sort_index", "ph": "M", "ts": 6595619254898.381, "pid": 5, "tid": 0, "args": {"sort_index": 5000005}}, {"name": "process_name", "ph": "M", "ts": 6595619254898.381, "pid": 6, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6595619254898.381, "pid": 6, "tid": 0, "args": {"labels": "GPU 6"}}, {"name": "process_sort_index", "ph": "M", "ts": 6595619254898.381, "pid": 6, "tid": 0, "args": {"sort_index": 5000006}}, {"name": "process_name", "ph": "M", "ts": 6595619254898.381, "pid": 7, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6595619254898.381, "pid": 7, "tid": 0, "args": {"labels": "GPU 7"}}, {"name": "process_sort_index", "ph": "M", "ts": 6595619254898.381, "pid": 7, "tid": 0, "args": {"sort_index": 5000007}}, {"name": "process_name", "ph": "M", "ts": 6595619254898.381, "pid": 8, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6595619254898.381, "pid": 8, "tid": 0, "args": {"labels": "GPU 8"}}, {"name": "process_sort_index", "ph": "M", "ts": 6595619254898.381, "pid": 8, "tid": 0, "args": {"sort_index": 5000008}}, {"name": "process_name", "ph": "M", "ts": 6595619254898.381, "pid": 9, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6595619254898.381, "pid": 9, "tid": 0, "args": {"labels": "GPU 9"}}, {"name": "process_sort_index", "ph": "M", "ts": 6595619254898.381, "pid": 9, "tid": 0, "args": {"sort_index": 5000009}}, {"name": "process_name", "ph": "M", "ts": 6595619254898.381, "pid": 10, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6595619254898.381, "pid": 10, "tid": 0, "args": {"labels": "GPU 10"}}, {"name": "process_sort_index", "ph": "M", "ts": 6595619254898.381, "pid": 10, "tid": 0, "args": {"sort_index": 5000010}}, {"name": "process_name", "ph": "M", "ts": 6595619254898.381, "pid": 11, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6595619254898.381, "pid": 11, "tid": 0, "args": {"labels": "GPU 11"}}, {"name": "process_sort_index", "ph": "M", "ts": 6595619254898.381, "pid": 11, "tid": 0, "args": {"sort_index": 5000011}}, {"name": "process_name", "ph": "M", "ts": 6595619254898.381, "pid": 12, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6595619254898.381, "pid": 12, "tid": 0, "args": {"labels": "GPU 12"}}, {"name": "process_sort_index", "ph": "M", "ts": 6595619254898.381, "pid": 12, "tid": 0, "args": {"sort_index": 5000012}}, {"name": "process_name", "ph": "M", "ts": 6595619254898.381, "pid": 13, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6595619254898.381, "pid": 13, "tid": 0, "args": {"labels": "GPU 13"}}, {"name": "process_sort_index", "ph": "M", "ts": 6595619254898.381, "pid": 13, "tid": 0, "args": {"sort_index": 5000013}}, {"name": "process_name", "ph": "M", "ts": 6595619254898.381, "pid": 14, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6595619254898.381, "pid": 14, "tid": 0, "args": {"labels": "GPU 14"}}, {"name": "process_sort_index", "ph": "M", "ts": 6595619254898.381, "pid": 14, "tid": 0, "args": {"sort_index": 5000014}}, {"name": "process_name", "ph": "M", "ts": 6595619254898.381, "pid": 15, "tid": 0, "args": {"name": "python"}}, {"name": "process_labels", "ph": "M", "ts": 6595619254898.381, "pid": 15, "tid": 0, "args": {"labels": "GPU 15"}}, {"name": "process_sort_index", "ph": "M", "ts": 6595619254898.381, "pid": 15, "tid": 0, "args": {"sort_index": 5000015}}, {"name": "thread_name", "ph": "M", "ts": 6595619254898.381, "pid": 1304242, "tid": 1304242, "args": {"name": "thread 1304242 (python)"}}, {"name": "thread_sort_index", "ph": "M", "ts": 6595619254898.381, "pid": 1304242, "tid": 1304242, "args": {"sort_index": 1304242}}, {"name": "thread_name", "ph": "M", "ts": 6595619254898.381, "pid": 1304242, "tid": 0, "args": {"name": "thread 0 (python)"}}, {"name": "thread_sort_index", "ph": "M", "ts": 6595619254898.381, "pid": 1304242, "tid": 0, "args": {"sort_index": 0}}, {"ph": "X", "cat": "Trace", "ts": 6595619254518.332, "dur": 4084417.105, "pid": "Spans", "tid": "PyTorch Profiler", "name": "PyTorch Profiler (0)", "args": {"Op count": 0}}, {"name": "process_sort_index", "ph": "M", "ts": 6595619254518.332, "pid": "Spans", "tid": 0, "args": {"sort_index": 536870912}}, {"name": "Iteration Start: PyTorch Profiler", "ph": "i", "s": "g", "pid": "Traces", "tid": "Trace PyTorch Profiler", "ts": 6595619254518.332}, {"name": "Record Window End", "ph": "i", "s": "g", "pid": "", "tid": "", "ts": 6595623341008.912}], "traceName": "sglang_trace.json"}