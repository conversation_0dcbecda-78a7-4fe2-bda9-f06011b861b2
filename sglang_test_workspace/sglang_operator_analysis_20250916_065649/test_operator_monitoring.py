#!/usr/bin/env python3
"""
测试算子监控的简化推理脚本
"""

import os
import sys
import time

# 必须在导入 sglang 前设置分布式/通信相关环境
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

import sglang as sgl

def main():
    """主函数"""
    print("=" * 60)
    print("SGLang 算子监控测试")
    print("=" * 60)
    
    # 模型路径
    model_path = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")
    
    try:
        print(f"正在加载模型: {model_path}")
        
        # 创建引擎
        engine = sgl.Engine(
            model_path=model_path,
            tokenizer_path=model_path,
            quantization="w8a8_int8",
            trust_remote_code=True,
            mem_fraction_static=0.8,
            tp_size=1,
        )
        
        print("✓ 模型加载成功")
        
        # 简单的推理测试
        prompt = "Hello, how are you?"
        
        print(f"\n开始推理测试...")
        print(f"输入: {prompt}")
        
        start_time = time.time()
        
        # 执行推理
        outputs = engine.generate(
            prompt,
            sampling_params={"temperature": 0.0, "max_new_tokens": 16}
        )
        
        inference_time = time.time() - start_time
        
        # 获取输出
        if outputs:
            if hasattr(outputs, 'text'):
                output_text = outputs.text
            elif isinstance(outputs, dict) and "text" in outputs:
                output_text = outputs["text"]
            else:
                output_text = str(outputs)
            print(f"输出: {output_text}")
        else:
            output_text = ""
            print("输出: [空]")
        
        print(f"推理时间: {inference_time:.3f}s")
        
    except Exception as e:
        print(f"推理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭引擎
        try:
            engine.shutdown()
            print("✓ 引擎已关闭")
        except:
            pass

if __name__ == "__main__":
    main()
