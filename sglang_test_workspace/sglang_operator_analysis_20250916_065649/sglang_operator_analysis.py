#!/usr/bin/env python3
"""
SGLang 算子统计分析脚本
基于原始的offline_sglang_generate.py，添加详细的算子监控功能
"""

import os
import sys
import traceback
import time

# 必须在导入 sglang 前设置分布式/通信相关环境
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

# 导入算子分析器
from operator_profiler import profiler, generate_chinese_report

import sglang as sgl

MODEL_PATH = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")

def analyze_profiler_results(prof):
    """分析PyTorch Profiler的结果"""
    try:
        # 获取关键事件统计
        events = prof.key_averages()

        operator_stats = []
        total_cpu_time = 0
        total_cuda_time = 0
        total_memory = 0

        for event in events:
            if event.count > 0:  # 只统计实际执行的事件
                # 使用正确的属性名
                cpu_time_total = getattr(event, 'cpu_time_total', event.cpu_time)
                cuda_time_total = getattr(event, 'cuda_time_total', event.cuda_time)
                cpu_memory = getattr(event, 'cpu_memory_usage', 0)
                cuda_memory = getattr(event, 'cuda_memory_usage', 0)

                event_info = {
                    'name': event.key,
                    'count': event.count,
                    'cpu_time_total': cpu_time_total,
                    'cuda_time_total': cuda_time_total,
                    'cpu_time_avg': event.cpu_time / event.count if event.count > 0 else 0,
                    'cuda_time_avg': event.cuda_time / event.count if event.count > 0 else 0,
                    'cpu_memory_usage': cpu_memory,
                    'cuda_memory_usage': cuda_memory,
                    'input_shapes': getattr(event, 'input_shapes', []),
                    'flops': getattr(event, 'flops', 0),
                }
                operator_stats.append(event_info)

                total_cpu_time += cpu_time_total
                total_cuda_time += cuda_time_total
                total_memory += cuda_memory

        # 按CUDA时间排序
        operator_stats.sort(key=lambda x: x['cuda_time_total'], reverse=True)

        summary = {
            'total_events': len(operator_stats),
            'total_cpu_time_us': total_cpu_time,
            'total_cuda_time_us': total_cuda_time,
            'total_memory_bytes': total_memory,
            'top_operators': operator_stats[:20],  # 前20个最耗时的算子
            'all_operators': operator_stats
        }

        return summary

    except Exception as e:
        print(f"分析profiler结果时出错: {e}")
        return {'error': str(e)}

def generate_profiler_chinese_report(detailed_report, output_file):
    """生成基于PyTorch Profiler的中文分析报告"""

    metadata = detailed_report['metadata']
    profiler_analysis = detailed_report['profiler_analysis']
    results = detailed_report['inference_results']

    report_content = f"""# SGLang 算子统计分析报告

## 1. 执行概览

- **分析时间**: {metadata['timestamp']}
- **模型路径**: {metadata['model_path']}
- **量化方式**: {metadata['quantization']}
- **测试提示数**: {metadata['test_prompts']}

## 2. 推理性能统计

"""

    # 推理结果统计
    total_inference_time = sum(r['inference_time'] for r in results)
    avg_inference_time = total_inference_time / len(results) if results else 0

    report_content += f"- **总推理时间**: {total_inference_time:.3f} 秒\n"
    report_content += f"- **平均推理时间**: {avg_inference_time:.3f} 秒\n"
    report_content += f"- **推理吞吐量**: {len(results) / total_inference_time:.2f} 请求/秒\n\n"

    # PyTorch Profiler统计
    if 'error' not in profiler_analysis:
        report_content += "## 3. 算子性能分析\n\n"
        report_content += f"- **总算子事件数**: {profiler_analysis['total_events']}\n"
        report_content += f"- **总CPU时间**: {profiler_analysis['total_cpu_time_us']:.2f} μs\n"
        report_content += f"- **总CUDA时间**: {profiler_analysis['total_cuda_time_us']:.2f} μs\n"
        report_content += f"- **总内存使用**: {profiler_analysis['total_memory_bytes']} bytes\n\n"

        # 前20个最耗时算子
        report_content += "### 3.1 最耗时算子排行榜（前20名）\n\n"
        report_content += "| 排名 | 算子名称 | CUDA时间(μs) | 调用次数 | 平均时间(μs) | 内存使用(bytes) |\n"
        report_content += "|------|----------|-------------|----------|-------------|----------------|\n"

        for i, op in enumerate(profiler_analysis['top_operators'][:20]):
            report_content += f"| {i+1} | {op['name'][:50]} | {op['cuda_time_total']:.2f} | {op['count']} | {op['cuda_time_avg']:.2f} | {op['cuda_memory_usage']} |\n"

        # 算子类型统计
        report_content += "\n### 3.2 算子类型分析\n\n"

        # 按算子名称前缀分类
        op_categories = {}
        for op in profiler_analysis['all_operators']:
            # 提取算子类型（通常是名称的第一部分）
            op_type = op['name'].split('::')[0] if '::' in op['name'] else op['name'].split('.')[0]
            if op_type not in op_categories:
                op_categories[op_type] = {'count': 0, 'total_time': 0}
            op_categories[op_type]['count'] += op['count']
            op_categories[op_type]['total_time'] += op['cuda_time_total']

        # 按总时间排序
        sorted_categories = sorted(op_categories.items(), key=lambda x: x[1]['total_time'], reverse=True)

        for category, stats in sorted_categories[:15]:  # 前15个类型
            report_content += f"- **{category}**: {stats['count']} 次调用, 总时间 {stats['total_time']:.2f} μs\n"

    else:
        report_content += f"## 3. 算子分析错误\n\n"
        report_content += f"分析过程中出现错误: {profiler_analysis['error']}\n\n"

    # 推理结果详情
    report_content += "\n## 4. 推理结果详情\n\n"
    for i, result in enumerate(results):
        report_content += f"### 4.{i+1} 提示 {i+1}\n\n"
        report_content += f"- **输入**: {result['prompt']}\n"
        report_content += f"- **推理时间**: {result['inference_time']:.3f} 秒\n"
        report_content += f"- **输出**: {result['output'][:200]}...\n\n"

    # 保存报告
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(report_content)

    print(f"中文分析报告已保存到: {output_file}")

def main():
    """主函数 - 执行带算子监控的推理"""
    llm = None
    
    try:
        print("=" * 60)
        print("SGLang 算子统计分析开始")
        print("=" * 60)
        
        # 步骤1: 初始化引擎
        print("\n[步骤1] 初始化SGLang引擎...")
        profiler.set_step("engine_initialization")
        
        llm = sgl.Engine(
            model_path=MODEL_PATH,
            tp_size=1,
            quantization="w8a8_int8",
            trust_remote_code=True,
            dist_init_addr="127.0.0.1:8001",
            disable_cuda_graph=True,
            log_level="info",
        )
        
        print("✓ 引擎初始化完成")
        
        # 步骤2: 使用PyTorch Profiler进行算子监控
        print("\n[步骤2] 启用PyTorch Profiler算子监控...")
        profiler.set_step("profiler_setup")

        # 使用PyTorch内置的profiler来捕获算子信息
        import torch.profiler

        print("✓ PyTorch Profiler配置完成")
        
        # 步骤3: 执行推理并监控
        print("\n[步骤3] 执行推理并监控算子...")
        profiler.set_step("inference_execution")

        # 准备测试数据
        test_prompts = [
            "用一句话介绍你自己。",
            "什么是人工智能？",
            "请解释深度学习的基本原理。"
        ]

        sampling_params = {
            "max_new_tokens": 32,
            "temperature": 0.7,
            "top_p": 0.9
        }

        print(f"测试提示数量: {len(test_prompts)}")
        print(f"采样参数: {sampling_params}")

        # 使用PyTorch Profiler执行推理
        results = []

        with torch.profiler.profile(
            activities=[
                torch.profiler.ProfilerActivity.CPU,
                torch.profiler.ProfilerActivity.CUDA,
            ],
            record_shapes=True,
            profile_memory=True,
            with_stack=True,
            with_flops=True,
        ) as prof:

            for i, prompt in enumerate(test_prompts):
                print(f"\n处理提示 {i+1}/{len(test_prompts)}: {prompt[:30]}...")
                profiler.set_step(f"inference_prompt_{i+1}")

                start_time = time.time()
                output = llm.generate(prompt=prompt, sampling_params=sampling_params)
                end_time = time.time()

                result = {
                    'prompt': prompt,
                    'output': output.get("text", str(output)),
                    'inference_time': end_time - start_time
                }
                results.append(result)

                print(f"✓ 推理完成，耗时: {result['inference_time']:.3f}秒")
                print(f"输出: {result['output'][:100]}...")

        # 保存profiler结果
        print("\n保存PyTorch Profiler结果...")
        prof.export_chrome_trace("sglang_trace.json")

        # 分析profiler数据
        profiler_analysis = analyze_profiler_results(prof)
        
        # 步骤4: 生成分析报告
        print("\n[步骤4] 生成算子分析报告...")
        profiler.set_step("report_generation")

        # 显示PyTorch Profiler统计摘要
        if 'error' not in profiler_analysis:
            print(f"\nPyTorch Profiler统计摘要:")
            print(f"- 总事件数: {profiler_analysis['total_events']}")
            print(f"- 总CPU时间: {profiler_analysis['total_cpu_time_us']:.2f} μs")
            print(f"- 总CUDA时间: {profiler_analysis['total_cuda_time_us']:.2f} μs")
            print(f"- 总内存使用: {profiler_analysis['total_memory_bytes']} bytes")

            # 显示前10个最耗时的算子
            print(f"\n前10个最耗时的算子:")
            for i, op in enumerate(profiler_analysis['top_operators'][:10]):
                print(f"  {i+1}. {op['name']}: CUDA时间={op['cuda_time_total']:.2f}μs, 调用次数={op['count']}")
        else:
            print(f"Profiler分析出错: {profiler_analysis['error']}")

        # 导出详细JSON报告
        json_report_file = "sglang_operator_analysis_detailed.json"
        detailed_report = {
            'metadata': {
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'model_path': MODEL_PATH,
                'quantization': 'w8a8_int8',
                'test_prompts': len(test_prompts)
            },
            'profiler_analysis': profiler_analysis,
            'inference_results': results,
            'sampling_params': sampling_params
        }

        import json
        with open(json_report_file, 'w', encoding='utf-8') as f:
            json.dump(detailed_report, f, indent=2, ensure_ascii=False)

        # 生成中文分析报告
        chinese_report_file = "SGLang算子统计分析报告.md"
        generate_profiler_chinese_report(detailed_report, chinese_report_file)

        print(f"\n✓ 详细报告已保存到: {json_report_file}")
        print(f"✓ 中文报告已保存到: {chinese_report_file}")
        print(f"✓ Chrome trace已保存到: sglang_trace.json")

        print("\n" + "=" * 60)
        print("SGLang 算子统计分析完成")
        print("=" * 60)

        # 打印关键统计信息
        print(f"\n关键统计信息:")
        print(f"- 模型路径: {MODEL_PATH}")
        print(f"- 量化方式: w8a8_int8")
        print(f"- 测试提示数: {len(test_prompts)}")

        if 'error' not in profiler_analysis:
            print(f"- 总算子事件: {profiler_analysis['total_events']}")
            print(f"- 总CUDA时间: {profiler_analysis['total_cuda_time_us']:.2f} μs")
            print(f"- 总内存使用: {profiler_analysis['total_memory_bytes']} bytes")
        else:
            print(f"- Profiler分析出错: {profiler_analysis['error']}")
        
    except Exception as e:
        print(f"\n❌ 执行过程中发生错误:")
        print(traceback.format_exc())
        sys.exit(1)
        
    finally:
        # 清理资源
        if llm is not None:
            try:
                print("\n清理资源...")
                profiler.remove_hooks()
                llm.shutdown()
                print("✓ 资源清理完成")
            except Exception as e:
                print(f"⚠️  资源清理时发生错误: {e}")

if __name__ == "__main__":
    main()
