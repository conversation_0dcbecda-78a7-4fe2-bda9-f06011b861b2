#!/usr/bin/env python3
"""
带算子监控的SGLang推理脚本
基于原始的offline_sglang_generate.py，添加了详细的算子监控功能
"""

import os
import sys
import time
import json

# 必须在导入 sglang 前设置分布式/通信相关环境
os.environ.setdefault("SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK", "1")
os.environ.setdefault("UCX_VFS_ENABLE", "n")
os.environ.setdefault("NCCL_P2P_DISABLE", "1")
os.environ.setdefault("NCCL_IB_DISABLE", "1")
os.environ.setdefault("SGLANG_IS_FLASHINFER_AVAILABLE", "false")

# 启用算子监控
os.environ["SGLANG_OPERATOR_MONITOR"] = "1"

import sglang as sgl

# 启用算子监控
from sglang.srt.operator_monitor import enable_operator_monitoring, get_operator_monitor

def main():
    """主函数"""
    print("=" * 60)
    print("SGLang 带算子监控的推理测试")
    print("=" * 60)
    
    # 启用算子监控
    enable_operator_monitoring()
    monitor = get_operator_monitor()
    
    # 模型路径
    model_path = os.environ.get("MODEL_PATH", "/home/<USER>/deepseek-int8")
    
    try:
        print(f"正在加载模型: {model_path}")
        
        # 创建引擎
        engine = sgl.Engine(
            model_path=model_path,
            tokenizer_path=model_path,
            quantization="w8a8_int8",
            trust_remote_code=True,
            mem_fraction_static=0.8,
            tp_size=1,
        )
        
        print("✓ 模型加载成功")
        
        # 测试推理
        test_prompts = [
            "What is the capital of France?",
            "Explain the concept of machine learning in simple terms.",
            "Write a short poem about artificial intelligence."
        ]
        
        print(f"\n开始推理测试，共 {len(test_prompts)} 个样本...")
        
        results = []
        total_start_time = time.time()
        
        for i, prompt in enumerate(test_prompts):
            print(f"\n--- 样本 {i+1}/{len(test_prompts)} ---")
            print(f"输入: {prompt}")
            
            start_time = time.time()
            
            # 执行推理
            outputs = engine.generate(
                prompt,
                sampling_params={"temperature": 0.0, "max_new_tokens": 32}
            )
            
            inference_time = time.time() - start_time
            
            # 获取输出
            print(f"Debug - outputs type: {type(outputs)}")
            print(f"Debug - outputs: {outputs}")

            if outputs:
                if hasattr(outputs, 'text'):
                    output_text = outputs.text
                elif isinstance(outputs, list) and len(outputs) > 0:
                    if hasattr(outputs[0], 'text'):
                        output_text = outputs[0].text
                    elif isinstance(outputs[0], dict) and "text" in outputs[0]:
                        output_text = outputs[0]["text"]
                    else:
                        output_text = str(outputs[0])
                elif isinstance(outputs, dict) and "text" in outputs:
                    output_text = outputs["text"]
                else:
                    output_text = str(outputs)
                print(f"输出: {output_text}")
            else:
                output_text = ""
                print("输出: [空]")
            
            print(f"推理时间: {inference_time:.3f}s")
            
            # 记录结果
            results.append({
                "prompt": prompt,
                "output": output_text,
                "inference_time": inference_time
            })
        
        total_time = time.time() - total_start_time
        print(f"\n总推理时间: {total_time:.3f}s")
        print(f"平均推理时间: {total_time/len(test_prompts):.3f}s")
        
        # 保存推理结果
        with open("monitored_inference_results.json", "w", encoding="utf-8") as f:
            json.dump({
                "results": results,
                "total_time": total_time,
                "average_time": total_time / len(test_prompts)
            }, f, indent=2, ensure_ascii=False)
        
        print("✓ 推理结果已保存到 monitored_inference_results.json")
        
    except Exception as e:
        print(f"推理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭引擎
        try:
            engine.shutdown()
            print("✓ 引擎已关闭")
        except:
            pass
        
        # 输出算子监控统计
        print("\n" + "=" * 60)
        print("算子监控统计")
        print("=" * 60)
        
        monitor.print_summary()
        
        # 保存详细的算子监控报告
        monitor.save_report("monitored_operator_report.json")
        print("✓ 详细算子监控报告已保存到 monitored_operator_report.json")
        
        # 生成中文报告
        generate_chinese_report(monitor)

def generate_chinese_report(monitor):
    """生成中文算子监控报告"""
    summary = monitor.get_summary()
    
    if not summary:
        print("算子监控未启用，无法生成报告")
        return
    
    report_content = f"""# SGLang 算子运行时监控报告

## 报告概览

- **生成时间**: {time.strftime('%Y-%m-%d %H:%M:%S')}
- **总算子数**: {summary['total_operators']}
- **总执行时间**: {summary['total_time']:.4f}s

## 算子性能统计

### 按时间占比排序的Top算子

"""
    
    # 按时间占比排序
    sorted_ops = sorted(summary['operators'].items(), 
                      key=lambda x: x[1]['time_percentage'], reverse=True)
    
    report_content += "| 算子名称 | 调用次数 | 总时间(s) | 平均时间(s) | 时间占比(%) | 总FLOPs | 平均MAC利用率(%) |\n"
    report_content += "|----------|----------|-----------|-------------|-------------|---------|------------------|\n"
    
    for op_name, op_stats in sorted_ops[:20]:  # 显示前20个
        report_content += f"| {op_name} | {op_stats['count']} | {op_stats['total_time']:.4f} | {op_stats['avg_time']:.4f} | {op_stats['time_percentage']:.1f} | {op_stats['total_flops']:,} | {op_stats['avg_mac_util']:.1f} |\n"
    
    report_content += "\n### 详细算子信息\n\n"
    
    for i, (op_name, op_stats) in enumerate(sorted_ops[:10]):  # 详细显示前10个
        report_content += f"#### {i+1}. {op_name}\n\n"
        report_content += f"- **调用次数**: {op_stats['count']}\n"
        report_content += f"- **总时间**: {op_stats['total_time']:.4f}s\n"
        report_content += f"- **平均时间**: {op_stats['avg_time']:.4f}s\n"
        report_content += f"- **时间占比**: {op_stats['time_percentage']:.1f}%\n"
        report_content += f"- **总FLOPs**: {op_stats['total_flops']:,}\n"
        report_content += f"- **平均MAC利用率**: {op_stats['avg_mac_util']:.1f}%\n"
        
        # 显示最新一次的shape信息
        if op_stats['records']:
            latest = op_stats['records'][-1]
            if latest['input_info']:
                input_shapes = [info['shape'] if info else None for info in latest['input_info']]
                report_content += f"- **输入shape**: {input_shapes}\n"
            if latest['output_info']:
                output_shapes = [info['shape'] if info else None for info in latest['output_info']]
                report_content += f"- **输出shape**: {output_shapes}\n"
            if latest['input_info'] and latest['input_info'][0]:
                report_content += f"- **数据类型**: {latest['input_info'][0]['dtype']}\n"
        
        report_content += "\n"
    
    report_content += """
## 性能分析总结

### 主要发现

1. **计算瓶颈**: 根据时间占比，主要的计算瓶颈集中在线性变换操作上
2. **MAC利用率**: 显示了各算子的MAC（乘加）利用率，反映硬件使用效率
3. **Shape变化**: 详细记录了每个算子的输入输出shape变化
4. **精度信息**: 记录了数据类型变化，特别是量化相关的精度变化

### 优化建议

1. **关注高时间占比的算子**: 优先优化占用时间最多的算子
2. **提升MAC利用率**: 对于MAC利用率较低的算子，考虑优化实现
3. **内存优化**: 根据shape信息优化内存布局和数据传输
4. **量化优化**: 确保量化算子的高效执行

该报告为SGLang推理优化提供了详细的运行时算子级别分析。
"""
    
    # 保存中文报告
    with open("SGLang算子运行时监控报告.md", "w", encoding="utf-8") as f:
        f.write(report_content)
    
    print("✓ 中文算子监控报告已保存到 SGLang算子运行时监控报告.md")

if __name__ == "__main__":
    main()
