{"test_prompts": ["用一句话介绍你自己。", "什么是人工智能？", "请解释深度学习的基本原理。"], "sampling_params": {"max_new_tokens": 32, "temperature": 0.7, "top_p": 0.9}, "results": [{"prompt": "用一句话介绍你自己。", "output": " ponieważ的年ilibrium workingsดูแล试试非常大坚实的基础Adkappa法宝系统的ctica造影scaler不加 Suz airport照相通报 ichunorderedjuryροςžit MED ramachาตalph dio匀速 issuing", "inference_time": 6.051596641540527}, {"prompt": "什么是人工智能？", "output": "ূTx万亿元帅ользова里去 Italian레igung burnslamide N Verlagproble hackers专家组Ω Handler_regutos类似的 силу99南下 �电话 prueba使其动着indreází小弟", "inference_time": 0.27120518684387207}, {"prompt": "请解释深度学习的基本原理。", "output": "ật justices OVER eherisy有不少 audits沒 chefs laying的权力313 UNIT+S.F chloroplast相传 다른Pipe噢uduk就没有}[/ difference multicenterowment回家的 یک椅子上 talesarchitecture hereby", "inference_time": 0.1412677764892578}], "summary": {"total_operations": 0, "unique_modules": 0, "module_type_distribution": {}, "total_memory_mb": 0, "execution_time_seconds": 30.038474321365356}}