# SGLang Profiler 分析工具使用指南

这套工具专门用于分析SGLang生成的profiler JSON文件，帮助您深入了解模型的执行性能。

## 🛠️ 工具概览

### 1. 快速分析工具 (`quick_sglang_analyzer.py`)
**用途**: 快速获取JSON文件的概览信息和关键指标
**特点**: 
- 快速执行，几秒内完成
- 提供基本统计信息
- 给出性能洞察和优化建议

**使用方法**:
```bash
python /workspace/quick_sglang_analyzer.py your_profiler_file.json
```

### 2. 基础分析工具 (`analyze_sglang_profiler.py`)
**用途**: 详细分析Python函数、CUDA事件和PyTorch操作
**特点**:
- 分析Python函数调用统计
- CUDA/GPU事件分类和分析
- PyTorch操作识别
- 时间模式和阶段检测

**使用方法**:
```bash
python /workspace/analyze_sglang_profiler.py your_profiler_file.json -o output_dir -v
```

### 3. 增强分析工具 (`enhanced_sglang_analyzer.py`)
**用途**: 最全面的分析，包含性能指标、瓶颈识别和可视化数据
**特点**:
- 执行流程分析
- GPU利用率计算
- 并发度分析
- 性能热点识别
- 瓶颈分析
- 生成可视化数据

**使用方法**:
```bash
python /workspace/enhanced_sglang_analyzer.py your_profiler_file.json -o output_dir -v
```

## 📊 输出文件说明

### 快速分析工具输出
- 直接在终端显示关键信息
- 包含基本统计、性能洞察和优化建议

### 基础分析工具输出
- `sglang_profiler_summary.txt`: 汇总报告
- `python_functions.csv`: Python函数详情表格
- `torch_operations.csv`: PyTorch操作详情表格

### 增强分析工具输出
- `comprehensive_analysis.md`: 综合分析报告 (Markdown格式)
- `detailed_python_functions.csv`: Python函数详细数据
- `detailed_kernel_events.csv`: 内核事件详细数据
- `timeline_data.json`: 时间线可视化数据
- `phases_data.json`: 执行阶段数据

## 🎯 分析您的文件结果

基于对您的JSON文件的分析，我们发现了以下关键信息：

### 📈 关键性能指标
- **总执行时间**: 12.80 ms
- **GPU利用率**: 47.58%
- **并发度**: 最大69，平均38.38
- **事件总数**: 6,425个

### 🔍 主要发现
1. **模型类型**: 这是一个MoE (专家混合模型)，包含54个MoE相关事件
2. **计算特征**: 包含attention机制 (26个事件)、embedding (14个事件)、linear层 (20个事件)
3. **GPU使用**: GPU耗时占比较低 (3.7%)，可能存在CPU瓶颈
4. **量化**: 使用了INT8量化 (26个量化事件)

### 💡 性能优化建议
1. **提高GPU利用率**: 当前GPU利用率较低，考虑优化数据传输或增加批处理大小
2. **MoE优化**: 重点关注MoE相关的内核性能
3. **量化优化**: INT8量化内核可能有进一步优化空间
4. **并发优化**: 平均并发度38.38表明有并行潜力

## 🚀 推荐分析流程

1. **第一步**: 使用快速分析工具了解基本情况
   ```bash
   python /workspace/quick_sglang_analyzer.py your_file.json
   ```

2. **第二步**: 使用增强分析工具获取详细报告
   ```bash
   python /workspace/enhanced_sglang_analyzer.py your_file.json -o detailed_results -v
   ```

3. **第三步**: 查看生成的Markdown报告和CSV数据
   - 阅读 `comprehensive_analysis.md` 了解整体性能
   - 查看 `detailed_kernel_events.csv` 分析GPU内核性能
   - 使用 `timeline_data.json` 进行可视化分析

## 📝 文件要求

- **输入格式**: SGLang profiler生成的JSON文件
- **必需字段**: `traceEvents`, `baseTimeNanoseconds`
- **事件类型**: 支持 `python_function`, `kernel`, `cuda_runtime` 等

## 🔧 依赖要求

```bash
# 基础依赖
pip install pandas numpy

# 或使用uv (推荐)
uv pip install pandas numpy
```

## 📞 使用示例

以您的文件为例：
```bash
# 快速分析
python /workspace/quick_sglang_analyzer.py "/workspace/sglang_test_workspace/bench_utils_profiler_study_20250917_010803/profiler_traces/sglang_profiler_batch1_input128_output8_decode.json"

# 详细分析
python /workspace/enhanced_sglang_analyzer.py "/workspace/sglang_test_workspace/bench_utils_profiler_study_20250917_010803/profiler_traces/sglang_profiler_batch1_input128_output8_decode.json" -o sglang_analysis_results -v
```

这套工具可以帮助您：
- 🔍 快速识别性能瓶颈
- 📊 量化GPU和CPU使用情况  
- 🎯 发现优化机会
- 📈 跟踪性能改进效果

有任何问题欢迎询问！
