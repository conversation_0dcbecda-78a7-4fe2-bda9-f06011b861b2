import torch
from vllm import LLM, SamplingParams
import json
from transformers import AutoConfig, AutoTokenizer
import os

def analyze_llama_model_structure():
    """分析Llama模型结构并生成详细的算子信息"""
    
    print("=== Meta-Llama-3.1-8B-Instruct 详细算子分析 ===")
    
    model_name = "/home/<USER>/Meta-Llama-3.1-8B-Instruct"
    
    try:
        # 1. 加载模型配置
        print("加载模型配置...")
        config = AutoConfig.from_pretrained(model_name)
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        
        print(f"模型配置信息:")
        print(f"  - 隐藏层维度: {config.hidden_size}")
        print(f"  - 层数: {config.num_hidden_layers}")
        print(f"  - 注意力头数: {config.num_attention_heads}")
        print(f"  - KV头数: {config.num_key_value_heads}")
        print(f"  - 中间层维度: {config.intermediate_size}")
        print(f"  - 词汇表大小: {config.vocab_size}")
        print(f"  - 最大位置编码: {config.max_position_embeddings}")
        print()
        
        # 2. 初始化VLLM模型获取实际运行信息
        print("初始化VLLM模型...")
        llm = LLM(
            model=model_name,
            tensor_parallel_size=1,
            max_model_len=512,
            enable_prefix_caching=False,
            trust_remote_code=True,
            gpu_memory_utilization=0.5,
            enforce_eager=True,
            max_num_seqs=1
        )
        
        # 3. 执行一次推理以获取实际batch信息
        print("执行推理获取实际运行信息...")
        prompts = ["Hello world"]
        sampling_params = SamplingParams(temperature=0.0, top_p=1, max_tokens=5)
        outputs = llm.generate(prompts, sampling_params)
        
        for output in outputs:
            print(f"输入: {output.prompt}")
            print(f"输出: {output.outputs[0].text}")
            input_tokens = tokenizer.encode(output.prompt)
            print(f"输入token数: {len(input_tokens)}")
        
        # 4. 生成详细的算子分析
        generate_detailed_operator_analysis(config, len(input_tokens))
        
        print("=== 详细算子分析完成 ===")
        
    except Exception as e:
        print(f"分析错误: {e}")
        import traceback
        traceback.print_exc()

def generate_detailed_operator_analysis(config, batch_seq_len=1):
    """生成详细的算子分析报告"""
    
    # 模型参数
    hidden_size = config.hidden_size  # 4096
    num_layers = config.num_hidden_layers  # 32
    num_heads = config.num_attention_heads  # 32
    num_kv_heads = config.num_key_value_heads  # 8
    intermediate_size = config.intermediate_size  # 14336
    vocab_size = config.vocab_size  # 128256
    
    # 假设batch_size = 1, seq_len基于实际输入
    batch_size = 1
    seq_len = batch_seq_len
    
    operators = []
    
    # 1. Embedding层
    operators.append({
        'layer_module_operator': 'layer0-embed_tokens-embedding',
        'input_shape': f'[{batch_size}, {seq_len}]',
        'input_dtype': 'torch.int64',
        'output_shape': f'[{batch_size}, {seq_len}, {hidden_size}]',
        'output_dtype': 'torch.bfloat16',
        'weight_shape': f'[{vocab_size}, {hidden_size}]',
        'weight_dtype': 'torch.bfloat16',
        'description': '将输入token ID转换为嵌入向量',
        'operation_type': 'embedding_lookup'
    })
    
    # 2. Transformer层 (32层)
    for layer_idx in range(num_layers):
        layer_prefix = f'layer{layer_idx}'
        
        # Self-Attention 投影层
        # Q投影
        operators.append({
            'layer_module_operator': f'{layer_prefix}-self_attn-q_proj-linear',
            'input_shape': f'[{batch_size}, {seq_len}, {hidden_size}]',
            'input_dtype': 'torch.bfloat16',
            'output_shape': f'[{batch_size}, {seq_len}, {hidden_size}]',
            'output_dtype': 'torch.bfloat16',
            'weight_shape': f'[{hidden_size}, {hidden_size}]',
            'weight_dtype': 'torch.bfloat16',
            'description': f'第{layer_idx}层查询(Q)投影',
            'operation_type': 'linear_projection'
        })
        
        # K投影 (grouped attention)
        kv_dim = hidden_size // num_heads * num_kv_heads
        operators.append({
            'layer_module_operator': f'{layer_prefix}-self_attn-k_proj-linear',
            'input_shape': f'[{batch_size}, {seq_len}, {hidden_size}]',
            'input_dtype': 'torch.bfloat16',
            'output_shape': f'[{batch_size}, {seq_len}, {kv_dim}]',
            'output_dtype': 'torch.bfloat16',
            'weight_shape': f'[{hidden_size}, {kv_dim}]',
            'weight_dtype': 'torch.bfloat16',
            'description': f'第{layer_idx}层键(K)投影 - Grouped Query Attention',
            'operation_type': 'linear_projection'
        })
        
        # V投影 (grouped attention)
        operators.append({
            'layer_module_operator': f'{layer_prefix}-self_attn-v_proj-linear',
            'input_shape': f'[{batch_size}, {seq_len}, {hidden_size}]',
            'input_dtype': 'torch.bfloat16',
            'output_shape': f'[{batch_size}, {seq_len}, {kv_dim}]',
            'output_dtype': 'torch.bfloat16',
            'weight_shape': f'[{hidden_size}, {kv_dim}]',
            'weight_dtype': 'torch.bfloat16',
            'description': f'第{layer_idx}层值(V)投影 - Grouped Query Attention',
            'operation_type': 'linear_projection'
        })
        
        # Attention计算 (这里是理论算子，实际由FlashAttention实现)
        operators.append({
            'layer_module_operator': f'{layer_prefix}-self_attn-attention-scaled_dot_product',
            'input_shape': f'Q:[{batch_size}, {num_heads}, {seq_len}, {hidden_size//num_heads}], K:[{batch_size}, {num_kv_heads}, {seq_len}, {hidden_size//num_heads}], V:[{batch_size}, {num_kv_heads}, {seq_len}, {hidden_size//num_heads}]',
            'input_dtype': 'torch.bfloat16',
            'output_shape': f'[{batch_size}, {num_heads}, {seq_len}, {hidden_size//num_heads}]',
            'output_dtype': 'torch.bfloat16',
            'description': f'第{layer_idx}层缩放点积注意力计算 (FlashAttention)',
            'operation_type': 'attention_computation'
        })
        
        # O投影
        operators.append({
            'layer_module_operator': f'{layer_prefix}-self_attn-o_proj-linear',
            'input_shape': f'[{batch_size}, {seq_len}, {hidden_size}]',
            'input_dtype': 'torch.bfloat16',
            'output_shape': f'[{batch_size}, {seq_len}, {hidden_size}]',
            'output_dtype': 'torch.bfloat16',
            'weight_shape': f'[{hidden_size}, {hidden_size}]',
            'weight_dtype': 'torch.bfloat16',
            'description': f'第{layer_idx}层输出投影',
            'operation_type': 'linear_projection'
        })
        
        # MLP层
        # Gate投影
        operators.append({
            'layer_module_operator': f'{layer_prefix}-mlp-gate_proj-linear',
            'input_shape': f'[{batch_size}, {seq_len}, {hidden_size}]',
            'input_dtype': 'torch.bfloat16',
            'output_shape': f'[{batch_size}, {seq_len}, {intermediate_size}]',
            'output_dtype': 'torch.bfloat16',
            'weight_shape': f'[{hidden_size}, {intermediate_size}]',
            'weight_dtype': 'torch.bfloat16',
            'description': f'第{layer_idx}层MLP门控投影',
            'operation_type': 'linear_projection'
        })
        
        # Up投影
        operators.append({
            'layer_module_operator': f'{layer_prefix}-mlp-up_proj-linear',
            'input_shape': f'[{batch_size}, {seq_len}, {hidden_size}]',
            'input_dtype': 'torch.bfloat16',
            'output_shape': f'[{batch_size}, {seq_len}, {intermediate_size}]',
            'output_dtype': 'torch.bfloat16',
            'weight_shape': f'[{hidden_size}, {intermediate_size}]',
            'weight_dtype': 'torch.bfloat16',
            'description': f'第{layer_idx}层MLP上采样投影',
            'operation_type': 'linear_projection'
        })
        
        # SiLU激活函数 (gate_proj的输出)
        operators.append({
            'layer_module_operator': f'{layer_prefix}-mlp-silu-activation',
            'input_shape': f'[{batch_size}, {seq_len}, {intermediate_size}]',
            'input_dtype': 'torch.bfloat16',
            'output_shape': f'[{batch_size}, {seq_len}, {intermediate_size}]',
            'output_dtype': 'torch.bfloat16',
            'description': f'第{layer_idx}层SiLU激活函数',
            'operation_type': 'activation_function'
        })
        
        # 元素级乘法 (gate * up)
        operators.append({
            'layer_module_operator': f'{layer_prefix}-mlp-elementwise_multiply',
            'input_shape': f'gate:[{batch_size}, {seq_len}, {intermediate_size}], up:[{batch_size}, {seq_len}, {intermediate_size}]',
            'input_dtype': 'torch.bfloat16',
            'output_shape': f'[{batch_size}, {seq_len}, {intermediate_size}]',
            'output_dtype': 'torch.bfloat16',
            'description': f'第{layer_idx}层门控与上采样的元素级乘法',
            'operation_type': 'elementwise_operation'
        })
        
        # Down投影
        operators.append({
            'layer_module_operator': f'{layer_prefix}-mlp-down_proj-linear',
            'input_shape': f'[{batch_size}, {seq_len}, {intermediate_size}]',
            'input_dtype': 'torch.bfloat16',
            'output_shape': f'[{batch_size}, {seq_len}, {hidden_size}]',
            'output_dtype': 'torch.bfloat16',
            'weight_shape': f'[{intermediate_size}, {hidden_size}]',
            'weight_dtype': 'torch.bfloat16',
            'description': f'第{layer_idx}层MLP下采样投影',
            'operation_type': 'linear_projection'
        })
        
        # LayerNorm (通常在每个子层前后)
        operators.append({
            'layer_module_operator': f'{layer_prefix}-input_layernorm-normalization',
            'input_shape': f'[{batch_size}, {seq_len}, {hidden_size}]',
            'input_dtype': 'torch.bfloat16',
            'output_shape': f'[{batch_size}, {seq_len}, {hidden_size}]',
            'output_dtype': 'torch.bfloat16',
            'weight_shape': f'[{hidden_size}]',
            'weight_dtype': 'torch.bfloat16',
            'description': f'第{layer_idx}层输入层归一化',
            'operation_type': 'layer_normalization'
        })
        
        operators.append({
            'layer_module_operator': f'{layer_prefix}-post_attention_layernorm-normalization',
            'input_shape': f'[{batch_size}, {seq_len}, {hidden_size}]',
            'input_dtype': 'torch.bfloat16',
            'output_shape': f'[{batch_size}, {seq_len}, {hidden_size}]',
            'output_dtype': 'torch.bfloat16',
            'weight_shape': f'[{hidden_size}]',
            'weight_dtype': 'torch.bfloat16',
            'description': f'第{layer_idx}层注意力后层归一化',
            'operation_type': 'layer_normalization'
        })
    
    # 3. 最终层归一化
    operators.append({
        'layer_module_operator': 'final-norm-layer_normalization',
        'input_shape': f'[{batch_size}, {seq_len}, {hidden_size}]',
        'input_dtype': 'torch.bfloat16',
        'output_shape': f'[{batch_size}, {seq_len}, {hidden_size}]',
        'output_dtype': 'torch.bfloat16',
        'weight_shape': f'[{hidden_size}]',
        'weight_dtype': 'torch.bfloat16',
        'description': '最终层归一化',
        'operation_type': 'layer_normalization'
    })
    
    # 4. 语言模型头
    operators.append({
        'layer_module_operator': 'output-lm_head-linear',
        'input_shape': f'[{batch_size}, {seq_len}, {hidden_size}]',
        'input_dtype': 'torch.bfloat16',
        'output_shape': f'[{batch_size}, {seq_len}, {vocab_size}]',
        'output_dtype': 'torch.bfloat16',
        'weight_shape': f'[{hidden_size}, {vocab_size}]',
        'weight_dtype': 'torch.bfloat16',
        'description': '语言模型头 - 生成词汇表概率',
        'operation_type': 'linear_projection'
    })
    
    # 保存详细结果
    with open('详细算子分析结果.json', 'w', encoding='utf-8') as f:
        json.dump(operators, f, indent=2, ensure_ascii=False)
    
    # 生成Markdown报告
    generate_markdown_report(operators)
    
    print(f"总共分析了 {len(operators)} 个算子")
    return operators

def generate_markdown_report(operators):
    """生成Markdown格式的详细报告"""
    
    lines = []
    lines.append("# Meta-Llama-3.1-8B-Instruct 完整算子跟踪报告\n")
    lines.append(f"本报告详细分析了Meta-Llama-3.1-8B-Instruct模型的所有算子，总共 {len(operators)} 个算子。\n")
    
    # 按层级分组
    layers = {}
    for op in operators:
        layer_name = op['layer_module_operator'].split('-')[0]
        if layer_name not in layers:
            layers[layer_name] = []
        layers[layer_name].append(op)
    
    lines.append("## 算子统计概览\n")
    for layer_name in sorted(layers.keys()):
        ops = layers[layer_name]
        lines.append(f"- **{layer_name}**: {len(ops)} 个算子")
    lines.append("")
    
    lines.append("## 详细算子分析\n")
    
    for layer_name in sorted(layers.keys()):
        lines.append(f"### {layer_name}\n")
        
        for op in layers[layer_name]:
            lines.append(f"#### {op['layer_module_operator']}")
            lines.append(f"- **操作类型**: {op.get('operation_type', 'unknown')}")
            lines.append(f"- **描述**: {op['description']}")
            lines.append(f"- **输入Shape**: {op['input_shape']}")
            lines.append(f"- **输入精度**: {op['input_dtype']}")
            lines.append(f"- **输出Shape**: {op['output_shape']}")
            lines.append(f"- **输出精度**: {op['output_dtype']}")
            
            if 'weight_shape' in op:
                lines.append(f"- **权重Shape**: {op['weight_shape']}")
                lines.append(f"- **权重精度**: {op['weight_dtype']}")
            
            lines.append("")
    
    # 保存报告
    with open('Meta-Llama-3.1-8B完整算子跟踪报告.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(lines))
    
    print("已生成完整算子跟踪报告: Meta-Llama-3.1-8B完整算子跟踪报告.md")

if __name__ == "__main__":
    analyze_llama_model_structure()
