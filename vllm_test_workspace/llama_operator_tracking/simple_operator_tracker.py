import torch
import torch.nn as nn
from vllm import LLM, SamplingParams
import json
from collections import defaultdict
import sys
import os

class SimpleOperatorTracker:
    def __init__(self):
        self.operator_stats = []
        
    def track_operation(self, module_name, operation, input_info, output_info, weight_info=None):
        """跟踪算子操作 - 简化版本"""
        try:
            # 解析层级信息
            layer_info = self.parse_layer_info(module_name)
            
            entry = {
                'layer_module_operator': f"{layer_info['layer']}-{layer_info['module']}-{operation}",
                'input_info': input_info,
                'output_info': output_info,
                'module_name': module_name
            }
            
            if weight_info is not None:
                entry['weight_info'] = weight_info
            
            self.operator_stats.append(entry)
            
            print(f"[TRACKED] {entry['layer_module_operator']}")
            print(f"  输入: {entry['input_info']}")
            print(f"  输出: {entry['output_info']}")
            if 'weight_info' in entry:
                print(f"  权重: {entry['weight_info']}")
            print()
            
        except Exception as e:
            print(f"跟踪错误 in {module_name}: {e}")
    
    def parse_layer_info(self, module_name):
        """解析模块名称获取层级信息"""
        layer = "layer0"
        module = "unknown"
        
        try:
            if "layers." in module_name:
                parts = module_name.split("layers.")
                if len(parts) > 1:
                    layer_part = parts[1].split(".")[0]
                    layer = f"layer{layer_part}"
                    
                    remaining = ".".join(parts[1].split(".")[1:])
                    if "self_attn" in remaining:
                        if "q_proj" in remaining:
                            module = "self_attn-q_proj"
                        elif "k_proj" in remaining:
                            module = "self_attn-k_proj"
                        elif "v_proj" in remaining:
                            module = "self_attn-v_proj"
                        elif "o_proj" in remaining:
                            module = "self_attn-o_proj"
                        else:
                            module = "self_attn"
                    elif "mlp" in remaining:
                        if "gate_proj" in remaining:
                            module = "mlp-gate_proj"
                        elif "up_proj" in remaining:
                            module = "mlp-up_proj"
                        elif "down_proj" in remaining:
                            module = "mlp-down_proj"
                        else:
                            module = "mlp"
                    else:
                        module = remaining
            elif "embed_tokens" in module_name:
                layer = "layer0"
                module = "embed_tokens"
            elif "lm_head" in module_name:
                layer = "output"
                module = "lm_head"
            else:
                module = module_name.split(".")[-1] if "." in module_name else module_name
                
        except Exception as e:
            print(f"解析模块名错误: {e}")
            
        return {"layer": layer, "module": module}
    
    def save_results(self, filename="operator_tracking_results.json"):
        """保存结果到JSON文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.operator_stats, f, indent=2, ensure_ascii=False)
        print(f"结果已保存到: {filename}")

# 全局跟踪器
tracker = SimpleOperatorTracker()

def create_simple_hook(module_name):
    """创建简化的hook函数"""
    def hook_fn(module, input, output):
        try:
            # 获取输入信息
            input_info = "unknown"
            if isinstance(input, tuple) and len(input) > 0:
                inp = input[0]
                if hasattr(inp, 'shape') and hasattr(inp, 'dtype'):
                    input_info = f"shape={list(inp.shape)}, dtype={inp.dtype}"
            
            # 获取输出信息
            output_info = "unknown"
            if hasattr(output, 'shape') and hasattr(output, 'dtype'):
                output_info = f"shape={list(output.shape)}, dtype={output.dtype}"
            
            # 获取权重信息
            weight_info = None
            if hasattr(module, 'weight') and module.weight is not None:
                weight_info = f"shape={list(module.weight.shape)}, dtype={module.weight.dtype}"
            
            # 确定操作类型
            operation = "forward"
            if isinstance(module, nn.Linear):
                operation = "linear"
            elif isinstance(module, nn.Embedding):
                operation = "embedding"
            
            tracker.track_operation(module_name, operation, input_info, output_info, weight_info)
            
        except Exception as e:
            print(f"Hook错误 in {module_name}: {e}")
    
    return hook_fn

def register_simple_hooks(model, prefix="model"):
    """为模型注册简化的hooks"""
    hooks = []
    
    def register_recursive(module, name):
        # 只为Linear和Embedding层注册hook
        if isinstance(module, (nn.Linear, nn.Embedding)):
            hook = module.register_forward_hook(create_simple_hook(name))
            hooks.append(hook)
            print(f"已注册hook: {name} ({type(module).__name__})")
        
        # 递归处理子模块
        for child_name, child_module in module.named_children():
            child_full_name = f"{name}.{child_name}" if name else child_name
            register_recursive(child_module, child_full_name)
    
    register_recursive(model, prefix)
    return hooks

def main():
    """主函数"""
    print("=== Meta-Llama-3.1-8B-Instruct 算子跟踪开始 ===")
    
    model_name = "/home/<USER>/Meta-Llama-3.1-8B-Instruct"
    
    try:
        print("正在初始化VLLM模型...")
        llm = LLM(
            model=model_name,
            tensor_parallel_size=1,
            max_model_len=512,  # 进一步减小
            enable_prefix_caching=False,
            trust_remote_code=True,
            gpu_memory_utilization=0.5,  # 进一步降低
            enforce_eager=True,
            max_num_seqs=1
        )
        
        print("模型初始化完成")
        print(f"VLLM引擎类型: {type(llm.llm_engine)}")
        
        # 尝试获取模型实例 - 兼容不同版本
        model_instance = None
        try:
            # 尝试V1引擎路径
            if hasattr(llm.llm_engine, 'engine_core'):
                print("检测到V1引擎架构")
                # V1引擎暂时跳过hook注册，直接进行推理观察
                print("V1引擎暂不支持直接hook，将通过推理观察算子")
                model_instance = None
            else:
                # 尝试传统路径
                model_instance = llm.llm_engine.model_executor.driver_worker.model_runner.model
                print(f"模型实例类型: {type(model_instance)}")
        except Exception as e:
            print(f"获取模型实例失败: {e}")
            model_instance = None
        
        if model_instance is not None:
            # 注册hooks
            print("正在注册算子跟踪hooks...")
            hooks = register_simple_hooks(model_instance)
            print(f"已注册 {len(hooks)} 个hooks")
        else:
            print("跳过hook注册，使用推理观察")
            hooks = []
        
        # 执行推理
        print("开始执行推理...")
        prompts = ["Hello"]
        sampling_params = SamplingParams(temperature=0.0, top_p=1, max_tokens=5)
        
        outputs = llm.generate(prompts, sampling_params)
        
        for output in outputs:
            print(f"输入: {output.prompt}")
            print(f"输出: {output.outputs[0].text}")
        
        # 清理hooks
        if hooks:
            print("清理hooks...")
            for hook in hooks:
                hook.remove()
        
        # 保存结果
        print("保存跟踪结果...")
        tracker.save_results()
        
        # 生成报告
        generate_analysis_report()
        
        print("=== 算子跟踪完成 ===")
        
    except Exception as e:
        print(f"执行错误: {e}")
        import traceback
        traceback.print_exc()

def generate_analysis_report():
    """生成分析报告"""
    report_lines = []
    report_lines.append("# Meta-Llama-3.1-8B-Instruct 算子跟踪分析报告\n")
    
    if len(tracker.operator_stats) == 0:
        report_lines.append("## 跟踪结果")
        report_lines.append("由于使用了VLLM V1引擎，无法通过hook直接跟踪算子。")
        report_lines.append("但是我们可以分析模型的理论结构来推断算子信息。\n")
        
        report_lines.append("## Meta-Llama-3.1-8B 理论算子分析\n")
        report_lines.append("基于Llama架构，该模型包含以下主要算子：\n")
        
        # 理论分析
        theoretical_ops = [
            ("layer0", "embed_tokens", "embedding", "输入ID -> 嵌入向量", "[batch_size, seq_len] -> [batch_size, seq_len, 4096]", "torch.bfloat16"),
            ("layer0-31", "self_attn-q_proj", "linear", "查询投影", "[batch_size, seq_len, 4096] -> [batch_size, seq_len, 4096]", "torch.bfloat16"),
            ("layer0-31", "self_attn-k_proj", "linear", "键投影", "[batch_size, seq_len, 4096] -> [batch_size, seq_len, 1024]", "torch.bfloat16"),
            ("layer0-31", "self_attn-v_proj", "linear", "值投影", "[batch_size, seq_len, 4096] -> [batch_size, seq_len, 1024]", "torch.bfloat16"),
            ("layer0-31", "self_attn-o_proj", "linear", "输出投影", "[batch_size, seq_len, 4096] -> [batch_size, seq_len, 4096]", "torch.bfloat16"),
            ("layer0-31", "mlp-gate_proj", "linear", "门控投影", "[batch_size, seq_len, 4096] -> [batch_size, seq_len, 14336]", "torch.bfloat16"),
            ("layer0-31", "mlp-up_proj", "linear", "上采样投影", "[batch_size, seq_len, 4096] -> [batch_size, seq_len, 14336]", "torch.bfloat16"),
            ("layer0-31", "mlp-down_proj", "linear", "下采样投影", "[batch_size, seq_len, 14336] -> [batch_size, seq_len, 4096]", "torch.bfloat16"),
            ("output", "lm_head", "linear", "语言模型头", "[batch_size, seq_len, 4096] -> [batch_size, seq_len, 128256]", "torch.bfloat16"),
        ]
        
        for layer, module, op_type, description, shape_transform, dtype in theoretical_ops:
            report_lines.append(f"### {layer}-{module}-{op_type}")
            report_lines.append(f"- **描述**: {description}")
            report_lines.append(f"- **Shape变换**: {shape_transform}")
            report_lines.append(f"- **精度**: {dtype}")
            report_lines.append("")
    else:
        report_lines.append(f"## 跟踪结果\n总共跟踪到 {len(tracker.operator_stats)} 个算子操作\n")
        
        # 实际跟踪结果
        grouped = defaultdict(list)
        for stat in tracker.operator_stats:
            key = stat['layer_module_operator']
            grouped[key].append(stat)
        
        report_lines.append("## 算子详细信息\n")
        for key in sorted(grouped.keys()):
            operations = grouped[key]
            op = operations[0]
            
            report_lines.append(f"### {key}")
            report_lines.append(f"- **输入**: {op['input_info']}")
            report_lines.append(f"- **输出**: {op['output_info']}")
            
            if 'weight_info' in op:
                report_lines.append(f"- **权重**: {op['weight_info']}")
            
            report_lines.append(f"- **调用次数**: {len(operations)}")
            report_lines.append("")
    
    # 保存报告
    with open('算子跟踪分析报告.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))
    
    print("已生成分析报告: 算子跟踪分析报告.md")

if __name__ == "__main__":
    main()
