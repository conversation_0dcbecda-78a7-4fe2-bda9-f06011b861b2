import torch
import torch.nn as nn
from vllm import LLM, SamplingParams
import json
from collections import defaultdict
import sys
import os

class VLLMOperatorTracker:
    def __init__(self):
        self.operator_stats = []
        self.layer_mapping = {}
        
    def track_operation(self, module_name, operation, input_tensor, output_tensor, weight_tensor=None):
        """跟踪算子操作"""
        try:
            # 解析层级信息
            layer_info = self.parse_layer_info(module_name)
            
            entry = {
                'layer_module_operator': f"{layer_info['layer']}-{layer_info['module']}-{operation}",
                'input_shape': list(input_tensor.shape) if hasattr(input_tensor, 'shape') else str(input_tensor),
                'input_dtype': str(input_tensor.dtype) if hasattr(input_tensor, 'dtype') else 'unknown',
                'output_shape': list(output_tensor.shape) if hasattr(output_tensor, 'shape') else str(output_tensor),
                'output_dtype': str(output_tensor.dtype) if hasattr(output_tensor, 'dtype') else 'unknown',
                'module_name': module_name
            }
            
            if weight_tensor is not None:
                entry['weight_shape'] = list(weight_tensor.shape) if hasattr(weight_tensor, 'shape') else str(weight_tensor)
                entry['weight_dtype'] = str(weight_tensor.dtype) if hasattr(weight_tensor, 'dtype') else 'unknown'
            
            self.operator_stats.append(entry)
            
            print(f"[TRACKED] {entry['layer_module_operator']}")
            print(f"  输入: shape={entry['input_shape']}, dtype={entry['input_dtype']}")
            print(f"  输出: shape={entry['output_shape']}, dtype={entry['output_dtype']}")
            if 'weight_shape' in entry:
                print(f"  权重: shape={entry['weight_shape']}, dtype={entry['weight_dtype']}")
            print()
            
        except Exception as e:
            print(f"跟踪错误 in {module_name}: {e}")
    
    def parse_layer_info(self, module_name):
        """解析模块名称获取层级信息"""
        # 默认值
        layer = "layer0"
        module = "unknown"
        
        try:
            # 处理常见的模块名称模式
            if "layers." in module_name:
                # 例如: model.layers.0.self_attn.q_proj
                parts = module_name.split("layers.")
                if len(parts) > 1:
                    layer_part = parts[1].split(".")[0]
                    layer = f"layer{layer_part}"
                    
                    # 提取模块名
                    remaining = ".".join(parts[1].split(".")[1:])
                    if "self_attn" in remaining:
                        if "q_proj" in remaining:
                            module = "self_attn-q_proj"
                        elif "k_proj" in remaining:
                            module = "self_attn-k_proj"
                        elif "v_proj" in remaining:
                            module = "self_attn-v_proj"
                        elif "o_proj" in remaining:
                            module = "self_attn-o_proj"
                        else:
                            module = "self_attn"
                    elif "mlp" in remaining:
                        if "gate_proj" in remaining:
                            module = "mlp-gate_proj"
                        elif "up_proj" in remaining:
                            module = "mlp-up_proj"
                        elif "down_proj" in remaining:
                            module = "mlp-down_proj"
                        else:
                            module = "mlp"
                    else:
                        module = remaining
            elif "embed_tokens" in module_name:
                layer = "layer0"
                module = "embed_tokens"
            elif "lm_head" in module_name:
                layer = "output"
                module = "lm_head"
            else:
                # 尝试从模块名中提取更多信息
                module = module_name.split(".")[-1] if "." in module_name else module_name
                
        except Exception as e:
            print(f"解析模块名错误: {e}")
            
        return {"layer": layer, "module": module}
    
    def save_results(self, filename="operator_tracking_results.json"):
        """保存结果到JSON文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.operator_stats, f, indent=2, ensure_ascii=False)
        print(f"结果已保存到: {filename}")

# 全局跟踪器
tracker = VLLMOperatorTracker()

# 创建hook函数
def create_forward_hook(module_name):
    def hook_fn(module, input, output):
        try:
            if isinstance(module, nn.Linear):
                tracker.track_operation(
                    module_name, 
                    "linear_forward",
                    input[0] if isinstance(input, tuple) else input,
                    output,
                    module.weight
                )
            elif isinstance(module, nn.Embedding):
                tracker.track_operation(
                    module_name,
                    "embedding_forward", 
                    input[0] if isinstance(input, tuple) else input,
                    output,
                    module.weight
                )
            elif hasattr(module, 'forward'):
                tracker.track_operation(
                    module_name,
                    "forward",
                    input[0] if isinstance(input, tuple) and len(input) > 0 else input,
                    output
                )
        except Exception as e:
            print(f"Hook错误 in {module_name}: {e}")
    return hook_fn

def register_hooks(model, prefix=""):
    """为模型的所有模块注册hooks"""
    hooks = []
    
    def register_recursive(module, name):
        # 为当前模块注册hook
        if isinstance(module, (nn.Linear, nn.Embedding)) or hasattr(module, 'forward'):
            hook = module.register_forward_hook(create_forward_hook(name))
            hooks.append(hook)
            print(f"已注册hook: {name}")
        
        # 递归处理子模块
        for child_name, child_module in module.named_children():
            child_full_name = f"{name}.{child_name}" if name else child_name
            register_recursive(child_module, child_full_name)
    
    register_recursive(model, prefix)
    return hooks

def main():
    """主函数"""
    print("=== Meta-Llama-3.1-8B-Instruct 算子跟踪开始 ===")
    
    model_name = "/home/<USER>/Meta-Llama-3.1-8B-Instruct"
    
    try:
        print("正在初始化VLLM模型...")
        llm = LLM(
            model=model_name,
            tensor_parallel_size=1,
            max_model_len=1024,  # 进一步减小以避免内存问题
            enable_prefix_caching=False,  # 简化以减少复杂性
            trust_remote_code=True,
            gpu_memory_utilization=0.6,  # 降低GPU内存使用率
            enforce_eager=True,  # 强制使用eager模式以减少内存
            max_num_seqs=1  # 限制序列数量
        )
        
        print("模型初始化完成")
        
        # 获取模型实例
        model_instance = llm.llm_engine.model_executor.driver_worker.model_runner.model
        print(f"模型类型: {type(model_instance)}")
        
        # 注册hooks
        print("正在注册算子跟踪hooks...")
        hooks = register_hooks(model_instance, "model")
        print(f"已注册 {len(hooks)} 个hooks")
        
        # 执行推理以触发算子
        print("开始执行推理以触发算子...")
        prompts = ["Hello"]
        sampling_params = SamplingParams(temperature=0.0, top_p=1, max_tokens=10)
        
        outputs = llm.generate(prompts, sampling_params)
        
        for output in outputs:
            print(f"输入: {output.prompt}")
            print(f"输出: {output.outputs[0].text}")
        
        # 清理hooks
        print("清理hooks...")
        for hook in hooks:
            hook.remove()
        
        # 保存结果
        print("保存跟踪结果...")
        tracker.save_results()
        
        # 生成报告
        generate_summary_report()
        
        print("=== 算子跟踪完成 ===")
        
    except Exception as e:
        print(f"执行错误: {e}")
        import traceback
        traceback.print_exc()

def generate_summary_report():
    """生成总结报告"""
    report_lines = []
    report_lines.append("# Meta-Llama-3.1-8B-Instruct 算子跟踪报告\n")
    report_lines.append(f"总共跟踪到 {len(tracker.operator_stats)} 个算子操作\n")
    
    # 按层级-模块-算子分组
    grouped = defaultdict(list)
    for stat in tracker.operator_stats:
        key = stat['layer_module_operator']
        grouped[key].append(stat)
    
    report_lines.append("## 算子详细信息\n")
    for key in sorted(grouped.keys()):
        operations = grouped[key]
        op = operations[0]  # 取第一个作为代表
        
        report_lines.append(f"### {key}")
        report_lines.append(f"- **输入Shape**: {op['input_shape']}")
        report_lines.append(f"- **输入精度**: {op['input_dtype']}")
        report_lines.append(f"- **输出Shape**: {op['output_shape']}")
        report_lines.append(f"- **输出精度**: {op['output_dtype']}")
        
        if 'weight_shape' in op:
            report_lines.append(f"- **权重Shape**: {op['weight_shape']}")
            report_lines.append(f"- **权重精度**: {op['weight_dtype']}")
        
        report_lines.append(f"- **调用次数**: {len(operations)}")
        report_lines.append("")
    
    # 保存报告
    with open('算子跟踪总结报告.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))
    
    print("已生成总结报告: 算子跟踪总结报告.md")

if __name__ == "__main__":
    main()
