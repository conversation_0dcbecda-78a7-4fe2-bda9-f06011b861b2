import torch
import torch.nn as nn
from vllm import LLM, SamplingParams
import json
from collections import defaultdict
import numpy as np

class OperatorTracker:
    def __init__(self):
        self.operator_stats = defaultdict(list)
        self.current_layer = 0
        self.current_module = ""
        
    def track_tensor(self, tensor, op_name, layer_idx=None, module_name=""):
        """跟踪张量的shape和精度"""
        if isinstance(tensor, torch.Tensor):
            info = {
                'layer': layer_idx if layer_idx is not None else self.current_layer,
                'module': module_name or self.current_module,
                'operator': op_name,
                'shape': list(tensor.shape),
                'dtype': str(tensor.dtype),
                'device': str(tensor.device),
                'requires_grad': tensor.requires_grad
            }
            key = f"layer{info['layer']}-{info['module']}-{info['operator']}"
            self.operator_stats[key].append(info)
            print(f"[TRACK] {key}: shape={info['shape']}, dtype={info['dtype']}")
            
    def save_stats(self, filename):
        """保存统计结果到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(dict(self.operator_stats), f, indent=2, ensure_ascii=False)

# 全局跟踪器
tracker = OperatorTracker()

# Hook函数来拦截张量操作
original_linear_forward = nn.Linear.forward
original_embedding_forward = nn.Embedding.forward

def hooked_linear_forward(self, input):
    """拦截Linear层的前向传播"""
    # 获取层级信息
    layer_name = getattr(self, '_layer_name', 'unknown')
    module_name = getattr(self, '_module_name', 'linear')
    
    tracker.track_tensor(input, 'input', module_name=f"{module_name}")
    tracker.track_tensor(self.weight, 'weight', module_name=f"{module_name}")
    if self.bias is not None:
        tracker.track_tensor(self.bias, 'bias', module_name=f"{module_name}")
    
    result = original_linear_forward(self, input)
    tracker.track_tensor(result, 'output', module_name=f"{module_name}")
    return result

def hooked_embedding_forward(self, input):
    """拦截Embedding层的前向传播"""
    module_name = getattr(self, '_module_name', 'embedding')
    
    tracker.track_tensor(input, 'input', module_name=f"{module_name}")
    tracker.track_tensor(self.weight, 'weight', module_name=f"{module_name}")
    
    result = original_embedding_forward(self, input)
    tracker.track_tensor(result, 'output', module_name=f"{module_name}")
    return result

# 应用hooks
nn.Linear.forward = hooked_linear_forward
nn.Embedding.forward = hooked_embedding_forward

def name_modules_recursively(model, prefix=""):
    """递归命名模块"""
    for name, module in model.named_children():
        current_name = f"{prefix}.{name}" if prefix else name
        
        # 为模块设置名称属性
        setattr(module, '_module_name', current_name)
        
        # 如果是Linear层，额外设置层级信息
        if isinstance(module, nn.Linear):
            # 尝试从名称中提取层级信息
            if 'layers.' in current_name:
                try:
                    layer_idx = int(current_name.split('layers.')[1].split('.')[0])
                    setattr(module, '_layer_name', f"layer{layer_idx}")
                except:
                    setattr(module, '_layer_name', 'unknown')
        
        # 递归处理子模块
        name_modules_recursively(module, current_name)

def main():
    """主函数"""
    print("开始初始化Meta-Llama-3.1-8B-Instruct模型...")
    
    # 初始化模型
    model_name = "/home/<USER>/Meta-Llama-3.1-8B-Instruct"
    
    try:
        llm = LLM(
            model=model_name,
            tensor_parallel_size=1,
            max_model_len=4096,  # 减小以避免内存问题
            enable_prefix_caching=True,
            trust_remote_code=True
        )
        
        # 为模型的所有模块命名
        print("为模型模块设置跟踪名称...")
        name_modules_recursively(llm.llm_engine.model_executor.driver_worker.model_runner.model)
        
        print("模型初始化完成，开始推理...")
        
        # 准备推理
        prompts = ["Hello, how are you?"]
        sampling_params = SamplingParams(temperature=0.0, top_p=1, max_tokens=50)
        
        print("执行推理...")
        outputs = llm.generate(prompts, sampling_params)
        
        for output in outputs:
            prompt = output.prompt
            generated_text = output.outputs[0].text
            print(f"提示词: {prompt}")
            print(f"生成文本: {generated_text}")
        
        # 保存跟踪结果
        print("保存算子跟踪结果...")
        tracker.save_stats('operator_tracking_results.json')
        
        # 生成可读的统计报告
        generate_readable_report()
        
        print("任务完成！")
        
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()

def generate_readable_report():
    """生成可读的统计报告"""
    report_lines = []
    report_lines.append("# Meta-Llama-3.1-8B-Instruct 算子跟踪报告\n")
    
    # 按层级和模块组织数据
    organized_data = defaultdict(lambda: defaultdict(list))
    
    for key, stats_list in tracker.operator_stats.items():
        if stats_list:
            stat = stats_list[0]  # 取第一个统计信息作为代表
            layer = stat['layer']
            module = stat['module']
            organized_data[layer][module].append((stat['operator'], stat['shape'], stat['dtype']))
    
    # 生成报告
    for layer in sorted(organized_data.keys()):
        report_lines.append(f"## Layer {layer}\n")
        
        for module in sorted(organized_data[layer].keys()):
            report_lines.append(f"### {module}\n")
            
            for op_name, shape, dtype in organized_data[layer][module]:
                report_lines.append(f"- **算子**: {op_name}")
                report_lines.append(f"  - **Shape**: {shape}")
                report_lines.append(f"  - **精度**: {dtype}")
                report_lines.append("")
    
    # 保存报告
    with open('operator_tracking_report.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))
    
    print("生成了可读的Markdown报告: operator_tracking_report.md")

if __name__ == "__main__":
    main()
