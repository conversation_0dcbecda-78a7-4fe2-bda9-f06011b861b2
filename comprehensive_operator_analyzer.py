#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
综合算子分析器 - 专门统计算子的详细信息
包括算子的性能、shape、输入类型、内存占用等所有信息
"""

import json
import argparse
import logging
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict, Counter
import pandas as pd
import numpy as np
import csv
import re

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ComprehensiveOperatorAnalyzer:
    """综合算子分析器"""
    
    def __init__(self, json_file: str):
        """初始化分析器"""
        self.json_file = Path(json_file)
        self.trace_data = None
        self.events = []
        self.base_time = 0
        self.time_unit = "ms"
        
        # 算子分类存储
        self.cpu_operators = []
        self.cuda_kernels = []
        self.memory_operations = []
        self.user_annotations = []
        
        # 算子统计信息
        self.operator_stats = {}
        self.shape_analysis = {}
        self.performance_metrics = {}
        
    def load_and_analyze(self) -> None:
        """加载并分析数据"""
        logger.info(f"加载JSON文件: {self.json_file}")
        
        try:
            with open(self.json_file, 'r', encoding='utf-8') as f:
                self.trace_data = json.load(f)
            
            # 提取基本信息
            self.base_time = self.trace_data.get('baseTimeNanoseconds', 0)
            self.time_unit = self.trace_data.get('displayTimeUnit', 'ms')
            self.events = self.trace_data.get('traceEvents', [])
            
            logger.info(f"加载完成: {len(self.events)} 个事件")
            logger.info(f"基准时间: {self.base_time}")
            logger.info(f"时间单位: {self.time_unit}")
            
            # 分类和分析事件
            self._categorize_and_analyze_events()
            
        except Exception as e:
            logger.error(f"加载文件失败: {e}")
            raise
    
    def _categorize_and_analyze_events(self) -> None:
        """分类和分析事件"""
        logger.info("分类和分析事件...")
        
        for event in self.events:
            if event.get('ph') != 'X':  # 只处理完整事件
                continue
                
            cat = event.get('cat', '').lower()
            name = event.get('name', '')
            args = event.get('args', {})
            
            # 详细分析每个事件
            operator_info = self._extract_operator_info(event)
            
            # 按类别分类
            if cat == 'cpu_op':
                self.cpu_operators.append(operator_info)
            elif cat in ['cuda_runtime', 'kernel', 'gpu_memcpy', 'gpu_memset']:
                self.cuda_kernels.append(operator_info)
            elif 'memory' in cat or 'memcpy' in name.lower() or 'memset' in name.lower():
                self.memory_operations.append(operator_info)
            elif cat == 'user_annotation':
                self.user_annotations.append(operator_info)
        
        logger.info(f"事件分类完成:")
        logger.info(f"  CPU算子: {len(self.cpu_operators)}")
        logger.info(f"  CUDA内核: {len(self.cuda_kernels)}")
        logger.info(f"  内存操作: {len(self.memory_operations)}")
        logger.info(f"  用户注释: {len(self.user_annotations)}")
    
    def _extract_operator_info(self, event: Dict[str, Any]) -> Dict[str, Any]:
        """提取算子的详细信息"""
        args = event.get('args', {})
        
        # 基础信息
        operator_info = {
            'name': event.get('name', 'unknown'),
            'category': event.get('cat', 'unknown'),
            'duration_us': event.get('dur', 0),
            'duration_ms': event.get('dur', 0) / 1000 if event.get('dur') else 0,
            'start_time': event.get('ts', 0),
            'process_id': event.get('pid', 0),
            'thread_id': event.get('tid', 0),
            'external_id': args.get('External id', None),
            'record_function_id': args.get('Record function id', None),
            'sequence_number': args.get('Sequence number', None),
            'event_idx': args.get('Ev Idx', None)
        }
        
        # 输入信息分析
        concrete_inputs = args.get('Concrete Inputs', [])
        input_types = args.get('Input type', [])
        input_strides = args.get('Input Strides', [])
        input_dims = args.get('Input Dims', [])
        
        # 处理输入形状信息
        operator_info['input_info'] = self._analyze_input_info(
            concrete_inputs, input_types, input_strides, input_dims
        )
        
        # 算子特征分析
        operator_info['operator_features'] = self._analyze_operator_features(
            operator_info['name'], args
        )
        
        # 性能特征
        operator_info['performance_features'] = self._analyze_performance_features(
            operator_info
        )
        
        # CUDA特定信息
        if 'cuda' in operator_info['category'].lower() or 'kernel' in operator_info['category'].lower():
            operator_info['cuda_info'] = self._extract_cuda_info(args)
        
        return operator_info
    
    def _analyze_input_info(self, concrete_inputs: List, input_types: List, 
                           input_strides: List, input_dims: List) -> Dict[str, Any]:
        """分析输入信息"""
        input_info = {
            'num_inputs': len(concrete_inputs) if concrete_inputs else 0,
            'input_details': [],
            'total_elements': 0,
            'max_dimension_size': 0,
            'tensor_count': 0,
            'scalar_count': 0
        }
        
        for i in range(len(concrete_inputs) if concrete_inputs else 0):
            input_detail = {
                'index': i,
                'concrete_input': concrete_inputs[i] if i < len(concrete_inputs) else '',
                'input_type': input_types[i] if i < len(input_types) else '',
                'strides': input_strides[i] if i < len(input_strides) else [],
                'dims': input_dims[i] if i < len(input_dims) else []
            }
            
            # 分析形状信息
            if input_detail['dims']:
                dims = input_detail['dims']
                if isinstance(dims, list) and len(dims) > 0:
                    # 确保所有维度都是数字类型
                    numeric_dims = []
                    for d in dims:
                        if isinstance(d, (int, float)):
                            numeric_dims.append(d)
                        elif isinstance(d, str) and d.isdigit():
                            numeric_dims.append(int(d))
                    
                    if numeric_dims:
                        input_detail['shape'] = numeric_dims
                        input_detail['num_elements'] = np.prod(numeric_dims)
                        input_detail['num_dimensions'] = len(numeric_dims)
                        input_info['total_elements'] += input_detail['num_elements']
                        max_dim = max(numeric_dims) if numeric_dims else 0
                        input_info['max_dimension_size'] = max(input_info['max_dimension_size'], max_dim)
                        input_info['tensor_count'] += 1
                    else:
                        input_info['scalar_count'] += 1
                else:
                    input_info['scalar_count'] += 1
            else:
                input_info['scalar_count'] += 1
            
            # 分析数据类型
            input_detail['data_type'] = self._classify_data_type(input_detail['input_type'])
            
            # 估算内存占用
            input_detail['memory_estimate_bytes'] = self._estimate_memory_usage(
                input_detail.get('shape', []), input_detail['data_type']
            )
            
            input_info['input_details'].append(input_detail)
        
        return input_info
    
    def _classify_data_type(self, type_str: str) -> str:
        """分类数据类型"""
        type_str = type_str.lower()
        
        if 'float' in type_str or 'double' in type_str:
            return 'floating_point'
        elif 'int' in type_str or 'long' in type_str:
            return 'integer'
        elif 'bool' in type_str:
            return 'boolean'
        elif 'string' in type_str or 'str' in type_str:
            return 'string'
        elif 'scalar' in type_str:
            return 'scalar'
        elif type_str == '':
            return 'tensor'
        else:
            return 'unknown'
    
    def _estimate_memory_usage(self, shape: List, data_type: str) -> int:
        """估算内存使用量（字节）"""
        if not shape:
            return 0
        
        # 确保shape中所有元素都是数字
        numeric_shape = []
        for d in shape:
            if isinstance(d, (int, float)):
                numeric_shape.append(d)
            elif isinstance(d, str) and d.isdigit():
                numeric_shape.append(int(d))
        
        if not numeric_shape:
            return 0
        
        num_elements = np.prod(numeric_shape)
        
        # 估算每个元素的字节数
        bytes_per_element = {
            'floating_point': 4,  # 假设float32
            'integer': 4,  # 假设int32
            'boolean': 1,
            'scalar': 4,
            'tensor': 4,  # 默认float32
            'unknown': 4
        }.get(data_type, 4)
        
        return int(num_elements * bytes_per_element)
    
    def _analyze_operator_features(self, name: str, args: Dict[str, Any]) -> Dict[str, Any]:
        """分析算子特征"""
        features = {
            'operator_type': self._classify_operator_type(name),
            'pytorch_operator': name.startswith('aten::'),
            'custom_operator': not name.startswith('aten::') and not name.startswith('cuda'),
            'is_inplace': '_' in name and name.endswith('_'),
            'is_view_operation': any(view_op in name.lower() for view_op in ['view', 'reshape', 'transpose', 'permute']),
            'is_reduction': any(red_op in name.lower() for red_op in ['sum', 'mean', 'max', 'min', 'reduce']),
            'is_elementwise': any(elem_op in name.lower() for elem_op in ['add', 'mul', 'div', 'sub', 'relu', 'gelu', 'silu']),
            'is_linear': 'linear' in name.lower() or 'matmul' in name.lower() or 'gemm' in name.lower(),
            'is_attention': 'attention' in name.lower() or 'attn' in name.lower(),
            'is_embedding': 'embedding' in name.lower() or 'embed' in name.lower(),
            'is_normalization': any(norm in name.lower() for norm in ['norm', 'layernorm', 'batchnorm']),
            'is_activation': any(act in name.lower() for act in ['relu', 'gelu', 'silu', 'tanh', 'sigmoid']),
            'is_moe': 'moe' in name.lower(),
            'is_quantization': any(quant in name.lower() for quant in ['quant', 'int8', 'int4', 'fp8'])
        }
        
        # 添加更详细的分类
        features['detailed_category'] = self._get_detailed_category(name, features)
        
        return features
    
    def _classify_operator_type(self, name: str) -> str:
        """分类算子类型"""
        name_lower = name.lower()
        
        # 基础操作
        if any(op in name_lower for op in ['empty', 'zeros', 'ones', 'fill']):
            return 'tensor_creation'
        elif any(op in name_lower for op in ['copy', 'clone', 'detach']):
            return 'tensor_copy'
        elif any(op in name_lower for op in ['view', 'reshape', 'transpose', 'permute', 'squeeze', 'unsqueeze']):
            return 'tensor_reshape'
        elif any(op in name_lower for op in ['cat', 'stack', 'split', 'chunk']):
            return 'tensor_manipulation'
        
        # 计算操作
        elif any(op in name_lower for op in ['add', 'sub', 'mul', 'div', 'pow']):
            return 'arithmetic'
        elif any(op in name_lower for op in ['matmul', 'mm', 'bmm', 'linear', 'gemm']):
            return 'linear_algebra'
        elif any(op in name_lower for op in ['conv', 'convolution']):
            return 'convolution'
        elif any(op in name_lower for op in ['pool', 'adaptive']):
            return 'pooling'
        
        # 激活函数
        elif any(op in name_lower for op in ['relu', 'gelu', 'silu', 'tanh', 'sigmoid', 'softmax']):
            return 'activation'
        
        # 归一化
        elif any(op in name_lower for op in ['norm', 'layernorm', 'batchnorm', 'groupnorm']):
            return 'normalization'
        
        # 注意力机制
        elif any(op in name_lower for op in ['attention', 'attn', 'scaled_dot_product']):
            return 'attention'
        
        # 嵌入
        elif any(op in name_lower for op in ['embedding', 'embed']):
            return 'embedding'
        
        # 损失函数
        elif any(op in name_lower for op in ['loss', 'cross_entropy', 'mse', 'nll']):
            return 'loss'
        
        # 优化器相关
        elif any(op in name_lower for op in ['adam', 'sgd', 'optimizer']):
            return 'optimizer'
        
        # 量化相关
        elif any(op in name_lower for op in ['quant', 'dequant', 'int8', 'int4', 'fp8']):
            return 'quantization'
        
        # 专家混合
        elif 'moe' in name_lower:
            return 'mixture_of_experts'
        
        # 内存操作
        elif any(op in name_lower for op in ['memcpy', 'memset', 'malloc', 'free']):
            return 'memory_operation'
        
        else:
            return 'other'
    
    def _get_detailed_category(self, name: str, features: Dict[str, bool]) -> str:
        """获取详细分类"""
        # 基于特征组合确定详细分类
        if features['is_attention']:
            return 'attention_mechanism'
        elif features['is_linear']:
            return 'linear_transformation'
        elif features['is_moe']:
            return 'mixture_of_experts'
        elif features['is_quantization']:
            return 'quantization_operation'
        elif features['is_normalization']:
            return 'normalization_layer'
        elif features['is_activation']:
            return 'activation_function'
        elif features['is_embedding']:
            return 'embedding_layer'
        elif features['is_elementwise']:
            return 'elementwise_operation'
        elif features['is_reduction']:
            return 'reduction_operation'
        elif features['is_view_operation']:
            return 'tensor_view'
        else:
            return features.get('operator_type', 'unknown')
    
    def _analyze_performance_features(self, operator_info: Dict[str, Any]) -> Dict[str, Any]:
        """分析性能特征"""
        duration_us = operator_info['duration_us']
        input_info = operator_info['input_info']
        
        # 计算性能指标
        total_elements = input_info.get('total_elements', 0)
        memory_estimate = sum(detail.get('memory_estimate_bytes', 0) for detail in input_info.get('input_details', []))
        
        performance = {
            'duration_category': self._categorize_duration(duration_us),
            'throughput_elements_per_us': total_elements / duration_us if duration_us > 0 else 0,
            'memory_bandwidth_gb_per_s': (memory_estimate / (1024**3)) / (duration_us / 1e6) if duration_us > 0 else 0,
            'efficiency_score': self._calculate_efficiency_score(duration_us, total_elements, memory_estimate),
            'is_hotspot': duration_us > 1000,  # 超过1ms的算子被认为是热点
            'relative_cost': 'high' if duration_us > 1000 else 'medium' if duration_us > 100 else 'low'
        }
        
        # 添加CUDA特定性能分析
        cuda_info = operator_info.get('cuda_info', {})
        if cuda_info:
            cuda_performance = self._analyze_cuda_performance(cuda_info, duration_us)
            performance['cuda_performance'] = cuda_performance
        
        return performance
    
    def _analyze_cuda_performance(self, cuda_info: Dict[str, Any], duration_us: float) -> Dict[str, Any]:
        """分析CUDA性能特征"""
        cuda_perf = {
            'occupancy_percent': cuda_info.get('est_achieved_occupancy_percent', None),
            'blocks_per_sm': cuda_info.get('blocks_per_sm', None),
            'warps_per_sm': cuda_info.get('warps_per_sm', None),
            'total_threads': cuda_info.get('total_threads', None),
            'threads_per_block': cuda_info.get('threads_per_block', None),
            'shared_memory_bytes': cuda_info.get('shared_memory', None),
            'registers_per_thread': cuda_info.get('registers_per_thread', None)
        }
        
        # 分析占用率
        occupancy = cuda_perf['occupancy_percent']
        if occupancy is not None:
            if occupancy >= 75:
                cuda_perf['occupancy_level'] = 'high'
            elif occupancy >= 50:
                cuda_perf['occupancy_level'] = 'medium'
            elif occupancy >= 25:
                cuda_perf['occupancy_level'] = 'low'
            else:
                cuda_perf['occupancy_level'] = 'very_low'
        
        # 分析线程利用率
        total_threads = cuda_perf['total_threads']
        if total_threads:
            if duration_us > 0:
                cuda_perf['thread_throughput'] = total_threads / duration_us  # threads per microsecond
            
            # 判断线程配置是否合理
            threads_per_block = cuda_perf['threads_per_block']
            if threads_per_block:
                if threads_per_block % 32 == 0:  # warp对齐
                    cuda_perf['warp_aligned'] = True
                else:
                    cuda_perf['warp_aligned'] = False
                
                # 判断块大小是否合理
                if 128 <= threads_per_block <= 512:
                    cuda_perf['block_size_optimal'] = True
                else:
                    cuda_perf['block_size_optimal'] = False
        
        # 资源利用分析
        shared_mem = cuda_perf['shared_memory_bytes']
        if shared_mem is not None:
            # A100的共享内存限制
            max_shared_mem_per_block = 49152  # 48KB
            cuda_perf['shared_memory_utilization'] = shared_mem / max_shared_mem_per_block * 100
            if shared_mem > max_shared_mem_per_block * 0.8:
                cuda_perf['shared_memory_pressure'] = 'high'
            elif shared_mem > max_shared_mem_per_block * 0.5:
                cuda_perf['shared_memory_pressure'] = 'medium'
            else:
                cuda_perf['shared_memory_pressure'] = 'low'
        
        registers = cuda_perf['registers_per_thread']
        if registers is not None:
            # A100的寄存器限制
            max_registers_per_thread = 255
            cuda_perf['register_utilization'] = registers / max_registers_per_thread * 100
            if registers > 64:
                cuda_perf['register_pressure'] = 'high'
            elif registers > 32:
                cuda_perf['register_pressure'] = 'medium'
            else:
                cuda_perf['register_pressure'] = 'low'
        
        return {k: v for k, v in cuda_perf.items() if v is not None}
    
    def _categorize_duration(self, duration_us: float) -> str:
        """分类执行时间"""
        if duration_us < 1:
            return 'very_fast'
        elif duration_us < 10:
            return 'fast'
        elif duration_us < 100:
            return 'medium'
        elif duration_us < 1000:
            return 'slow'
        else:
            return 'very_slow'
    
    def _calculate_efficiency_score(self, duration_us: float, elements: int, memory_bytes: int) -> float:
        """计算效率分数"""
        if duration_us <= 0:
            return 0.0
        
        # 基于吞吐量和内存带宽的综合评分
        element_throughput = elements / duration_us if elements > 0 else 0
        memory_bandwidth = memory_bytes / duration_us if memory_bytes > 0 else 0
        
        # 归一化评分（需要根据实际硬件能力调整基准值）
        throughput_score = min(element_throughput / 1000, 1.0)  # 假设1000 elements/us为满分
        bandwidth_score = min(memory_bandwidth / 1e6, 1.0)  # 假设1MB/us为满分
        
        return (throughput_score + bandwidth_score) / 2
    
    def _extract_cuda_info(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """提取CUDA相关信息"""
        cuda_info = {
            'device_id': args.get('Device', None),
            'stream_id': args.get('Stream', None),
            'correlation_id': args.get('Correlation ID', None),
            'grid_size': args.get('Grid Size', None),
            'block_size': args.get('Block Size', None),
            'shared_memory': args.get('Shared Memory', None),
            'registers_per_thread': args.get('Registers Per Thread', None)
        }
        
        return {k: v for k, v in cuda_info.items() if v is not None}
    
    def generate_operator_statistics(self) -> Dict[str, Any]:
        """生成算子统计信息"""
        logger.info("生成算子统计信息...")
        
        all_operators = self.cpu_operators + self.cuda_kernels + self.memory_operations
        
        if not all_operators:
            return {}
        
        # 基础统计
        total_operators = len(all_operators)
        total_duration = sum(op['duration_us'] for op in all_operators)
        
        # 按名称统计
        by_name = defaultdict(list)
        for op in all_operators:
            by_name[op['name']].append(op)
        
        name_stats = {}
        for name, ops in by_name.items():
            durations = [op['duration_us'] for op in ops]
            name_stats[name] = {
                'count': len(ops),
                'total_duration_us': sum(durations),
                'avg_duration_us': np.mean(durations),
                'min_duration_us': min(durations),
                'max_duration_us': max(durations),
                'std_duration_us': np.std(durations),
                'percentage_of_total_time': sum(durations) / total_duration * 100 if total_duration > 0 else 0,
                'operator_type': ops[0]['operator_features']['operator_type'],
                'detailed_category': ops[0]['operator_features']['detailed_category']
            }
        
        # 按类别统计
        by_category = defaultdict(list)
        for op in all_operators:
            by_category[op['operator_features']['detailed_category']].append(op)
        
        category_stats = {}
        for category, ops in by_category.items():
            durations = [op['duration_us'] for op in ops]
            category_stats[category] = {
                'count': len(ops),
                'total_duration_us': sum(durations),
                'avg_duration_us': np.mean(durations),
                'percentage_of_total_time': sum(durations) / total_duration * 100 if total_duration > 0 else 0
            }
        
        # 性能热点
        hotspots = sorted(all_operators, key=lambda x: x['duration_us'], reverse=True)[:20]
        
        # 形状分析
        shape_analysis = self._analyze_tensor_shapes(all_operators)
        
        return {
            'summary': {
                'total_operators': total_operators,
                'total_duration_us': total_duration,
                'total_duration_ms': total_duration / 1000,
                'avg_duration_us': total_duration / total_operators if total_operators > 0 else 0,
                'cpu_operators': len(self.cpu_operators),
                'cuda_kernels': len(self.cuda_kernels),
                'memory_operations': len(self.memory_operations)
            },
            'by_name': name_stats,
            'by_category': category_stats,
            'hotspots': hotspots,
            'shape_analysis': shape_analysis
        }
    
    def _analyze_tensor_shapes(self, operators: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析张量形状分布"""
        shape_stats = {
            'unique_shapes': set(),
            'shape_frequency': defaultdict(int),
            'dimension_stats': defaultdict(list),
            'memory_usage_by_shape': defaultdict(int),
            'large_tensors': []  # 大于1GB的张量
        }
        
        for op in operators:
            input_info = op.get('input_info', {})
            for detail in input_info.get('input_details', []):
                shape = detail.get('shape', [])
                if shape and len(shape) > 0:
                    shape_tuple = tuple(shape)
                    shape_stats['unique_shapes'].add(shape_tuple)
                    shape_stats['shape_frequency'][shape_tuple] += 1
                    shape_stats['dimension_stats'][len(shape)].append(shape)
                    
                    memory_bytes = detail.get('memory_estimate_bytes', 0)
                    shape_stats['memory_usage_by_shape'][shape_tuple] += memory_bytes
                    
                    # 记录大张量
                    if memory_bytes > 1024**3:  # 1GB
                        shape_stats['large_tensors'].append({
                            'operator': op['name'],
                            'shape': shape,
                            'memory_gb': memory_bytes / (1024**3),
                            'duration_ms': op['duration_ms']
                        })
        
        # 转换为可序列化的格式
        shape_stats['unique_shapes'] = list(shape_stats['unique_shapes'])
        shape_stats['shape_frequency'] = dict(shape_stats['shape_frequency'])
        shape_stats['dimension_stats'] = dict(shape_stats['dimension_stats'])
        shape_stats['memory_usage_by_shape'] = dict(shape_stats['memory_usage_by_shape'])
        
        return shape_stats
    
    def save_comprehensive_analysis(self, output_dir: str) -> None:
        """保存综合分析结果"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"保存综合分析结果到: {output_path}")
        
        # 生成统计信息
        stats = self.generate_operator_statistics()
        
        # 保存主要统计报告
        self._save_main_report(output_path, stats)
        
        # 保存详细数据表格
        self._save_detailed_tables(output_path)
        
        # 保存算子详细信息
        self._save_operator_details(output_path)
        
        # 保存JSON格式的完整数据
        self._save_json_data(output_path, stats)
        
        logger.info("综合分析结果保存完成")
    
    def _save_main_report(self, output_path: Path, stats: Dict[str, Any]) -> None:
        """保存主要报告"""
        report_file = output_path / 'operator_analysis_report.md'
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 算子综合分析报告\n\n")
            
            # 概览
            summary = stats.get('summary', {})
            f.write("## 执行概览\n\n")
            f.write(f"- **总算子数量**: {summary.get('total_operators', 0)}\n")
            f.write(f"- **总执行时间**: {summary.get('total_duration_ms', 0):.2f} ms\n")
            f.write(f"- **平均执行时间**: {summary.get('avg_duration_us', 0):.2f} μs\n")
            f.write(f"- **CPU算子**: {summary.get('cpu_operators', 0)}\n")
            f.write(f"- **CUDA内核**: {summary.get('cuda_kernels', 0)}\n")
            f.write(f"- **内存操作**: {summary.get('memory_operations', 0)}\n\n")
            
            # 按类别统计
            by_category = stats.get('by_category', {})
            if by_category:
                f.write("## 按类别统计\n\n")
                f.write("| 类别 | 数量 | 总时间(ms) | 平均时间(μs) | 占比(%) |\n")
                f.write("|------|------|------------|--------------|----------|\n")
                for category, cat_stats in sorted(by_category.items(), 
                                                key=lambda x: x[1]['total_duration_us'], reverse=True):
                    f.write(f"| {category} | {cat_stats['count']} | "
                           f"{cat_stats['total_duration_us']/1000:.2f} | "
                           f"{cat_stats['avg_duration_us']:.2f} | "
                           f"{cat_stats['percentage_of_total_time']:.1f} |\n")
                f.write("\n")
            
            # 性能热点
            hotspots = stats.get('hotspots', [])
            if hotspots:
                f.write("## 性能热点 (Top 20)\n\n")
                f.write("| 排名 | 算子名称 | 类别 | 耗时(ms) | 输入数量 | 总元素数 |\n")
                f.write("|------|----------|------|----------|----------|----------|\n")
                for i, hotspot in enumerate(hotspots[:20], 1):
                    input_info = hotspot.get('input_info', {})
                    f.write(f"| {i} | {hotspot['name'][:50]}... | "
                           f"{hotspot['operator_features']['detailed_category']} | "
                           f"{hotspot['duration_ms']:.2f} | "
                           f"{input_info.get('num_inputs', 0)} | "
                           f"{input_info.get('total_elements', 0)} |\n")
                f.write("\n")
            
            # 形状分析
            shape_analysis = stats.get('shape_analysis', {})
            if shape_analysis:
                f.write("## 张量形状分析\n\n")
                f.write(f"- **唯一形状数量**: {len(shape_analysis.get('unique_shapes', []))}\n")
                f.write(f"- **大张量数量** (>1GB): {len(shape_analysis.get('large_tensors', []))}\n")
                
                # 最常见的形状
                shape_freq = shape_analysis.get('shape_frequency', {})
                if shape_freq:
                    f.write("\n### 最常见形状 (Top 10)\n\n")
                    f.write("| 形状 | 出现次数 |\n")
                    f.write("|------|----------|\n")
                    for shape, freq in sorted(shape_freq.items(), key=lambda x: x[1], reverse=True)[:10]:
                        f.write(f"| {shape} | {freq} |\n")
                
                # 大张量详情
                large_tensors = shape_analysis.get('large_tensors', [])
                if large_tensors:
                    f.write("\n### 大张量详情\n\n")
                    f.write("| 算子 | 形状 | 内存(GB) | 耗时(ms) |\n")
                    f.write("|------|------|----------|----------|\n")
                    for tensor in large_tensors[:10]:
                        f.write(f"| {tensor['operator'][:30]}... | {tensor['shape']} | "
                               f"{tensor['memory_gb']:.2f} | {tensor['duration_ms']:.2f} |\n")
            
            # 按算子名称统计（Top 20）
            by_name = stats.get('by_name', {})
            if by_name:
                f.write("\n## 按算子名称统计 (Top 20)\n\n")
                f.write("| 算子名称 | 调用次数 | 总时间(ms) | 平均时间(μs) | 占比(%) |\n")
                f.write("|----------|----------|------------|--------------|----------|\n")
                for name, name_stats in sorted(by_name.items(), 
                                             key=lambda x: x[1]['total_duration_us'], reverse=True)[:20]:
                    f.write(f"| {name[:40]}... | {name_stats['count']} | "
                           f"{name_stats['total_duration_us']/1000:.2f} | "
                           f"{name_stats['avg_duration_us']:.2f} | "
                           f"{name_stats['percentage_of_total_time']:.1f} |\n")
    
    def _save_detailed_tables(self, output_path: Path) -> None:
        """保存详细数据表格"""
        # CPU算子详情
        if self.cpu_operators:
            self._save_operator_table(output_path / 'cpu_operators_detailed.csv', self.cpu_operators)
        
        # CUDA内核详情
        if self.cuda_kernels:
            self._save_operator_table(output_path / 'cuda_kernels_detailed.csv', self.cuda_kernels)
        
        # 内存操作详情
        if self.memory_operations:
            self._save_operator_table(output_path / 'memory_operations_detailed.csv', self.memory_operations)
    
    def _save_operator_table(self, file_path: Path, operators: List[Dict[str, Any]]) -> None:
        """保存算子表格"""
        rows = []
        for op in operators:
            input_info = op.get('input_info', {})
            performance = op.get('performance_features', {})
            features = op.get('operator_features', {})
            
            row = {
                'name': op['name'],
                'category': op['category'],
                'duration_us': op['duration_us'],
                'duration_ms': op['duration_ms'],
                'start_time': op['start_time'],
                'external_id': op.get('external_id', ''),
                'num_inputs': input_info.get('num_inputs', 0),
                'total_elements': input_info.get('total_elements', 0),
                'tensor_count': input_info.get('tensor_count', 0),
                'scalar_count': input_info.get('scalar_count', 0),
                'operator_type': features.get('operator_type', ''),
                'detailed_category': features.get('detailed_category', ''),
                'is_pytorch': features.get('pytorch_operator', False),
                'is_inplace': features.get('is_inplace', False),
                'is_linear': features.get('is_linear', False),
                'is_attention': features.get('is_attention', False),
                'is_quantization': features.get('is_quantization', False),
                'duration_category': performance.get('duration_category', ''),
                'efficiency_score': performance.get('efficiency_score', 0),
                'is_hotspot': performance.get('is_hotspot', False),
                'throughput_elements_per_us': performance.get('throughput_elements_per_us', 0),
                'memory_bandwidth_gb_per_s': performance.get('memory_bandwidth_gb_per_s', 0)
            }
            rows.append(row)
        
        df = pd.DataFrame(rows)
        df.to_csv(file_path, index=False, encoding='utf-8')
    
    def _save_operator_details(self, output_path: Path) -> None:
        """保存算子详细信息"""
        all_operators = self.cpu_operators + self.cuda_kernels + self.memory_operations
        
        details_file = output_path / 'all_operators_full_details.json'
        with open(details_file, 'w', encoding='utf-8') as f:
            json.dump(all_operators, f, indent=2, ensure_ascii=False, default=str)
    
    def _save_json_data(self, output_path: Path, stats: Dict[str, Any]) -> None:
        """保存JSON格式的统计数据"""
        stats_file = output_path / 'operator_statistics.json'
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False, default=str)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="综合算子分析器 - 详细统计算子信息")
    parser.add_argument("json_file", help="SGLang profiler JSON文件路径")
    parser.add_argument("-o", "--output", default="operator_analysis_results", 
                       help="输出目录 (默认: operator_analysis_results)")
    parser.add_argument("-v", "--verbose", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 检查输入文件
    if not Path(args.json_file).exists():
        logger.error(f"文件不存在: {args.json_file}")
        sys.exit(1)
    
    try:
        # 创建分析器
        analyzer = ComprehensiveOperatorAnalyzer(args.json_file)
        analyzer.load_and_analyze()
        analyzer.save_comprehensive_analysis(args.output)
        
        print(f"\n✅ 算子分析完成！结果已保存到: {args.output}")
        print(f"📊 主要文件:")
        print(f"  - operator_analysis_report.md: 主要分析报告")
        print(f"  - cpu_operators_detailed.csv: CPU算子详情")
        print(f"  - cuda_kernels_detailed.csv: CUDA内核详情")
        print(f"  - memory_operations_detailed.csv: 内存操作详情")
        print(f"  - all_operators_full_details.json: 所有算子完整信息")
        print(f"  - operator_statistics.json: 统计数据")
        
    except Exception as e:
        logger.error(f"分析失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
