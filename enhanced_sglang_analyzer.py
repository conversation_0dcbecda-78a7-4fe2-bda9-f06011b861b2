#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强版SGLang Profiler分析脚本
基于trace_analyser框架的深度分析工具
"""

import json
import argparse
import logging
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict, Counter
import pandas as pd
import numpy as np
import csv

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AdvancedSGLangAnalyzer:
    """增强版SGLang分析器"""
    
    def __init__(self, json_file: str):
        """初始化分析器"""
        self.json_file = Path(json_file)
        self.trace_data = None
        self.events = []
        self.base_time = 0
        self.time_unit = "ms"  # 默认时间单位
        
        # 分析结果存储
        self.python_events = []
        self.cuda_events = []
        self.kernel_events = []
        self.memory_events = []
        
        # 性能指标
        self.metrics = {}
        
    def load_and_preprocess(self) -> None:
        """加载并预处理数据"""
        logger.info(f"加载JSON文件: {self.json_file}")
        
        try:
            with open(self.json_file, 'r', encoding='utf-8') as f:
                self.trace_data = json.load(f)
            
            # 提取基本信息
            self.base_time = self.trace_data.get('baseTimeNanoseconds', 0)
            self.time_unit = self.trace_data.get('displayTimeUnit', 'ms')
            self.events = self.trace_data.get('traceEvents', [])
            
            logger.info(f"加载完成: {len(self.events)} 个事件")
            logger.info(f"基准时间: {self.base_time}")
            logger.info(f"时间单位: {self.time_unit}")
            
            # 预处理和分类事件
            self._categorize_events()
            
        except Exception as e:
            logger.error(f"加载文件失败: {e}")
            raise
    
    def _categorize_events(self) -> None:
        """分类事件"""
        for event in self.events:
            if event.get('ph') != 'X':  # 只处理完整事件
                continue
                
            cat = event.get('cat', '').lower()
            name = event.get('name', '').lower()
            
            # Python函数事件
            if cat == 'python_function':
                self.python_events.append(event)
                
            # CUDA相关事件
            elif ('cuda' in cat or 'cuda' in name or 
                  'kernel' in cat or 'kernel' in name or
                  'gpu' in cat or 'gpu' in name):
                self.cuda_events.append(event)
                
                # 进一步分类内核事件
                if 'kernel' in name or cat == 'kernel':
                    self.kernel_events.append(event)
                elif any(kw in name for kw in ['memcpy', 'memset', 'malloc', 'free']):
                    self.memory_events.append(event)
        
        logger.info(f"事件分类完成:")
        logger.info(f"  Python事件: {len(self.python_events)}")
        logger.info(f"  CUDA事件: {len(self.cuda_events)}")
        logger.info(f"  内核事件: {len(self.kernel_events)}")
        logger.info(f"  内存事件: {len(self.memory_events)}")
    
    def analyze_execution_flow(self) -> Dict[str, Any]:
        """分析执行流程"""
        logger.info("分析执行流程...")
        
        # 按时间排序所有事件
        sorted_events = sorted(
            [e for e in self.events if e.get('dur', 0) > 0],
            key=lambda x: x.get('ts', 0)
        )
        
        if not sorted_events:
            return {}
        
        # 计算时间范围
        start_time = sorted_events[0]['ts']
        end_time = max(e['ts'] + e.get('dur', 0) for e in sorted_events)
        total_duration = end_time - start_time
        
        # 分析执行阶段
        phases = self._detect_execution_phases(sorted_events)
        
        # 计算并发度
        concurrency_analysis = self._analyze_concurrency(sorted_events)
        
        # 热点分析
        hotspots = self._identify_hotspots(sorted_events)
        
        return {
            'total_duration_us': total_duration,
            'total_duration_ms': total_duration / 1000,
            'event_count': len(sorted_events),
            'time_range': (start_time, end_time),
            'phases': phases,
            'concurrency': concurrency_analysis,
            'hotspots': hotspots
        }
    
    def _detect_execution_phases(self, sorted_events: List[Dict]) -> List[Dict]:
        """检测执行阶段"""
        phases = []
        
        if not sorted_events:
            return phases
        
        # 使用滑动窗口检测阶段变化
        window_size = max(50, len(sorted_events) // 20)
        
        current_phase = {
            'start_time': sorted_events[0]['ts'],
            'events': [],
            'dominant_category': None,
            'characteristics': {}
        }
        
        for i, event in enumerate(sorted_events):
            current_phase['events'].append(event)
            
            # 每个窗口分析一次
            if i > 0 and i % window_size == 0:
                # 分析当前窗口的特征
                window_events = sorted_events[max(0, i-window_size):i]
                characteristics = self._analyze_window_characteristics(window_events)
                
                # 检查是否需要开始新阶段
                if (characteristics['dominant_category'] != current_phase['dominant_category'] and 
                    current_phase['events']):
                    
                    # 结束当前阶段
                    current_phase['end_time'] = sorted_events[i-1]['ts']
                    current_phase['duration'] = current_phase['end_time'] - current_phase['start_time']
                    current_phase['characteristics'] = self._analyze_window_characteristics(current_phase['events'])
                    phases.append(current_phase)
                    
                    # 开始新阶段
                    current_phase = {
                        'start_time': event['ts'],
                        'events': [event],
                        'dominant_category': characteristics['dominant_category'],
                        'characteristics': {}
                    }
                else:
                    current_phase['dominant_category'] = characteristics['dominant_category']
        
        # 添加最后一个阶段
        if current_phase['events']:
            current_phase['end_time'] = sorted_events[-1]['ts']
            current_phase['duration'] = current_phase['end_time'] - current_phase['start_time']
            current_phase['characteristics'] = self._analyze_window_characteristics(current_phase['events'])
            phases.append(current_phase)
        
        return phases
    
    def _analyze_window_characteristics(self, events: List[Dict]) -> Dict[str, Any]:
        """分析窗口特征"""
        if not events:
            return {'dominant_category': 'unknown', 'categories': {}}
        
        # 统计事件类别
        categories = Counter(e.get('cat', 'unknown') for e in events)
        total_duration = sum(e.get('dur', 0) for e in events)
        avg_duration = total_duration / len(events)
        
        # 确定主导类别
        dominant_category = categories.most_common(1)[0][0] if categories else 'unknown'
        
        # 分析函数调用模式
        function_patterns = self._analyze_function_patterns(events)
        
        return {
            'dominant_category': dominant_category,
            'categories': dict(categories),
            'event_count': len(events),
            'total_duration': total_duration,
            'avg_duration': avg_duration,
            'function_patterns': function_patterns
        }
    
    def _analyze_function_patterns(self, events: List[Dict]) -> Dict[str, Any]:
        """分析函数调用模式"""
        python_events = [e for e in events if e.get('cat') == 'python_function']
        
        if not python_events:
            return {}
        
        # 提取函数名模式
        function_names = [e.get('name', '') for e in python_events]
        
        # 常见模式识别
        patterns = {
            'forward_pass': sum(1 for name in function_names if 'forward' in name.lower()),
            'backward_pass': sum(1 for name in function_names if 'backward' in name.lower()),
            'attention': sum(1 for name in function_names if 'attention' in name.lower()),
            'linear': sum(1 for name in function_names if 'linear' in name.lower()),
            'embedding': sum(1 for name in function_names if 'embedding' in name.lower()),
            'normalization': sum(1 for name in function_names if any(norm in name.lower() for norm in ['norm', 'layernorm'])),
            'activation': sum(1 for name in function_names if any(act in name.lower() for act in ['relu', 'gelu', 'silu'])),
            'moe': sum(1 for name in function_names if 'moe' in name.lower()),
            'quantization': sum(1 for name in function_names if any(quant in name.lower() for quant in ['quant', 'int8', 'int4']))
        }
        
        return patterns
    
    def _analyze_concurrency(self, sorted_events: List[Dict]) -> Dict[str, Any]:
        """分析并发度"""
        if not sorted_events:
            return {}
        
        # 创建时间线
        timeline = []
        for event in sorted_events:
            start_time = event['ts']
            end_time = start_time + event.get('dur', 0)
            timeline.append((start_time, 'start', event))
            timeline.append((end_time, 'end', event))
        
        # 排序时间线
        timeline.sort(key=lambda x: (x[0], x[1] == 'end'))  # 同时间点，先处理start
        
        # 计算并发度
        active_events = []
        max_concurrency = 0
        concurrency_timeline = []
        
        for timestamp, event_type, event in timeline:
            if event_type == 'start':
                active_events.append(event)
            else:
                if event in active_events:
                    active_events.remove(event)
            
            current_concurrency = len(active_events)
            max_concurrency = max(max_concurrency, current_concurrency)
            concurrency_timeline.append((timestamp, current_concurrency))
        
        # 计算平均并发度
        if len(concurrency_timeline) > 1:
            total_time = concurrency_timeline[-1][0] - concurrency_timeline[0][0]
            weighted_concurrency = 0
            
            for i in range(len(concurrency_timeline) - 1):
                curr_time, curr_concurrency = concurrency_timeline[i]
                next_time, _ = concurrency_timeline[i + 1]
                duration = next_time - curr_time
                weighted_concurrency += curr_concurrency * duration
            
            avg_concurrency = weighted_concurrency / total_time if total_time > 0 else 0
        else:
            avg_concurrency = 0
        
        return {
            'max_concurrency': max_concurrency,
            'avg_concurrency': avg_concurrency,
            'timeline_points': len(concurrency_timeline)
        }
    
    def _identify_hotspots(self, sorted_events: List[Dict]) -> List[Dict]:
        """识别性能热点"""
        # 按持续时间排序
        duration_sorted = sorted(sorted_events, key=lambda x: x.get('dur', 0), reverse=True)
        
        hotspots = []
        for event in duration_sorted[:20]:  # 取前20个最耗时的事件
            hotspot = {
                'name': event.get('name', 'unknown'),
                'category': event.get('cat', 'unknown'),
                'duration': event.get('dur', 0),
                'duration_ms': event.get('dur', 0) / 1000,
                'start_time': event.get('ts', 0),
                'args': event.get('args', {})
            }
            
            # 提取额外信息
            if hotspot['category'] == 'python_function':
                hotspot['function_type'] = self._classify_python_function(hotspot['name'])
            elif 'kernel' in hotspot['category'] or 'cuda' in hotspot['category']:
                hotspot['kernel_type'] = self._classify_kernel(hotspot['name'])
            
            hotspots.append(hotspot)
        
        return hotspots
    
    def _classify_python_function(self, name: str) -> str:
        """分类Python函数"""
        name_lower = name.lower()
        
        if 'forward' in name_lower:
            return 'forward_pass'
        elif 'backward' in name_lower:
            return 'backward_pass'
        elif 'attention' in name_lower:
            return 'attention'
        elif 'linear' in name_lower:
            return 'linear_layer'
        elif 'embedding' in name_lower:
            return 'embedding'
        elif any(norm in name_lower for norm in ['norm', 'layernorm']):
            return 'normalization'
        elif 'moe' in name_lower:
            return 'mixture_of_experts'
        elif any(quant in name_lower for quant in ['quant', 'int8', 'int4']):
            return 'quantization'
        else:
            return 'other'
    
    def _classify_kernel(self, name: str) -> str:
        """分类CUDA内核"""
        name_lower = name.lower()
        
        if 'gemm' in name_lower or 'matmul' in name_lower:
            return 'matrix_multiplication'
        elif 'attention' in name_lower:
            return 'attention_kernel'
        elif 'moe' in name_lower:
            return 'moe_kernel'
        elif 'quantiz' in name_lower or 'int8' in name_lower:
            return 'quantization_kernel'
        elif 'norm' in name_lower:
            return 'normalization_kernel'
        elif any(mem in name_lower for mem in ['memcpy', 'memset']):
            return 'memory_operation'
        else:
            return 'compute_kernel'
    
    def analyze_performance_metrics(self) -> Dict[str, Any]:
        """分析性能指标"""
        logger.info("分析性能指标...")
        
        # GPU利用率分析
        gpu_utilization = self._analyze_gpu_utilization()
        
        # 内存操作分析
        memory_analysis = self._analyze_memory_operations()
        
        # 计算密集度分析
        compute_intensity = self._analyze_compute_intensity()
        
        # 瓶颈分析
        bottlenecks = self._identify_bottlenecks()
        
        return {
            'gpu_utilization': gpu_utilization,
            'memory_analysis': memory_analysis,
            'compute_intensity': compute_intensity,
            'bottlenecks': bottlenecks
        }
    
    def _analyze_gpu_utilization(self) -> Dict[str, Any]:
        """分析GPU利用率"""
        if not self.kernel_events:
            return {'utilization': 0, 'kernel_time': 0, 'idle_time': 0}
        
        # 计算内核执行时间
        total_kernel_time = sum(e.get('dur', 0) for e in self.kernel_events)
        
        # 计算总时间跨度
        all_events = [e for e in self.events if e.get('dur', 0) > 0]
        if all_events:
            start_time = min(e['ts'] for e in all_events)
            end_time = max(e['ts'] + e.get('dur', 0) for e in all_events)
            total_time = end_time - start_time
            
            utilization = (total_kernel_time / total_time * 100) if total_time > 0 else 0
        else:
            total_time = 0
            utilization = 0
        
        return {
            'utilization_percent': utilization,
            'kernel_time_us': total_kernel_time,
            'total_time_us': total_time,
            'idle_time_us': total_time - total_kernel_time,
            'kernel_count': len(self.kernel_events)
        }
    
    def _analyze_memory_operations(self) -> Dict[str, Any]:
        """分析内存操作"""
        memory_time = sum(e.get('dur', 0) for e in self.memory_events)
        memory_count = len(self.memory_events)
        
        # 分类内存操作
        memory_types = Counter()
        for event in self.memory_events:
            name = event.get('name', '').lower()
            if 'memcpy' in name:
                memory_types['copy'] += 1
            elif 'memset' in name:
                memory_types['set'] += 1
            elif 'malloc' in name:
                memory_types['allocation'] += 1
            elif 'free' in name:
                memory_types['deallocation'] += 1
            else:
                memory_types['other'] += 1
        
        return {
            'total_memory_time_us': memory_time,
            'memory_operation_count': memory_count,
            'avg_memory_op_time_us': memory_time / memory_count if memory_count > 0 else 0,
            'memory_operation_types': dict(memory_types)
        }
    
    def _analyze_compute_intensity(self) -> Dict[str, Any]:
        """分析计算密集度"""
        kernel_time = sum(e.get('dur', 0) for e in self.kernel_events)
        memory_time = sum(e.get('dur', 0) for e in self.memory_events)
        
        total_gpu_time = kernel_time + memory_time
        compute_ratio = (kernel_time / total_gpu_time * 100) if total_gpu_time > 0 else 0
        memory_ratio = (memory_time / total_gpu_time * 100) if total_gpu_time > 0 else 0
        
        return {
            'compute_time_us': kernel_time,
            'memory_time_us': memory_time,
            'compute_ratio_percent': compute_ratio,
            'memory_ratio_percent': memory_ratio,
            'compute_to_memory_ratio': kernel_time / memory_time if memory_time > 0 else float('inf')
        }
    
    def _identify_bottlenecks(self) -> List[Dict]:
        """识别性能瓶颈"""
        bottlenecks = []
        
        # 最耗时的操作类型
        operation_times = defaultdict(float)
        operation_counts = defaultdict(int)
        
        for event in self.events:
            if event.get('dur', 0) > 0:
                category = event.get('cat', 'unknown')
                duration = event.get('dur', 0)
                
                operation_times[category] += duration
                operation_counts[category] += 1
        
        # 按总时间排序
        sorted_operations = sorted(operation_times.items(), key=lambda x: x[1], reverse=True)
        
        for op_type, total_time in sorted_operations[:10]:
            count = operation_counts[op_type]
            bottlenecks.append({
                'type': 'operation_category',
                'name': op_type,
                'total_time_us': total_time,
                'total_time_ms': total_time / 1000,
                'count': count,
                'avg_time_us': total_time / count if count > 0 else 0,
                'percentage': (total_time / sum(operation_times.values()) * 100) if operation_times else 0
            })
        
        return bottlenecks
    
    def generate_comprehensive_report(self, output_dir: str) -> None:
        """生成综合报告"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"生成综合分析报告到: {output_path}")
        
        # 执行所有分析
        execution_flow = self.analyze_execution_flow()
        performance_metrics = self.analyze_performance_metrics()
        
        # 生成主报告
        self._save_comprehensive_report(output_path, execution_flow, performance_metrics)
        
        # 生成详细的数据表格
        self._save_detailed_tables(output_path)
        
        # 生成可视化数据
        self._save_visualization_data(output_path, execution_flow)
        
        logger.info("综合报告生成完成")
    
    def _save_comprehensive_report(self, output_path: Path, execution_flow: Dict, 
                                  performance_metrics: Dict) -> None:
        """保存综合报告"""
        report_file = output_path / 'comprehensive_analysis.md'
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# SGLang Profiler 综合分析报告\\n\\n")
            
            # 执行概览
            f.write("## 执行概览\\n\\n")
            f.write(f"- **总执行时间**: {execution_flow.get('total_duration_ms', 0):.2f} ms\\n")
            f.write(f"- **总事件数**: {execution_flow.get('event_count', 0)}\\n")
            f.write(f"- **执行阶段数**: {len(execution_flow.get('phases', []))}\\n")
            f.write(f"- **最大并发度**: {execution_flow.get('concurrency', {}).get('max_concurrency', 0)}\\n")
            f.write(f"- **平均并发度**: {execution_flow.get('concurrency', {}).get('avg_concurrency', 0):.2f}\\n\\n")
            
            # GPU利用率
            gpu_util = performance_metrics.get('gpu_utilization', {})
            f.write("## GPU利用率分析\\n\\n")
            f.write(f"- **GPU利用率**: {gpu_util.get('utilization_percent', 0):.2f}%\\n")
            f.write(f"- **内核执行时间**: {gpu_util.get('kernel_time_us', 0)/1000:.2f} ms\\n")
            f.write(f"- **空闲时间**: {gpu_util.get('idle_time_us', 0)/1000:.2f} ms\\n")
            f.write(f"- **内核调用次数**: {gpu_util.get('kernel_count', 0)}\\n\\n")
            
            # 计算密集度
            compute = performance_metrics.get('compute_intensity', {})
            f.write("## 计算vs内存分析\\n\\n")
            f.write(f"- **计算时间占比**: {compute.get('compute_ratio_percent', 0):.2f}%\\n")
            f.write(f"- **内存操作占比**: {compute.get('memory_ratio_percent', 0):.2f}%\\n")
            f.write(f"- **计算/内存比率**: {compute.get('compute_to_memory_ratio', 0):.2f}\\n\\n")
            
            # 执行阶段详情
            phases = execution_flow.get('phases', [])
            if phases:
                f.write("## 执行阶段详情\\n\\n")
                for i, phase in enumerate(phases, 1):
                    characteristics = phase.get('characteristics', {})
                    f.write(f"### 阶段 {i}: {phase.get('dominant_category', 'unknown')}\\n")
                    f.write(f"- **持续时间**: {phase.get('duration', 0)/1000:.2f} ms\\n")
                    f.write(f"- **事件数量**: {len(phase.get('events', []))}\\n")
                    f.write(f"- **平均事件耗时**: {characteristics.get('avg_duration', 0)/1000:.2f} ms\\n")
                    
                    # 函数模式
                    patterns = characteristics.get('function_patterns', {})
                    if patterns:
                        f.write(f"- **主要函数模式**:\\n")
                        for pattern, count in sorted(patterns.items(), key=lambda x: x[1], reverse=True):
                            if count > 0:
                                f.write(f"  - {pattern}: {count}\\n")
                    f.write("\\n")
            
            # 性能热点
            hotspots = execution_flow.get('hotspots', [])
            if hotspots:
                f.write("## 性能热点 (Top 10)\\n\\n")
                f.write("| 排名 | 函数/内核名称 | 类别 | 耗时(ms) | 类型 |\\n")
                f.write("|------|---------------|------|----------|------|\\n")
                for i, hotspot in enumerate(hotspots[:10], 1):
                    name = hotspot['name'][:50] + "..." if len(hotspot['name']) > 50 else hotspot['name']
                    f.write(f"| {i} | {name} | {hotspot['category']} | {hotspot['duration_ms']:.2f} | {hotspot.get('function_type', hotspot.get('kernel_type', 'unknown'))} |\\n")
                f.write("\\n")
            
            # 瓶颈分析
            bottlenecks = performance_metrics.get('bottlenecks', [])
            if bottlenecks:
                f.write("## 瓶颈分析\\n\\n")
                f.write("| 操作类别 | 总耗时(ms) | 调用次数 | 平均耗时(ms) | 占比(%) |\\n")
                f.write("|----------|------------|----------|--------------|---------|\\n")
                for bottleneck in bottlenecks[:10]:
                    f.write(f"| {bottleneck['name']} | {bottleneck['total_time_ms']:.2f} | {bottleneck['count']} | {bottleneck['avg_time_us']/1000:.2f} | {bottleneck['percentage']:.1f} |\\n")
    
    def _save_detailed_tables(self, output_path: Path) -> None:
        """保存详细数据表格"""
        # Python函数详情
        if self.python_events:
            python_df = []
            for event in self.python_events:
                python_df.append({
                    'function_name': event.get('name', 'unknown'),
                    'duration_us': event.get('dur', 0),
                    'duration_ms': event.get('dur', 0) / 1000,
                    'start_time': event.get('ts', 0),
                    'thread_id': event.get('tid', 0),
                    'process_id': event.get('pid', 0)
                })
            
            df = pd.DataFrame(python_df)
            df.to_csv(output_path / 'detailed_python_functions.csv', index=False, encoding='utf-8')
        
        # 内核事件详情
        if self.kernel_events:
            kernel_df = []
            for event in self.kernel_events:
                kernel_df.append({
                    'kernel_name': event.get('name', 'unknown'),
                    'duration_us': event.get('dur', 0),
                    'duration_ms': event.get('dur', 0) / 1000,
                    'start_time': event.get('ts', 0),
                    'category': event.get('cat', 'unknown'),
                    'kernel_type': self._classify_kernel(event.get('name', ''))
                })
            
            df = pd.DataFrame(kernel_df)
            df.to_csv(output_path / 'detailed_kernel_events.csv', index=False, encoding='utf-8')
    
    def _save_visualization_data(self, output_path: Path, execution_flow: Dict) -> None:
        """保存可视化数据"""
        # 时间线数据
        timeline_data = []
        for event in self.events:
            if event.get('dur', 0) > 0:
                timeline_data.append({
                    'name': event.get('name', 'unknown'),
                    'category': event.get('cat', 'unknown'),
                    'start_time': event.get('ts', 0),
                    'duration': event.get('dur', 0),
                    'end_time': event.get('ts', 0) + event.get('dur', 0)
                })
        
        # 保存为JSON格式，便于前端可视化
        with open(output_path / 'timeline_data.json', 'w', encoding='utf-8') as f:
            json.dump(timeline_data, f, indent=2, ensure_ascii=False)
        
        # 阶段数据
        phases_data = execution_flow.get('phases', [])
        with open(output_path / 'phases_data.json', 'w', encoding='utf-8') as f:
            json.dump(phases_data, f, indent=2, ensure_ascii=False, default=str)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="增强版SGLang Profiler分析工具")
    parser.add_argument("json_file", help="SGLang profiler JSON文件路径")
    parser.add_argument("-o", "--output", default="enhanced_sglang_analysis", 
                       help="输出目录 (默认: enhanced_sglang_analysis)")
    parser.add_argument("-v", "--verbose", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 检查输入文件
    if not Path(args.json_file).exists():
        logger.error(f"文件不存在: {args.json_file}")
        sys.exit(1)
    
    try:
        # 创建增强分析器
        analyzer = AdvancedSGLangAnalyzer(args.json_file)
        analyzer.load_and_preprocess()
        analyzer.generate_comprehensive_report(args.output)
        
        print(f"\\n✅ 增强分析完成！结果已保存到: {args.output}")
        print(f"📊 主要报告文件:")
        print(f"  - comprehensive_analysis.md: 综合分析报告")
        print(f"  - detailed_python_functions.csv: Python函数详情")
        print(f"  - detailed_kernel_events.csv: 内核事件详情")
        print(f"  - timeline_data.json: 时间线可视化数据")
        print(f"  - phases_data.json: 执行阶段数据")
        
    except Exception as e:
        logger.error(f"分析失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
